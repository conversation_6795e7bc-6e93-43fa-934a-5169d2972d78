# OCPP Load Manager

A system for managing charging stations based on OCPP Smart-Charging-Profiles. This application monitors OCPP MeterValues to determine currents (A) and voltages (V) for phases L1-L3 and regulates charging power per group to ensure predefined limits per phase (kW or A) are not exceeded.

## Features

- Support for multiple groups of charge points
- Time-dependent limits (schedules)
- Automatic generation and sending of Smart-Charging-Profiles (OCPP 1.6/2.0.1)
- Next.js-based web interface for configuration and live monitoring
- Fail-safe mechanism for charge points to revert to standard profiles

## Technology Stack

- **Frontend**: Next.js 14, React 18, TypeScript, Tailwind CSS
- **Backend**: Next.js API Routes, Prisma ORM
- **Database**: PostgreSQL
- **Message Bus**: NATS
- **OCPP**: WebSocket server for OCPP 1.6 JSON

## Prerequisites

- Node.js 18+
- PostgreSQL
- NATS Server (optional, falls back to local event handling)

## Getting Started

### 1. Clone the repository

```bash
git clone https://github.com/yourusername/ocpp-load-manager.git
cd ocpp-load-manager
```

### 2. Install dependencies

```bash
npm install
```

### 3. Set up the database

Create a PostgreSQL database and update the `.env` file with your database connection string:

```
DATABASE_URL="postgresql://username:password@localhost:5432/ocpp_load_manager"
```

### 4. Run database migrations

```bash
npx prisma migrate dev
```

### 5. Start the development server

```bash
npm run dev
```

The application will be available at http://localhost:3000.

### 6. Start the OCPP server

Visit http://localhost:3000/dashboard and click the "Start Server" button to start the OCPP WebSocket server.

## Project Structure

- `src/app`: Next.js App Router pages and API routes
- `src/components`: React components
- `src/lib`: Core functionality
  - `core`: Load management algorithms
  - `db`: Database connection
  - `ocpp`: OCPP WebSocket server
  - `scheduler`: Time-based scheduling
  - `messageBus`: Event handling
  - `websocket`: WebSocket server for live updates
  - `utils`: Utility functions

## API Endpoints

- `/api/groups`: Manage charging groups
- `/api/chargepoints`: Manage charge points
- `/api/limits`: Configure power limits
- `/api/schedules`: Manage time schedules
- `/api/server`: Control server components

## OCPP Connection

Charge points should connect to the WebSocket server at:

```
ws://your-server-ip:9000/ocpp/{chargePointId}
```

## Development

### Running tests

```bash
npm test
```

### Building for production

```bash
npm run build
```

## License

[MIT](LICENSE)
