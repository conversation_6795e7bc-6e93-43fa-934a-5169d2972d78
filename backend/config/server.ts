import cronTask from "./cron.task";


export default ({ env }) => {
  // Definiere den Upload-Pfad
  const uploadRootDir = env('UPLOAD_ROOT_DIR', './public/uploads');
  const uploadBaseUrl = env('UPLOAD_BASE_URL', '/uploads');

  return {
  host: env('HOST', '0.0.0.0'),
  port: env.int('PORT', 1337),
  app: {
    keys: env.array('APP_KEYS', ['myKeyA', 'myKeyB']),
  },
  cron: {
    enable: true,
    task: cronTask,
    // Force cron jobs to run in development mode
    options: {
      timezone: 'Europe/Berlin',
    },
  },
  publicURL: env('PUBLIC_URL', 'https://jr.payter.eulektro.de'),
  frontendURL: env('FRONTEND_URL', 'https://localhost:3000'),

    invoiceFolder: env('INVOICE_FOLDER', '/var/www/vhosts/terminal.eulektro.de/invoices'),
    upload: {
      config: {
        provider: 'local', // Verwende den Standard-Provider von Strapi
        providerOptions: {
          sizeLimit: 100 * 1024 * 1024, // 100MB in Bytes
          localServer: {
            maxage: 300000 // 5 Minuten Cache-Zeit
          }
        },
        actionOptions: {
          upload: {},
          uploadStream: {},
          delete: {},
        },
      },
    }
  };
};
