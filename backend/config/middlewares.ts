export default [
  'strapi::logger',
  'strapi::errors',
  'strapi::security',
  {
    name: 'strapi::cors',
    config: {
      headers: '*',
      origin: [
        'http://localhost:3000',
        'http://localhost:1337',
        'https://terminal.eulektro.de',
        'https://api.terminal.eulektro.de'
      ].filter(Boolean)
    }
  },
  'strapi::poweredBy',
  'strapi::query',
  'strapi::body',
  'strapi::session',
  'strapi::favicon',
  'strapi::public',

  // Terminal-Logger-Middleware für das automatische Protokollieren von Terminal-Kommunikation
  {
    name: 'api::terminal-message-log.terminal-logger',
    config: {
      // <PERSON><PERSON> von Endpunkten, die überwacht werden sollen
      endpoints: [
         '/api/terminals',      // Terminal-Verwaltung
         '/api/payter',         // Payter-API
         '/api/terminal-events', // Terminal-Events
         '/payment-sessions'    // Zahlungssessions
      ]
    }
  },

  // Terminal-Log-Filter wurde in den Controller integriert
  // {
  //   name: 'api::terminal-message-log.terminal-log-filter',
  //   config: {}
  // }
];
