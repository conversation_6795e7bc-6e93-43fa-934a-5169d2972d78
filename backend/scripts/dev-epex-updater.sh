#!/bin/bash

# Development EPEX Price Updater
# Simulates cron job behavior for development environment
# Usage: ./scripts/dev-epex-updater.sh [interval_minutes]

INTERVAL_MINUTES=${1:-10}  # Default: 10 minutes
BACKEND_URL="http://localhost:1337"

echo "🔄 Starting EPEX Development Updater"
echo "📊 Update interval: $INTERVAL_MINUTES minutes"
echo "🌐 Backend URL: $BACKEND_URL"
echo "⏰ Started at: $(date)"
echo "=================================="

# Function to check EPEX status via standard API
check_epex_status() {
    echo "📈 $(date): Checking EPEX status..."

    # Use the standard EPEX API to check for data
    response=$(curl -s -X GET "$BACKEND_URL/api/epexes?pagination[pageSize]=5&sort[0]=timestamp:desc" 2>/dev/null)

    if [ $? -eq 0 ]; then
        # Count records in response
        count=$(echo "$response" | jq '.data | length' 2>/dev/null || echo "0")
        echo "📊 Found $count EPEX records in database"

        if [ "$count" -gt 0 ]; then
            latest=$(echo "$response" | jq '.data[0].timestamp' 2>/dev/null || echo "unknown")
            echo "📅 Latest record: $latest"
        fi
    else
        echo "❌ Status check failed - Backend not reachable"
    fi
    echo ""
}

# Note: Manual EPEX fetching is handled by cron jobs
# The fetchAndStorePrices service method runs automatically

# Initial status check
check_epex_status

# Main loop - just monitor status (EPEX fetching is handled by cron jobs)
echo "🔄 Starting monitoring loop (Ctrl+C to stop)..."
echo "ℹ️  Note: EPEX price fetching is handled automatically by cron jobs"
while true; do
    sleep_seconds=$((INTERVAL_MINUTES * 60))
    echo "⏳ Waiting $INTERVAL_MINUTES minutes until next status check..."
    sleep $sleep_seconds

    check_epex_status
done
