/**
 * cron-job-execution controller
 */

import { factories } from '@strapi/strapi';

export default factories.createCoreController('api::cron-job-execution.cron-job-execution', ({ strapi }) => ({
  /**
   * Get all cron jobs with their status
   */
  async getAllJobsWithStatus(ctx) {
    try {
      // Check if user has admin role
      const user = ctx.state.user;
      if (!user || user.role?.type !== 'administrator') {
        return ctx.forbidden('Access denied. Administrator role required.');
      }

      const cronJobService = strapi.service('api::cron-job-execution.cron-job-execution');
      const jobsWithStatus = await cronJobService.getAllJobsWithStatus();

      // Add job configuration information
      const jobsWithConfig = jobsWithStatus.map(job => {
        let schedule = 'Unknown';
        let description = '';

        switch (job.jobName) {
          case 'epexPriceFetching':
            schedule = 'Every 10 minutes';
            description = 'Fetches electricity prices from aWATTar API';
            break;
          case 'epexBackfill':
            schedule = 'Manual only';
            description = 'Backfills missing EPEX price data (up to 90 days)';
            break;
          case 'myJob':
            schedule = 'Every Monday at 1:00 AM';
            description = 'Example job';
            break;
        }

        return {
          ...job,
          schedule,
          description,
        };
      });

      ctx.body = jobsWithConfig;
    } catch (error) {
      console.error('Error getting jobs with status:', error);
      ctx.badRequest('Failed to get jobs with status');
    }
  },

  /**
   * Get execution history for a specific job
   */
  async getExecutionHistory(ctx) {
    try {
      // Check if user has admin role
      const user = ctx.state.user;
      if (!user || user.role?.type !== 'administrator') {
        return ctx.forbidden('Access denied. Administrator role required.');
      }

      const { jobName } = ctx.params;
      const { limit = '50' } = ctx.query;

      if (!jobName) {
        return ctx.badRequest('Job name is required');
      }

      const cronJobService = strapi.service('api::cron-job-execution.cron-job-execution');
      const limitNumber = parseInt(typeof limit === 'string' ? limit : '50', 10);
      const history = await cronJobService.getExecutionHistory(jobName, limitNumber);

      ctx.body = history;
    } catch (error) {
      console.error('Error getting execution history:', error);
      ctx.badRequest('Failed to get execution history');
    }
  },

  /**
   * Manually trigger a cron job
   */
  async triggerJob(ctx) {
    try {
      // Check if user has admin role
      const user = ctx.state.user;
      if (!user || user.role?.type !== 'administrator') {
        return ctx.forbidden('Access denied. Administrator role required.');
      }

      const { jobName } = ctx.params;

      if (!jobName) {
        return ctx.badRequest('Job name is required');
      }

      // Auto-cleanup stuck executions before triggering new job
      try {
        console.log('Auto-cleaning stuck executions...');

        // Find all executions that are still RUNNING but older than 5 minutes
        const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);

        const stuckExecutions = await strapi.entityService.findMany('api::cron-job-execution.cron-job-execution', {
          filters: {
            status: 'RUNNING',
            startTime: {
              $lt: fiveMinutesAgo.toISOString(),
            },
          },
        });

        console.log(`Found ${stuckExecutions.length} stuck executions to clean up`);

        for (const execution of stuckExecutions) {
          try {
            const startTime = new Date(execution.startTime);
            const endTime = new Date();
            const duration = endTime.getTime() - startTime.getTime();

            await strapi.entityService.update('api::cron-job-execution.cron-job-execution', execution.id, {
              data: {
                status: 'CANCELLED',
                endTime,
                duration,
                summary: 'Execution was stuck and automatically cancelled during cleanup',
                errorMessage: 'Execution timeout - automatically cancelled',
              },
            });

            console.log(`Auto-cleaned stuck execution: ${execution.id} (${execution.jobName})`);
          } catch (updateError) {
            console.error(`Failed to clean up execution ${execution.id}:`, updateError);
          }
        }
      } catch (cleanupError) {
        console.error('Error during auto-cleanup:', cleanupError);
      }

      const cronJobService = strapi.service('api::cron-job-execution.cron-job-execution');
      const result = await cronJobService.triggerJob(jobName, user.documentId);

      ctx.body = {
        message: `Job ${jobName} triggered successfully`,
        execution: result.execution,
        result: result.result,
      };
    } catch (error) {
      console.error('Error triggering job:', error);
      ctx.badRequest(`Failed to trigger job: ${error.message}`);
    }
  },

  /**
   * Get available cron jobs
   */
  async getAvailableJobs(ctx) {
    try {
      // Check if user has admin role
      const user = ctx.state.user;
      if (!user || user.role?.type !== 'administrator') {
        return ctx.forbidden('Access denied. Administrator role required.');
      }

      const availableJobs = [
        {
          name: 'epexPriceFetching',
          displayName: 'EPEX Price Fetching',
          description: 'Fetches electricity prices from aWATTar API',
          schedule: 'Every 10 minutes',
          enabled: true,
        },
        {
          name: 'epexBackfill',
          displayName: 'EPEX Data Backfill',
          description: 'Backfills missing EPEX price data (up to 90 days)',
          schedule: 'Manual only',
          enabled: true,
        },
        {
          name: 'epexHistoricalDataLoad',
          displayName: 'EPEX Historical Data Load',
          description: 'Loads all EPEX price data for current year and previous year (2024-2025). ⚠️ This may take a long time and uses many API calls.',
          schedule: 'Manual only',
          enabled: true,
        },
      ];

      ctx.body = availableJobs;
    } catch (error) {
      console.error('Error getting available jobs:', error);
      ctx.badRequest('Failed to get available jobs');
    }
  },

}));
