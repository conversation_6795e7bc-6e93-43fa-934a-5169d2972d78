
import { factories } from '@strapi/strapi';

// Standard REST API Routen für Invoices
const coreRouter = factories.createCoreRouter('api::invoice.invoice', {
    config: {
        find: {
            auth: false,
            policies: [],
            middlewares: []
        },
        findOne: {
            auth: false,
            policies: [],
            middlewares: []
        },
        create: {
            auth: false,
            policies: [],
            middlewares: []
        },
        update: {
            auth: false,
            policies: [],
            middlewares: []
        },
        delete: {
            auth: false,
            policies: [],
            middlewares: []
        }
    }
});

// Benutzerdefinierte Routen
const customRoutes = {
    routes: [
        {
            method: 'POST',
            path: '/invoices/testendpunk',
            handler: 'invoice.testendpunkt',
            config: {
                auth: false, // Authentifizierung aktiviert
                policies: [],
                middlewares: []
            }
        },
        {
            method: 'POST',
            path: '/invoices/:id/generate-pdf',
            handler: 'invoice.generatePdf',
            config: {
                auth: false, // Authentifizierung aktiviert
                policies: [],
                middlewares: []
            }
        },
        {
            method: 'GET',
            path: '/invoices/:id/download-pdf',
            handler: 'invoice.downloadPdf',
            config: {
                auth: false, // Authentifizierung aktiviert
                policies: [],
                middlewares: []
            }
        },
        {
            method: 'GET',
            path: '/invoices/missing-pdfs',
            handler: 'invoice.findMissingPdfs',
            config: {
                auth: false, // Authentifizierung aktiviert
                policies: [],
                middlewares: []
            }
        },

        // Weitere benutzerdefinierte Routen hier...
    ]
};