import fs from "fs";
import { access } from "fs/promises";
import PDFDocument from "pdfkit";
import { z } from "zod";
import * as path from "path";
import { DateTime } from "luxon";

// Angepasstes Schema basierend auf dem Strapi Invoice-Modell
export const InvoiceSchema = z.object({
  documentId: z.string(),
  invoice_number: z.string().optional(),
  invoice_date: z.date().optional(),
  sum_gross: z.number(),
  sum_net: z.number(),
  vat_amount: z.number(),
  vat_percentage: z.number().default(19),
  period_start_utc: z.string(),
  period_end_utc: z.string(),
  invoice_status: z.enum(["DRAFT", "INMUTABLE_WRITTEN", "PAID"]),
  kindOfInvoice: z.enum(["INVOICE", "CREDIT", "STORNO", "CREDIT_STORNO"]),
  invoice_positions: z.array(
    z.object({
      pos: z.number(),
      title: z.string(),
      description: z.string().nullable().optional(),
      unit: z.string(),
      unit_price: z.number(),
      amount: z.number(),
      tax_rate: z.number(),
      sum_net: z.number(),
      sum_tax: z.number(),
      sum_gross: z.number(),
    })
  ),
  mandant: z.object({
    documentId: z.string(),
    name: z.string(),
    logo: z.object({url: z.string()}).optional()}),
    ocpi_cdr: z.object({
    documentId: z.string(),
    cdrId: z.string(),
    startDateTime: z.string().optional(),
    endDateTime: z.string().optional(),
  }).optional(),
  ocpi_session: z.object({
    documentId: z.string(),
    sessionId: z.string(),
    startTime: z.string().optional(),
    endTime: z.string().optional(),
  }).optional(),
});

export type InvoiceType = z.infer<typeof InvoiceSchema>;

export class InvoicePdf {
  invoice: InvoiceType;
  #pdfPath: string;
  regularFontPath = "public/font/OpenSans/OpenSans-Regular.ttf";
  boldFontPath = "public/font/OpenSans/OpenSans-Bold.ttf";
  hasRegularFont = false;
  hasBoldFont = false;

  constructor(invoice: any) {
    if (!invoice) {
      throw new Error("No invoice found");
    }
    const validateInvoice = InvoiceSchema.safeParse(invoice);
    if (!validateInvoice.success) {
      throw new Error(`Invalid invoice: ${validateInvoice.error.message}`);
    }
    this.invoice = validateInvoice.data;
    this.#pdfPath = "";

    // Überprüfe, ob die Schriftarten existieren
    this.hasRegularFont = fs.existsSync(this.regularFontPath);
    this.hasBoldFont = fs.existsSync(this.boldFontPath);

    console.log(`Regular font exists: ${this.hasRegularFont}`);
    console.log(`Bold font exists: ${this.hasBoldFont}`);
  }

  // Hilfsfunktion zum sicheren Setzen der Schriftart
  setFont(doc: typeof PDFDocument, isBold: boolean = false) {
    try {
      if (isBold && this.hasBoldFont) {
        doc.font(this.boldFontPath);
      } else if (!isBold && this.hasRegularFont) {
        doc.font(this.regularFontPath);
      }
      // Wenn die Schriftart nicht existiert, wird die Standard-Schriftart verwendet
    } catch (error) {
      console.error(`Fehler beim Setzen der Schriftart: ${error.message}`);
    }
    return doc;
  }

  async init() {
    console.log('InvoicePdf.init() called');
    const pdfPath = await this.createPdf();
    console.log(`InvoicePdf.init() completed, pdfPath = ${pdfPath}`);
    console.log(`InvoicePdf.filepath = ${this.filepath}`);
  }

  get filepath(): string {
    return this.#pdfPath;
  }

  async fileExists(filename: string) {
    try {
      await access(filename);
      return true;
    } catch (err: unknown) {
      throw err;
    }
  }

  getCustomer() {
    return "<EMAIL>";
  }

  async getMandant() {
    const mandant = await strapi.documents('api::mandant.mandant').findOne({
      documentId: this.invoice.mandant?.documentId,
      populate: {
        invoice_templates: true,
        logo: true
      }
    });

    if (!mandant) {
      throw new Error("No Mandant found");
    }
    return mandant;
  }

  async getInvoiceTemplate() {
    // Aktuelles Datum für die Suche nach dem gültigen Template (nur das Datum ohne Zeit)
    const today = new Date().toISOString().split('T')[0]; // z.B. '2024-06-07'

    console.log(`Suche nach Template für Mandant ${this.invoice.mandant?.documentId} und Datum ${today}`);

    // Template aus der Datenbank holen, das für das aktuelle Datum gültig ist
    const template = await strapi.documents('api::invoice-template.invoice-template').findFirst({
      filters: {
        mandant: {
          documentId: this.invoice.mandant?.documentId
        },
        from: {
          $lte: today
        },
        to: {
          $gte: today
        }
      }
    });

    if (!template) {

      console.warn(`Kein gültiges Invoice-Template für Mandant ${this.invoice.mandant?.documentId} gefunden.`);
      return null;
    }
    console.log(`Gültiges Invoice-Template gefunden: ${template.invoice_prefix}`);
    return template;
  }

  //todo Zahlungsreferenz
  get Subject(): string {
    return `Ladevorgang ${this.invoice.ocpi_cdr?.cdrId ?? ''}`
  }

  get Subtitle(): string {
    const startDate = new Date(this.invoice.period_start_utc).toLocaleString('de-DE', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
    const endDate = new Date(this.invoice.period_end_utc).toLocaleString('de-DE', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });

    return `am ${startDate} Uhr bis ${endDate} Uhr`;
  }

  get InvoiceNumber(): string {
    return this.invoice.invoice_number || "";
  }

  get Customernumber(): string {
    return "";
  }

  get ServicePeriod(): string {
    if (!this.invoice) {
      throw new Error("No invoice found");
    }

    const startDate = new Date(this.invoice.period_start_utc).toLocaleString('de-DE', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });
    const endDate = new Date(this.invoice.period_end_utc).toLocaleString('de-DE', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    });

    return `${startDate} - ${endDate}`;
  }

  get InvoiceDate(): string {
    if (this.invoice.invoice_date) {
      return this.formatDate(new Date(this.invoice.invoice_date));
    }
    return this.formatDate(new Date());
  }

  padTo2Digits = (num: number) => {
    return num.toString().padStart(2, "0");
  };

  formatDate = (date: Date) => {
    return [
      this.padTo2Digits(date.getDate()),
      this.padTo2Digits(date.getMonth() + 1),
      date.getFullYear(),
    ].join(".");
  };

  drawTableHeader(doc: typeof PDFDocument, y: number) {
    doc.y = y;
    doc.rect(48, y, 517, 18).fillOpacity(0.25).fill("grey");
    doc.y += 2;
    doc.fillColor("black").fillOpacity(1);
    this.setFont(doc, true);
    doc.text("Pos.", 50, doc.y, {});
    doc.moveUp();
    doc.text("Beschreibung", 80, doc.y, {});
    doc.moveUp();
    doc.text("Menge", 290, doc.y, {});
    doc.moveUp();
    doc.text("Einheit", 340, doc.y, {});
    doc.moveUp();
    doc.text("MwSt.", 390, doc.y, {});
    doc.moveUp();
    doc.text("Einzelpreis", 435, doc.y, { width: 55 });
    doc.moveUp();
    doc.text("Gesamtpreis", 499, doc.y, { lineBreak: false });
  }

  async drawFooterAndLogo(doc: typeof PDFDocument) {
    // Hole das aktuelle Template und den Mandanten
    const template = await this.getInvoiceTemplate();
    const mandant = await this.getMandant();

    // Firmenname aus dem Template oder Mandanten
    const companyName = template?.companyName || mandant?.name

    // Adressdaten aus dem Template
    const street = template?.street
    const houseNr = template?.houseNr
    const zip = template?.zip
    const city = template?.city
    const country = template?.country
    const vatId = template?.vatId

    // Logo aus dem Mandanten laden
    try {
      if (mandant?.logo) {
        console.log(`Logo gefunden im Mandanten:`, mandant.logo);

        // Vollständigen Pfad zum Logo erstellen
        const publicDir = './public'; // Standard-Verzeichnis für öffentliche Dateien in Strapi
        const logoUrl = mandant.logo.url || (typeof mandant.logo === 'string' ? mandant.logo : null);

        // Wenn keine URL vorhanden ist, Fehler werfen
        if (!logoUrl) {
          console.error(`Keine Logo-URL gefunden im Mandanten-Objekt`);
          throw new Error(`Keine Logo-URL gefunden im Mandanten-Objekt`);
        }

        // Wenn die URL mit "/uploads/" beginnt, ist es ein Strapi-Upload
        if (logoUrl.startsWith('/uploads/')) {
          const logoPath = path.join(publicDir, logoUrl);

          console.log(`Versuche Logo zu laden von: ${logoPath}`);

          if (fs.existsSync(logoPath)) {
            // Logo oben rechts platzieren
            // Zuerst die Bildgröße ermitteln
            const logoSize = doc.openImage(logoPath);

            // Maximale Größe für das Logo festlegen
            const maxWidth = 150; // maximale Breite in Punkten
            const maxHeight = 80; // maximale Höhe in Punkten

            // Skalierungsfaktor berechnen, um das Bild anzupassen
            let scale = 1;
            if (logoSize.width > maxWidth || logoSize.height > maxHeight) {
              const scaleWidth = maxWidth / logoSize.width;
              const scaleHeight = maxHeight / logoSize.height;
              scale = Math.min(scaleWidth, scaleHeight); // kleineren Wert nehmen, um Verzerrung zu vermeiden
            }

            // Neue Dimensionen berechnen
            const width = logoSize.width * scale;
            const height = logoSize.height * scale;

            // Position berechnen (oben rechts mit etwas Abstand)
            const x = 50 // 560 ist die Seitenbreite, 20 ist der Abstand vom Rand
            const y = 20; // Abstand von oben

            // Logo einfügen
            doc.image(logoPath, x, y, {
              width: width,
              height: height
            });

            console.log(`Logo erfolgreich geladen und eingefügt (${width}x${height} bei ${x},${y})`);
          } else {
            console.error(`Logo-Datei existiert nicht: ${logoPath}`);
            throw new Error(`Logo-Datei existiert nicht: ${logoPath}`);
          }
        } else {
          // Externe URL oder anderer Pfad
          console.error(`Logo-URL wird nicht unterstützt: ${logoUrl}`);
          throw new Error(`Logo-URL wird nicht unterstützt: ${logoUrl}`);
        }
      } else {
        // Fallback auf Standard-Logo
        const defaultLogoPath = path.join('./public', 'logo/EULEKTRO_21697c_R33_G105_B124.png');

        if (fs.existsSync(defaultLogoPath)) {
          console.log(`Standard-Logo gefunden: ${defaultLogoPath}`);

          // Logo oben rechts platzieren
          // Zuerst die Bildgröße ermitteln
          const logoSize = doc.openImage(defaultLogoPath);

          // Maximale Größe für das Logo festlegen
          const maxWidth = 150; // maximale Breite in Punkten
          const maxHeight = 80; // maximale Höhe in Punkten

          // Skalierungsfaktor berechnen, um das Bild anzupassen
          let scale = 1;
          if (logoSize.width > maxWidth || logoSize.height > maxHeight) {
            const scaleWidth = maxWidth / logoSize.width;
            const scaleHeight = maxHeight / logoSize.height;
            scale = Math.min(scaleWidth, scaleHeight); // kleineren Wert nehmen, um Verzerrung zu vermeiden
          }

          // Neue Dimensionen berechnen
          const width = logoSize.width * scale;
          const height = logoSize.height * scale;

          // Position berechnen (oben rechts mit etwas Abstand)
          const x = 560 - width - 20; // 560 ist die Seitenbreite, 20 ist der Abstand vom Rand
          const y = 20; // Abstand von oben

          // Logo einfügen
          doc.image(defaultLogoPath, x, y, {
            width: width,
            height: height
          });

          console.log(`Standard-Logo erfolgreich geladen und eingefügt (${width}x${height} bei ${x},${y})`);
        } else {
          throw new Error(`Standard-Logo nicht gefunden: ${defaultLogoPath}`);
        }
      }
    } catch (error) {
      console.error(`Fehler beim Einfügen des Logos: ${error.message}`);
      // Platzhalter für das Logo oben rechts
      const width = 150;
      const height = 80;
      const x = 560 - width - 20; // 560 ist die Seitenbreite, 20 ist der Abstand vom Rand
      const y = 20; // Abstand von oben

      // Rechteck als Platzhalter zeichnen
      doc.rect(x, y, width, height).stroke();

      // Firmenname in den Platzhalter schreiben
      doc.fontSize(12).text(companyName, x, y + height/2 - 6, { width: width, align: "center" });
    }

    // create Footer
    doc
      .moveTo(50, 785)
      .lineTo(560, 785)
      .lineWidth(0.5)
      .strokeColor("grey")
      .stroke();
    doc.fillColor("black").fillOpacity(1);
    doc.moveDown();
    this.setFont(doc, true).fontSize(6);
    doc.text(companyName, 50, 790, { lineBreak: false });
    doc.text("Kontakt", 200, 790, { lineBreak: false });
    if(template.bankName && template.bankIBAN && template.bankBIC) {
      doc.text("Bankverbindung", 350, 790, { lineBreak: false });
    }
    this.setFont(doc);

    doc.moveDown();

    // Adressdaten aus dem Template verwenden
    doc.text(`${street} ${houseNr}`, 50, 798, { lineBreak: false });
    doc.text(`${zip} ${city}`, 50, 806, { lineBreak: false });

    // USt-ID nur anzeigen, wenn vorhanden
    if (vatId) {
      doc.text(`USt-ID: ${vatId}`, 50, 814, { lineBreak: false });
    }

    // Geschäftsführer nur anzeigen, wenn vorhanden
    if (template?.ceo) {
      doc.text(`GF: ${template.ceo}`, 50, 822, { lineBreak: false });
    }

    // Handelsregisternummer nur anzeigen, wenn vorhanden
    if (template?.commercialRegister) {
      doc.text(`${template.commercialRegister}`, 50, 830, { lineBreak: false });
    }

    // Kontaktdaten nur anzeigen, wenn vorhanden
    let contactY = 798;
    if (template?.email) {
      doc.text(`E-Mail: ${template.email}`, 200, contactY, { lineBreak: false });
      contactY += 8;
    }
    if (template?.website) {
      doc.text(`Website: ${template.website}`, 200, contactY, { lineBreak: false });
    }

    if(template.bankName && template.bankIBAN && template.bankBIC) {

      // Bankverbindung nur anzeigen, wenn vorhanden
      let bankY = 798;
      if (template?.bankName) {
        doc.text(`Bank: ${template.bankName}`, 350, bankY, { lineBreak: false });
        bankY += 8;
      }
      if (template?.bankIBAN) {
        doc.text(`IBAN: ${template.bankIBAN}`, 350, bankY, { lineBreak: false });
        bankY += 8;
      }
      if (template?.bankBIC) {
        doc.text(`BIC: ${template.bankBIC}`, 350, bankY, { lineBreak: false });
      }
    }
  }

  writePageNumber = (doc: typeof PDFDocument) => {
    //Global Edits to All Pages (Header/Footer, etc)
    const pages = doc.bufferedPageRange();
    for (let i = 0; i < pages.count; i++) {
      doc.switchToPage(i);

      //Footer: Add page number
      const oldBottomMargin = doc.page.margins.bottom;
      doc.page.margins.bottom = 0; //Dumb: Have to remove bottom margin in order to write into it
      doc.fontSize(8);
      doc.text(`Seite: ${i + 1} von ${pages.count}`, 500, 790, {
        lineBreak: false,
      });
      doc.page.margins.bottom = oldBottomMargin; // ReProtect bottom margin
    }
  };

  async createPdf() {
    console.log('InvoicePdf.createPdf() called');
    try {
      // Erstelle ein neues PDF-Dokument mit Standard-Schriftart
      const doc = new PDFDocument({
        bufferPages: true,
        size: "A4",
      });
      console.log('PDFDocument created successfully');
    await this.drawFooterAndLogo(doc);

    // Logo wird bereits in drawFooterAndLogo hinzugefügt

    // Füge den Rechnungstitel hinzu
    this.setFont(doc);

    doc.fontSize(18)
      .text(`Rechnung`, 260, 135, {
        width: 300,
        lineBreak: false,
        align: "right",
      });

    // Setze den Schriftstil und die Schriftgröße
    this.setFont(doc).fontSize(12);

    // Hole das aktuelle Template und den Mandanten
    const template = await this.getInvoiceTemplate();
    const mandant = await this.getMandant();

    // Firmenname aus dem Template oder Mandanten
    const companyName = template?.companyName || mandant?.name || "";

    // Adressdaten aus dem Template
    const street = template?.street
    const houseNr = template?.houseNr
    const zip = template?.zip
    const city = template?.city

    // Füge Firmenadresse hinzu
    this.setFont(doc, true)
      .fontSize(8)
      .text(companyName, 50, 155, { align: "left", continued: true });
    this.setFont(doc)
      .fontSize(8)
      .text(` | ${street} ${houseNr} | ${zip} ${city}`);

    // Füge Rechnungsdetails hinzu
    doc.fontSize(9);

    this.setFont(doc, true);
    doc.y = 350;
    doc.text(`Betreff `, 50, doc.y);

    doc.y = 180;
    doc.text(`Rechnungsnummer: `, 320, doc.y, { align: "left", width: 120 });
    if (this.Customernumber) {
      doc.text(`Kundennummer: `, { align: "left", width: 120 });
    }
    doc.text(`Leistungszeitraum: `, { align: "left", width: 120 });
    doc.text(`Rechnungsdatum: `, { align: "left", width: 120 });

    doc.y = 350;
    doc.moveDown();
    // 95
    this.setFont(doc);
    doc.text(`${this.Subject} `, 50, doc.y, { width: 400 });
    doc.text(`${this.Subtitle}`, 50, doc.y, { width: 400 });

    doc.y = 180;
    doc.x = 428;
    doc.fontSize(9);
    doc.text(`${this.InvoiceNumber} `, 428, doc.y, {
      align: "left",
      lineBreak: false,
    });
    if (this.Customernumber) {
      doc.moveDown();
      doc.text(`${this.Customernumber} `, 428, doc.y, {
        align: "left",
        lineBreak: false,
      });
    }
    doc.moveDown();
    doc.text(`${this.ServicePeriod} `, 428, doc.y, {
      align: "left",
      lineBreak: false,
    });
    doc.moveDown();
    doc.text(`${this.InvoiceDate} `, 428, doc.y, {
      align: "left",
      lineBreak: false,
    });
    doc.moveDown();
    // Füge eine Tabelle mit den Rechnungspositionen hinzu

    const maxPositionsFirstPage = 6; // 7 passen auf Seite 1
    const maxPositionsPerPage = 14; // 12 auf folgenden Seiten Passen Sie diese Zahl an, um die Anzahl der Positionen pro Seite zu steuern.
    const headerAndFooterHeight = 100; // Passen Sie diese Zahl an, um den Platz für Kopf- und Fußzeile zu steuern.

    this.drawTableHeader(doc, 400);

    doc.moveDown();
    doc.moveDown();
    const regexDot = /\./;
    for (let index = 0; index < this.invoice.invoice_positions.length; index++) {
      const position = this.invoice.invoice_positions[index];
      // Überprüfen Sie, ob ein Seitenumbruch erforderlich ist

      if (
        (index !== 0 &&
          index <= maxPositionsFirstPage &&
          index % maxPositionsFirstPage === 0) ||
        (index > maxPositionsFirstPage &&
          (index - maxPositionsFirstPage) % maxPositionsPerPage === 0)
      ) {
        doc.addPage();
        await this.drawFooterAndLogo(doc);
        doc.fontSize(10);
        this.drawTableHeader(doc, headerAndFooterHeight);
        doc.y = headerAndFooterHeight;
        doc.moveDown();
        doc.moveDown();
      }

      this.setFont(doc, true)
        .text(`${position.pos}`, 50, doc.y, {});
      doc.moveUp();

      // Verwende description, wenn vorhanden, sonst title
      const textToDisplay = position.description || position.title;

      this.setFont(doc)
        .text(textToDisplay, 80, doc.y, {
          width: 200,
          lineBreak: true,
        });
      doc.moveUp();
      // Menge hinzufügen
      doc.text(
        `${position.amount.toFixed(2).replace(regexDot, ",")}`,
        290,
        doc.y,
        { width: 40, align: "right", lineBreak: false }
      );
      doc.moveUp();
      doc.text(`${position.unit}`, 340, doc.y);
      doc.moveUp();
      // MwSt. einfügen
      doc.text(`${position.tax_rate}%`, 390, doc.y, {
        width: 22,
        align: "right",
        lineBreak: false,
      });
      doc.moveUp();
      // Einzelpreis hinzufügen
      doc.text(
        `${position.unit_price.toFixed(2).replace(regexDot, ",")}`,
        435,
        doc.y,
        { width: 55, align: "right", lineBreak: false }
      );
      doc.moveUp();

      // Gesamtpreis hinzufügen
      doc.text(
        `€ ${position.sum_gross.toFixed(2).replace(regexDot, ",")}`,
        504,
        doc.y,
        {
          width: 58,
          align: "right",
          lineBreak: false,
          lineGap: 5,
        }
      );
    }

    doc.moveDown();
    doc.moveDown();
    doc
      .moveTo(50, doc.y)
      .lineTo(563, doc.y)
      .lineWidth(0.5)
      .strokeColor("grey")
      .stroke();
    doc.moveUp();
    // Netto hinzufügen
    doc.text("Netto", 380, doc.y);
    doc.moveUp();
    doc.text(
      `€ ${this.invoice.sum_net.toFixed(2).replace(regexDot, ",")}`,
      450,
      doc.y,
      {
        align: "right",
        width: 112,
        lineBreak: false,
      }
    );
    // MwSt. hinzufügen // ToDo dynamic tax rate
    doc.text(`zzgl. ${this.invoice.vat_percentage}% MwSt.`, 335, doc.y);
    doc.moveUp();
    doc.text(
      `€ ${this.invoice.vat_amount.toFixed(2).replace(regexDot, ",")}`,
      450,
      doc.y,
      {
        align: "right",
        width: 112,
        lineBreak: false,
      }
    );
    // Gesamtpreis hinzufügen
    this.setFont(doc, true)
      .text("Gesamtpreis", 344, doc.y, {
        lineBreak: false,
      });

    doc.text(
      `€ ${this.invoice.sum_gross.toFixed(2).replace(regexDot, ",")}`,
      344,
      doc.y,
      { align: "right", width: 218, lineBreak: false }
    );
    doc.moveDown();
    doc.moveDown();
    switch (this.invoice.kindOfInvoice) {
      case "INVOICE": {
        this.setFont(doc)
          .text("Vielen Dank für ihren Besuch.", 50, doc.y, { align: "left" });
        doc.moveDown();
        doc.moveDown();
        doc.moveDown();
        doc.moveDown();
        doc.moveDown();
        this.setFont(doc, true)
          .text("Diese Rechnung wurde bereits bezahlt.", 50, doc.y, {
            align: "left",
          });
        break;
      }
      case "STORNO": {
        this.setFont(doc)
          .text(`Diese Stornorechnung storniert Rechnung`, 50, doc.y, {
            align: "left",
          });
        break;
      }
    }
    doc.moveDown();
    doc.moveDown();

    this.writePageNumber(doc);

    const date = new Date();
    const year = date.getFullYear();
    const month = date.getMonth() + 1; // JavaScript Monate sind 0-basiert, also addieren wir 1

    // Ordnerpfad aufbauen
    const invoiceFolder = strapi.config.get('server.invoiceFolder', '/tmp/invoices');
    console.log(`Using invoice folder: ${invoiceFolder}`);
    const dir = path.join(invoiceFolder, String(year), String(month));

    // Überprüfen, ob der Ordner existiert, wenn nicht, dann erstellen
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true }); // Option "recursive" erlaubt die Erstellung von verschachtelten Ordnern
    }

    const fileName = `Invoice_${this.invoice.invoice_number}.pdf`;
    const pdfPath = path.join(dir, fileName);

    const stream = fs.createWriteStream(pdfPath);
    doc.pipe(stream);
    doc.end();

    await new Promise<void>((resolve) => {
      stream.on("finish", function () {
        resolve();
      });
    });
    this.#pdfPath = pdfPath;
    return pdfPath;
    } catch (error) {
      console.error('Error creating PDF:', error);
      throw error;
    }
  }
}
