/**
 * epex service
 */

import { factories } from '@strapi/strapi';
import axios from 'axios';

interface AWattarPrice {
  start_timestamp: number;
  end_timestamp: number;
  marketprice: number;
  unit: string;
}

interface AWattarResponse {
  object: string;
  data: AWattarPrice[];
  url: string;
}

interface FetchResult {
  newPrices: number;
  updatedPrices: number;
  totalPrices: number;
  dateRange: {
    from: string;
    to: string;
  };
  summary: string;
}

const defaultService = factories.createCoreService('api::epex.epex');

export default {
  ...defaultService,

  /**
   * Check if we should fetch new data based on time and existing data
   */
  async shouldFetchData(): Promise<boolean> {
    const now = new Date();
    const currentHour = now.getHours();
    const currentMinute = now.getMinutes();

    // Only fetch next-day prices after 13:31 (1:31 PM)
    const isAfterPublishTime = currentHour > 13 || (currentHour === 13 && currentMinute >= 31);

    if (!isAfterPublishTime) {
      // Before 13:31, only fetch if we're missing current day data
      const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const existingTodayPrices = await strapi.entityService.findMany('api::epex.epex', {
        filters: {
          timestamp: {
            $gte: todayStart.toISOString(),
          },
        },
        limit: 1,
      });

      return existingTodayPrices.length === 0;
    }

    // After 13:31, check if we have tomorrow's data
    const tomorrowStart = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1);
    const tomorrowEnd = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 2);

    const existingTomorrowPrices = await strapi.entityService.findMany('api::epex.epex', {
      filters: {
        timestamp: {
          $gte: tomorrowStart.toISOString(),
          $lt: tomorrowEnd.toISOString(),
        },
      },
      limit: 1,
    });

    return existingTomorrowPrices.length === 0;
  },

  /**
   * Check daily API call limit (max 100 calls per day)
   */
  async checkApiCallLimit(): Promise<boolean> {
    const today = new Date();
    const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const todayEnd = new Date(today.getFullYear(), today.getMonth(), today.getDate() + 1);

    // Count API calls made today by checking terminal message logs
    const apiCalls = await strapi.entityService.findMany('api::terminal-message-log.terminal-message-log', {
      filters: {
        terminalId: 'AWATTAR_API',
        createdAt: {
          $gte: todayStart.toISOString(),
          $lt: todayEnd.toISOString(),
        },
      },
    });

    return apiCalls.length < 100; // Max 100 calls per day
  },

  /**
   * Fetch prices from aWATTar API
   */
  async fetchPricesFromAPI(startTimestamp: number): Promise<AWattarPrice[]> {
    const apiUrl = process.env.AWATTAR_API_URL || 'https://api.awattar.at/v1/marketdata';

    try {
      // Log API call
      const logService = strapi.service('api::terminal-message-log.terminal-message-log');
      await logService.log({
        terminalId: 'AWATTAR_API',
        direction: 'ServerToServer',
        payload: {
          url: `${apiUrl}?start=${startTimestamp}`,
          timestamp: new Date().toISOString(),
        },
        messageType: 'info',
      });

      const response = await axios.get<AWattarResponse>(`${apiUrl}?start=${startTimestamp}`, {
        timeout: 30000, // 30 second timeout
        headers: {
          'User-Agent': 'EulektroTerminalVerwaltung/1.0',
        },
      });

      // Log successful response
      await logService.log({
        terminalId: 'AWATTAR_API',
        direction: 'ServerToServer',
        payload: {
          url: `${apiUrl}?start=${startTimestamp}`,
          responseStatus: response.status,
          dataCount: response.data.data?.length || 0,
          timestamp: new Date().toISOString(),
        },
        messageType: 'info',
      });

      return response.data.data || [];
    } catch (error) {
      // Log API error
      const logService = strapi.service('api::terminal-message-log.terminal-message-log');
      await logService.log({
        terminalId: 'AWATTAR_API',
        direction: 'ServerToServer',
        payload: {
          url: `${apiUrl}?start=${startTimestamp}`,
          error: error.message,
          timestamp: new Date().toISOString(),
        },
        messageType: 'error',
      });

      throw new Error(`Failed to fetch prices from aWATTar API: ${error.message}`);
    }
  },

  /**
   * Store or update price data in database
   */
  async storePriceData(prices: AWattarPrice[]): Promise<{ newPrices: number; updatedPrices: number }> {
    let newPrices = 0;
    let updatedPrices = 0;

    for (const price of prices) {
      const timestamp = new Date(price.start_timestamp);

      // Check if price already exists
      const existingPrice = await strapi.entityService.findMany('api::epex.epex', {
        filters: {
          timestamp: timestamp.toISOString(),
        },
        limit: 1,
      });

      const priceData = {
        timestamp: timestamp.toISOString(),
        amount: price.marketprice,
        year: timestamp.getFullYear(),
        month: timestamp.getMonth() + 1,
        day: timestamp.getDate(),
        hour: timestamp.getHours(),
        minute: timestamp.getMinutes(),
        marketprice: price.marketprice,
        unit: price.unit,
        source: 'aWATTar',
        fetchedAt: new Date().toISOString(),
      };

      if (existingPrice.length > 0) {
        // Update existing price
        await strapi.entityService.update('api::epex.epex', existingPrice[0].documentId, {
          data: priceData,
        });
        updatedPrices++;
      } else {
        // Create new price
        await strapi.entityService.create('api::epex.epex', {
          data: priceData,
        });
        newPrices++;
      }
    }

    return { newPrices, updatedPrices };
  },

  /**
   * Backfill missing historical data (up to 90 days)
   */
  async backfillMissingData(): Promise<FetchResult> {
    const now = new Date();
    const ninetyDaysAgo = new Date(now.getTime() - (90 * 24 * 60 * 60 * 1000));

    // Find gaps in data
    const existingPrices = await strapi.entityService.findMany('api::epex.epex', {
      filters: {
        timestamp: {
          $gte: ninetyDaysAgo.toISOString(),
        },
      },
      sort: { timestamp: 'asc' },
    });

    const gaps = this.findDataGaps(existingPrices, ninetyDaysAgo, now);

    let totalNewPrices = 0;
    let totalUpdatedPrices = 0;
    let earliestDate = now;
    let latestDate = ninetyDaysAgo;

    for (const gap of gaps) {
      if (!(await this.checkApiCallLimit())) {
        console.log('API call limit reached, stopping backfill');
        break;
      }

      try {
        const startTimestamp = Math.floor(gap.start.getTime() / 1000) * 1000; // Convert to milliseconds
        const prices = await this.fetchPricesFromAPI(startTimestamp);

        if (prices.length > 0) {
          const result = await this.storePriceData(prices);
          totalNewPrices += result.newPrices;
          totalUpdatedPrices += result.updatedPrices;

          // Update date range
          const firstPrice = new Date(prices[0].start_timestamp);
          const lastPrice = new Date(prices[prices.length - 1].start_timestamp);

          if (firstPrice < earliestDate) earliestDate = firstPrice;
          if (lastPrice > latestDate) latestDate = lastPrice;
        }

        // Small delay to avoid overwhelming the API
        await new Promise(resolve => setTimeout(resolve, 1000));
      } catch (error) {
        console.error(`Failed to backfill data for gap starting ${gap.start}:`, error);
      }
    }

    return {
      newPrices: totalNewPrices,
      updatedPrices: totalUpdatedPrices,
      totalPrices: totalNewPrices + totalUpdatedPrices,
      dateRange: {
        from: earliestDate.toISOString().split('T')[0],
        to: latestDate.toISOString().split('T')[0],
      },
      summary: `Backfilled ${totalNewPrices} new prices and updated ${totalUpdatedPrices} existing prices`,
    };
  },

  /**
   * Find gaps in existing price data
   */
  findDataGaps(existingPrices: any[], startDate: Date, endDate: Date): { start: Date; end: Date }[] {
    const gaps: { start: Date; end: Date }[] = [];
    const hourlySlots = new Set();

    // Create set of existing hourly slots
    existingPrices.forEach(price => {
      const timestamp = new Date(price.timestamp);
      const hourSlot = new Date(timestamp.getFullYear(), timestamp.getMonth(), timestamp.getDate(), timestamp.getHours());
      hourlySlots.add(hourSlot.getTime());
    });

    // Check for missing hourly slots
    let currentDate = new Date(startDate);
    let gapStart: Date | null = null;

    while (currentDate <= endDate) {
      const hourSlot = new Date(currentDate.getFullYear(), currentDate.getMonth(), currentDate.getDate(), currentDate.getHours());

      if (!hourlySlots.has(hourSlot.getTime())) {
        if (!gapStart) {
          gapStart = new Date(hourSlot);
        }
      } else {
        if (gapStart) {
          gaps.push({
            start: gapStart,
            end: new Date(hourSlot.getTime() - 1),
          });
          gapStart = null;
        }
      }

      currentDate.setHours(currentDate.getHours() + 1);
    }

    // Close final gap if needed
    if (gapStart) {
      gaps.push({
        start: gapStart,
        end: endDate,
      });
    }

    return gaps;
  },

  /**
   * Main method to fetch and store prices - called by cron job
   */
  async fetchAndStorePrices(): Promise<FetchResult> {
    console.log('Starting EPEX price fetching job...');

    // Check if we should fetch data
    const shouldFetch = await this.shouldFetchData();
    if (!shouldFetch) {
      return {
        newPrices: 0,
        updatedPrices: 0,
        totalPrices: 0,
        dateRange: { from: '', to: '' },
        summary: 'No new data needed at this time',
      };
    }

    // Check API call limit
    const canMakeApiCall = await this.checkApiCallLimit();
    if (!canMakeApiCall) {
      throw new Error('Daily API call limit (100) reached');
    }

    const now = new Date();
    let startTimestamp: number;
    let fetchDescription: string;

    // Determine what to fetch based on time
    const currentHour = now.getHours();
    const currentMinute = now.getMinutes();
    const isAfterPublishTime = currentHour > 13 || (currentHour === 13 && currentMinute >= 31);

    if (isAfterPublishTime) {
      // Fetch tomorrow's prices
      const tomorrow = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1);
      startTimestamp = Math.floor(tomorrow.getTime() / 1000) * 1000;
      fetchDescription = 'next-day prices';
    } else {
      // Fetch today's prices
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      startTimestamp = Math.floor(today.getTime() / 1000) * 1000;
      fetchDescription = 'current-day prices';
    }

    try {
      console.log(`Fetching ${fetchDescription} from aWATTar API...`);
      const prices = await this.fetchPricesFromAPI(startTimestamp);

      if (prices.length === 0) {
        return {
          newPrices: 0,
          updatedPrices: 0,
          totalPrices: 0,
          dateRange: { from: '', to: '' },
          summary: `No ${fetchDescription} available from API`,
        };
      }

      console.log(`Received ${prices.length} price entries, storing in database...`);
      const result = await this.storePriceData(prices);

      // Calculate date range
      const firstPrice = new Date(prices[0].start_timestamp);
      const lastPrice = new Date(prices[prices.length - 1].start_timestamp);

      const fetchResult: FetchResult = {
        newPrices: result.newPrices,
        updatedPrices: result.updatedPrices,
        totalPrices: result.newPrices + result.updatedPrices,
        dateRange: {
          from: firstPrice.toISOString().split('T')[0],
          to: lastPrice.toISOString().split('T')[0],
        },
        summary: `Successfully imported ${result.newPrices} new prices and updated ${result.updatedPrices} existing prices for ${fetchDescription}`,
      };

      console.log('EPEX price fetching completed successfully:', fetchResult.summary);
      return fetchResult;

    } catch (error) {
      console.error('EPEX price fetching failed:', error);
      throw error;
    }
  },

  /**
   * Manual backfill method for admin interface
   */
  async manualBackfill(): Promise<FetchResult> {
    console.log('Starting manual backfill of EPEX price data...');
    return await this.backfillMissingData();
  },
};
