/**
 * epex router
 */

const { factories: routeFactories } = require('@strapi/strapi');

module.exports = routeFactories.createCoreRouter('api::epex.epex', {
  config: {
    find: {
      auth: false // Temporarily disable auth for testing
    },
    findOne: {
      auth: false // Temporarily disable auth for testing
    },
    create: {
      auth: {
        scope: ['api::epex.epex.create']
      }
    },
    update: {
      auth: {
        scope: ['api::epex.epex.update']
      }
    },
    delete: {
      auth: {
        scope: ['api::epex.epex.delete']
      }
    }
  }
});
