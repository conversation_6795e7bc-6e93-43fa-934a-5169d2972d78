/**
 * terminal controller
 */

import { factories } from '@strapi/strapi'

export default factories.createCoreController('api::terminal.terminal', ({ strapi }) => ({
    // Standard GET-Endpunkt für alle Terminals
    async find(ctx) {
        try {
            const { data, meta } = await super.find(ctx);
            return { data, meta };
        } catch (error) {
            ctx.throw(500, 'Fehler beim Abrufen der Terminals');
        }
    },

    // GET-Endpunkt für ein spezifisches Terminal
    async findOne(ctx) {
        try {
            const { data, meta } = await super.findOne(ctx);
            return { data, meta };
        } catch (error) {
            ctx.throw(500, 'Fehler beim Abrufen des Terminals');
        }
    },


}));
