{"kind": "collectionType", "collectionName": "tariffs", "info": {"singularName": "tariff", "pluralName": "tariffs", "displayName": "Tariff", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"name": {"type": "string", "required": true}, "mandants": {"type": "relation", "relation": "oneToMany", "target": "api::mandant.mandant", "mappedBy": "tariff"}, "terminals": {"type": "relation", "relation": "oneToMany", "target": "api::terminal.terminal", "mappedBy": "tariff"}, "ocpi_evses": {"type": "relation", "relation": "oneToMany", "target": "api::ocpi-evse.ocpi-evse"}, "ocpi_locations": {"type": "relation", "relation": "oneToMany", "target": "api::ocpi-location.ocpi-location", "mappedBy": "tariff"}, "history": {"type": "component", "repeatable": true, "component": "tariff.tarif-history"}, "priceAC": {"type": "component", "repeatable": true, "component": "tariff.price"}, "blockFeeAC": {"type": "component", "repeatable": true, "component": "tariff.block-fee"}, "priceDC": {"type": "component", "repeatable": true, "component": "tariff.price"}, "blockFeeDC": {"type": "component", "repeatable": true, "component": "tariff.block-fee"}, "deleted": {"type": "boolean", "default": false}, "payment_session": {"type": "relation", "relation": "oneToOne", "target": "api::payment-session.payment-session", "mappedBy": "tariff"}}}