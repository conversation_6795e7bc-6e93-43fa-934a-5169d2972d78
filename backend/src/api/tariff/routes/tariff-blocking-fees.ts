/**
 * Custom routes for tariff blocking fees
 */

export default {
  routes: [
    {
      method: 'POST',
      path: '/tariffs/:tariffId/blocking-fees/:type',
      handler: 'tariff.addBlockingFees',
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: 'POST',
      path: '/tariffs/:tariffId/prices/:type',
      handler: 'tariff.addPrices',
      config: {
        policies: [],
        middlewares: [],
      },
    },
  ],
};
