/**
 * Validity Period Management Service
 *
 * This service handles automatic validity period management to ensure:
 * - No gaps between validity periods
 * - No overlapping validity periods
 * - Proper validation of new validity periods
 */

interface ValidityPeriodComponent {
  id?: number;
  validFrom: string;
  validTo: string | null;
  [key: string]: any;
}

interface ValidityPeriodValidation {
  isValid: boolean;
  error?: string;
  adjustedComponents?: ValidityPeriodComponent[];
}

export default () => ({
  /**
   * Validates that a new validFrom date is at least 1 hour in the future
   */
  validateFutureDate(validFrom: string): boolean {
    const newValidFromDate = new Date(validFrom);
    const oneHourFromNow = new Date();
    oneHourFromNow.setHours(oneHourFromNow.getHours() + 1);

    return newValidFromDate >= oneHourFromNow;
  },

  /**
   * Validates and adjusts validity periods for pricing components
   * to prevent gaps and overlaps
   */
  validateAndAdjustValidityPeriods(
    existingComponents: ValidityPeriodComponent[],
    newValidFrom: string,
    newValidTo: string | null
  ): ValidityPeriodValidation {
    // Validate that new validFrom is in the future
    if (!this.validateFutureDate(newValidFrom)) {
      return {
        isValid: false,
        error: 'The validFrom date must be at least 1 hour in the future'
      };
    }

    const newValidFromDate = new Date(newValidFrom);
    const newValidToDate = newValidTo ? new Date(newValidTo) : null;

    // Validate that validTo is after validFrom if provided
    if (newValidToDate && newValidToDate <= newValidFromDate) {
      return {
        isValid: false,
        error: 'The validTo date must be after the validFrom date'
      };
    }

    // Find components that need adjustment
    const adjustedComponents: ValidityPeriodComponent[] = [];

    for (const component of existingComponents) {
      const componentValidFrom = new Date(component.validFrom);
      const componentValidTo = component.validTo ? new Date(component.validTo) : null;

      // Check if this component is currently active (validTo is null or in the future)
      const isActive = !componentValidTo || componentValidTo > new Date();

      if (isActive) {
        // Check if this component overlaps with the new validity period
        const hasOverlap = this.checkOverlap(
          componentValidFrom,
          componentValidTo,
          newValidFromDate,
          newValidToDate
        );

        if (hasOverlap) {
          // Adjust the existing component's validTo to match new component's validFrom
          const adjustedComponent = {
            ...component,
            validTo: newValidFrom
          };
          adjustedComponents.push(adjustedComponent);
        }
      }
    }

    return {
      isValid: true,
      adjustedComponents
    };
  },

  /**
   * Checks if two validity periods overlap
   */
  checkOverlap(
    start1: Date,
    end1: Date | null,
    start2: Date,
    end2: Date | null
  ): boolean {
    // If end1 is null, it means the first period is open-ended
    const effectiveEnd1 = end1 || new Date('2099-12-31');
    // If end2 is null, it means the second period is open-ended
    const effectiveEnd2 = end2 || new Date('2099-12-31');

    // Check for overlap: periods overlap if start1 < end2 AND start2 < end1
    return start1 < effectiveEnd2 && start2 < effectiveEnd1;
  },

  /**
   * Manages validity periods for blocking fees specifically
   */
  async manageBlockingFeeValidityPeriods(
    strapi: any,
    tariffId: string,
    type: 'ac' | 'dc',
    newValidFrom: string,
    newValidTo: string | null
  ): Promise<ValidityPeriodValidation> {
    // Get current tariff with blocking fees (only if not deleted)
    const tariff = await strapi.documents('api::tariff.tariff').findOne({
      documentId: tariffId,
      filters: {
        deleted: { $ne: true }
      },
      populate: {
        blockFeeAC: true,
        blockFeeDC: true,
      },
    });

    if (!tariff) {
      return {
        isValid: false,
        error: 'Tariff not found'
      };
    }

    const fieldName = type === 'ac' ? 'blockFeeAC' : 'blockFeeDC';
    const existingBlockFees = tariff[fieldName] || [];

    // Validate and get adjusted components
    const validation = this.validateAndAdjustValidityPeriods(
      existingBlockFees,
      newValidFrom,
      newValidTo
    );

    if (!validation.isValid) {
      return validation;
    }

    // If there are components to adjust, update them
    if (validation.adjustedComponents && validation.adjustedComponents.length > 0) {
      // Update the existing blocking fees with adjusted validity periods
      const updatedBlockFees = existingBlockFees.map(blockFee => {
        const adjustedComponent = validation.adjustedComponents!.find(
          adj => adj.id === blockFee.id
        );
        return adjustedComponent || blockFee;
      });

      // Update the tariff with adjusted blocking fees
      await strapi.documents('api::tariff.tariff').update({
        documentId: tariffId,
        data: {
          [fieldName]: updatedBlockFees,
        },
      });
    }

    return validation;
  },

  /**
   * Manages validity periods for price components specifically
   */
  async managePriceValidityPeriods(
    strapi: any,
    tariffId: string,
    type: 'ac' | 'dc',
    newValidFrom: string,
    newValidTo: string | null
  ): Promise<ValidityPeriodValidation> {
    // Get current tariff with price components (only if not deleted)
    const tariff = await strapi.documents('api::tariff.tariff').findOne({
      documentId: tariffId,
      filters: {
        deleted: { $ne: true }
      },
      populate: {
        priceAC: true,
        priceDC: true,
      },
    });

    if (!tariff) {
      return {
        isValid: false,
        error: 'Tariff not found'
      };
    }

    const fieldName = type === 'ac' ? 'priceAC' : 'priceDC';
    const existingPrices = tariff[fieldName] || [];

    // Validate and get adjusted components
    const validation = this.validateAndAdjustValidityPeriods(
      existingPrices,
      newValidFrom,
      newValidTo
    );

    if (!validation.isValid) {
      return validation;
    }

    // If there are components to adjust, update them
    if (validation.adjustedComponents && validation.adjustedComponents.length > 0) {
      // Update the existing prices with adjusted validity periods
      const updatedPrices = existingPrices.map(price => {
        const adjustedComponent = validation.adjustedComponents!.find(
          adj => adj.id === price.id
        );
        return adjustedComponent || price;
      });

      // Update the tariff with adjusted prices
      await strapi.documents('api::tariff.tariff').update({
        documentId: tariffId,
        data: {
          [fieldName]: updatedPrices,
        },
      });
    }

    return validation;
  },

  /**
   * Manages validity periods for entire tariffs when creating new ones
   */
  async manageTariffValidityPeriods(
    strapi: any,
    mandantIds: string[],
    newValidFrom: string,
    newValidTo: string | null
  ): Promise<ValidityPeriodValidation> {
    // Validate that new validFrom is in the future
    if (!this.validateFutureDate(newValidFrom)) {
      return {
        isValid: false,
        error: 'The validFrom date must be at least 1 hour in the future'
      };
    }

    // Find existing active tariffs for the same mandants (only non-deleted)
    const existingTariffs = await strapi.documents('api::tariff.tariff').findMany({
      filters: {
        mandants: {
          documentId: {
            $in: mandantIds
          }
        },
        deleted: { $ne: true }
      },
      populate: {
        priceAC: true,
        priceDC: true,
        blockFeeAC: true,
        blockFeeDC: true,
      },
    });

    const adjustedTariffs: any[] = [];
    const newValidFromDate = new Date(newValidFrom);

    for (const tariff of existingTariffs) {
      let needsUpdate = false;
      const updatedTariff = { ...tariff };

      // Check and adjust AC prices
      if (tariff.priceAC && tariff.priceAC.length > 0) {
        const adjustedPrices = tariff.priceAC.map((price: any) => {
          const priceValidTo = price.validTo ? new Date(price.validTo) : null;
          const isActive = !priceValidTo || priceValidTo > new Date();

          if (isActive && new Date(price.validFrom) < newValidFromDate) {
            needsUpdate = true;
            return { ...price, validTo: newValidFrom };
          }
          return price;
        });
        updatedTariff.priceAC = adjustedPrices;
      }

      // Check and adjust DC prices
      if (tariff.priceDC && tariff.priceDC.length > 0) {
        const adjustedPrices = tariff.priceDC.map((price: any) => {
          const priceValidTo = price.validTo ? new Date(price.validTo) : null;
          const isActive = !priceValidTo || priceValidTo > new Date();

          if (isActive && new Date(price.validFrom) < newValidFromDate) {
            needsUpdate = true;
            return { ...price, validTo: newValidFrom };
          }
          return price;
        });
        updatedTariff.priceDC = adjustedPrices;
      }

      // Check and adjust AC blocking fees
      if (tariff.blockFeeAC && tariff.blockFeeAC.length > 0) {
        const adjustedBlockFees = tariff.blockFeeAC.map((blockFee: any) => {
          const blockFeeValidTo = blockFee.validTo ? new Date(blockFee.validTo) : null;
          const isActive = !blockFeeValidTo || blockFeeValidTo > new Date();

          if (isActive && new Date(blockFee.validFrom) < newValidFromDate) {
            needsUpdate = true;
            return { ...blockFee, validTo: newValidFrom };
          }
          return blockFee;
        });
        updatedTariff.blockFeeAC = adjustedBlockFees;
      }

      // Check and adjust DC blocking fees
      if (tariff.blockFeeDC && tariff.blockFeeDC.length > 0) {
        const adjustedBlockFees = tariff.blockFeeDC.map((blockFee: any) => {
          const blockFeeValidTo = blockFee.validTo ? new Date(blockFee.validTo) : null;
          const isActive = !blockFeeValidTo || blockFeeValidTo > new Date();

          if (isActive && new Date(blockFee.validFrom) < newValidFromDate) {
            needsUpdate = true;
            return { ...blockFee, validTo: newValidFrom };
          }
          return blockFee;
        });
        updatedTariff.blockFeeDC = adjustedBlockFees;
      }

      if (needsUpdate) {
        adjustedTariffs.push(updatedTariff);
      }
    }

    // Update all affected tariffs
    for (const adjustedTariff of adjustedTariffs) {
      await strapi.documents('api::tariff.tariff').update({
        documentId: adjustedTariff.documentId,
        data: {
          priceAC: adjustedTariff.priceAC,
          priceDC: adjustedTariff.priceDC,
          blockFeeAC: adjustedTariff.blockFeeAC,
          blockFeeDC: adjustedTariff.blockFeeDC,
        },
      });
    }

    return {
      isValid: true,
      adjustedComponents: adjustedTariffs
    };
  }
});
