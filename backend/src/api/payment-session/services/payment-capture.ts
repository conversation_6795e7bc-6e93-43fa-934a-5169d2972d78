/**
 * Payment Capture Service
 *
 * Dieser Service stellt Funktionen zur Berechnung des zu belastenden Betrags
 * und zur Belastung der Kreditkarte bereit.
 */

import { AxiosInstance } from 'axios';

/**
 * Berechnet den zu belastenden Betrag basierend auf dem Preis und den CDR-Daten
 *
 * @param price - Der Preis für den Ladevorgang
 * @param cdr - Die CDR-Daten
 * @returns Der zu belastende Betrag in Cent
 */
export async function calculateAmountToCaptchaByPriceAndCdr(
  price: any,
  cdr: any
): Promise<number> {
  try {
    console.log('Berechne Betrag für CDR:', cdr.cdrId);
    console.log('Preis:', price);

    // Extrahiere die relevanten Daten aus dem CDR
    const totalEnergy = cdr.totalEnergy || 0; // kWh
    const totalTime = cdr.totalTime || 0; // Stunden
    const totalTimeMinutes = Math.ceil(totalTime * 60); // Umrechnung in Minuten

    console.log(`Energie: ${totalEnergy} kWh, Zeit: ${totalTimeMinutes} Minuten`);

    // Berechne die Kosten basierend auf dem Preis
    let totalAmount = 0;

    // 1. Session-Gebühr (einmalig)
    totalAmount += price.sessionFee || 0;
    console.log(`Session-Gebühr: ${price.sessionFee / 100} €`);

    // 2. Energiekosten (pro kWh)
    const energyCost = totalEnergy * (price.pricePerKwh);
    totalAmount += energyCost;
    console.log(`Energiekosten: ${energyCost / 100} € (${totalEnergy} kWh * ${price.pricePerKwh / 100} €/kWh)`);

    // 3. Blockiergebühr (pro Minute nach Ablauf der Gnadenfrist)
    let blockingFee = 0;
    const gracePeriodMinutes = price.gracePeriod || 0;

    if (totalTimeMinutes > gracePeriodMinutes) {
      const blockingMinutes = totalTimeMinutes - gracePeriodMinutes;
      blockingFee = blockingMinutes * (price.perMinute || 0);

      // Begrenze die Blockiergebühr auf das Maximum
      if (price.maxBlockFee && blockingFee > price.maxBlockFee) {
        blockingFee = price.maxBlockFee;
      }

      console.log(`Blockiergebühr: ${blockingFee / 100} € (${blockingMinutes} Min. * ${price.perMinute / 100} €/Min., Max: ${price.maxBlockFee / 100} €)`);
    } else {
      console.log(`Keine Blockiergebühr (${totalTimeMinutes} Min. < Gnadenfrist ${gracePeriodMinutes} Min.)`);
    }

    totalAmount += blockingFee;

    // Runde auf ganze Cent
    totalAmount = Math.round(totalAmount);

    console.log(`Gesamtbetrag: ${totalAmount / 100} €`);

    return totalAmount;
  } catch (error) {
    console.error('Fehler bei der Berechnung des zu belastenden Betrags:', error);
    throw error;
  }
}

/**
 * Belastet die Kreditkarte mit dem berechneten Betrag
 *
 * @param strapi - Die Strapi-Instanz
 * @param paymentSessionId - Die ID der Payment-Session
 * @param amountToCapture - Der zu belastende Betrag in Cent
 * @returns Die aktualisierte Payment-Session
 */
export async function chargeCreditCard(
  paymentSessionId: string,
  amountToCapture: number
): Promise<any> {
  try {
    console.log(`Belaste Kreditkarte für Payment-Session ${paymentSessionId} mit ${amountToCapture / 100} €`);

    // Hole die Payment-Session
    const paymentSession = await strapi.documents('api::payment-session.payment-session').findOne({
      documentId: paymentSessionId,
      populate: {
        terminal: {
          populate: {
            payter_connection: true,
            mandant: true
          }
        }
      }
    });

    if (!paymentSession) {
      throw new Error(`Payment-Session ${paymentSessionId} nicht gefunden`);
    }

    // Prüfe, ob die Payment-Session im Status "authorized" ist
    if (paymentSession.paymentSessionState !== 'authorized') {
      throw new Error(`Payment-Session ${paymentSessionId} ist nicht im Status "authorized", sondern "${paymentSession.paymentSessionState}"`);
    }

    // Prüfe, ob der zu belastende Betrag nicht größer ist als der autorisierte Betrag
    if (amountToCapture > paymentSession.blockedAmount) {
      console.warn(`Warnung: Der zu belastende Betrag (${amountToCapture / 100} €) ist größer als der autorisierte Betrag (${paymentSession.blockedAmount / 100} €). Begrenze auf den autorisierten Betrag.`);
      amountToCapture = paymentSession.blockedAmount;
    }

    // Hole die Payter-Connection für das Terminal
    const terminalId = paymentSession.terminal?.serialNumber;
    if (!terminalId) {
      throw new Error(`Kein Terminal für Payment-Session ${paymentSessionId} gefunden`);
    }
    const ctx = {
      state: {
        terminal: paymentSession.terminal
      }
    };

    // Hole den API-Client für die Payter-API
    const { apiClient, callback } = await strapi.service('api::payter.api-client').getPayterApiClient(ctx);

    if (!apiClient) {
      throw new Error('Konnte keinen API-Client für die Payter-API erstellen');
    }

    // Führe die Capture-Operation durch
    const captureResult = await capturePayment(
      apiClient,
      terminalId,
      paymentSession.paymentIntent,
      amountToCapture
    );

    console.log('Capture-Ergebnis:', captureResult);

    // Überprüfe das Ergebnis der Capture-Operation
    if (!captureResult.success) {
      console.error(`Fehler beim Belasten der Kreditkarte: ${captureResult.message}`);

      // Aktualisiere die Payment-Session mit dem Fehlerstatus
      const updatedPaymentSession = await strapi.documents('api::payment-session.payment-session').update({
        documentId: paymentSessionId,
        data: {
          paymentSessionState: 'error',
          history: [
            ...(paymentSession.history as unknown as any || []),
            {
              action: 'capture_failed',
              amount: amountToCapture,
              timestamp: new Date().toISOString(),
              result: captureResult
            }
          ]
        }
      });

      return {
        success: false,
        paymentSession: updatedPaymentSession,
        error: captureResult.error,
        message: captureResult.message
      };
    }

    // Aktualisiere die Payment-Session mit dem erfolgreichen Status
    const updatedPaymentSession = await strapi.documents('api::payment-session.payment-session').update({
      documentId: paymentSessionId,
      data: {
        paymentSessionState: 'captured',
        capturedAmount: amountToCapture,
        closedAt: new Date(),
        history: [
          ...(paymentSession.history as unknown as any || []),
          {
            action: 'capture',
            amount: amountToCapture,
            timestamp: new Date().toISOString(),
            result: captureResult
          }
        ]
      }
    });

    console.log(`Kreditkarte erfolgreich mit ${amountToCapture / 100} € belastet`);

    return {
      success: true,
      paymentSession: updatedPaymentSession,
      message: `Kreditkarte erfolgreich mit ${amountToCapture / 100} € belastet`
    };
  } catch (error) {
    console.error('Fehler beim Belasten der Kreditkarte:', error);
    throw error;
  }
}

/**
 * Führt die Capture-Operation durch
 *
 * @param apiClient - Der API-Client für die Payter-API
 * @param terminalId - Die Terminal-ID
 * @param paymentIntent - Die Payment-Intent-ID
 * @param amount - Der zu belastende Betrag in Cent
 * @returns Das Ergebnis der Capture-Operation
 */
export async function capturePayment(
  apiClient: AxiosInstance,
  terminalId: string,
  paymentIntent: string,
  amount: number
): Promise<any> {
  try {
    // Parameter für die Capture-Operation
    const params = new URLSearchParams({
      commitAmount: amount.toString(),
    });

    const url = `/terminals/${terminalId}/sessions/${paymentIntent}/commit?${params.toString()}`;
    console.log(`Capturing payment for terminal ${terminalId} with URL: ${url}`);

    // Sende die Anfrage an die Payter API
    const response = await apiClient.post(url, {});

    // Erfolgreiche Antwort (200)
    console.log(`Capture successful for terminal ${terminalId}:`, response.data);

    // Überprüfe, ob die Antwort das erwartete Format hat
    if (response.data && response.data.result === 'APPROVED') {
      console.log(`Payment captured successfully: ${amount / 100} €, Authorization code: ${response.data.authorizationCode}`);
      return {
        success: true,
        data: response.data,
        message: 'Payment captured successfully'
      };
    } else {
      console.warn(`Unexpected response format from Payter API:`, response.data);
      return {
        success: true, // Wir nehmen an, dass es erfolgreich war, wenn kein Fehler geworfen wurde
        data: response.data,
        message: 'Payment captured with unexpected response format'
      };
    }
  } catch (error: any) {
    console.error(`Error capturing payment for terminal ${terminalId}:`, error);

    // Extrahiere die Fehlermeldung und den Statuscode
    const statusCode = error.response?.status || 500;
    const errorMessage = error.response?.data?.message || error.message || 'Unknown error';

    // Behandle verschiedene Fehlerfälle
    switch (statusCode) {
      case 404:
        // Terminal nicht gefunden
        console.error(`Terminal ${terminalId} not found`);
        return {
          success: false,
          error: 'TERMINAL_NOT_FOUND',
          message: `Terminal ${terminalId} not found`,
          statusCode
        };

      case 409:
        // Konflikt (z.B. bereits abgebucht)
        console.error(`Conflict while capturing payment: ${errorMessage}`);
        return {
          success: false,
          error: 'CAPTURE_CONFLICT',
          message: errorMessage,
          statusCode
        };

      case 500:
        // Serverfehler
        console.error(`Server error while capturing payment: ${errorMessage}`);
        return {
          success: false,
          error: 'SERVER_ERROR',
          message: errorMessage,
          statusCode
        };

      default:
        // Sonstiger Fehler
        console.error(`Error (${statusCode}) while capturing payment: ${errorMessage}`);
        return {
          success: false,
          error: 'CAPTURE_FAILED',
          message: errorMessage,
          statusCode
        };
    }
  }
}
