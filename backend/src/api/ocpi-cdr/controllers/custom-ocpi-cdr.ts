/**
 * OCPI 2.2.1 CDRs (Charge Detail Records) Module Controller
 * Implementiert die OCPI 2.2.1 CDR-Endpunkte für CPO
 */

import { z } from 'zod';
import { createSuccessResponse, createErrorResponse, OCPIStatusCode } from '../../ocpi-common/utils';
import {displayScreen} from "../../payter/controllers/terminal/uiActions";
import {InvoiceManager} from "../../invoice/services/invoice-manager";
import {cancelPayment} from "../../payment-session/services/payment-capture";

// Zod Schemas für Datenvalidierung
const PriceSchema = z.object({
  excl_vat: z.number().optional(),
  incl_vat: z.number().optional(),
  currency: z.string().optional()
});

const LocationSchema = z.object({
  id: z.string().optional(),
  name: z.string().optional(),
  address: z.string().optional(),
  city: z.string().optional(),
  postal_code: z.string().optional(),
  country: z.string().optional(),
  coordinates: z.object({
    latitude: z.string().optional(),
    longitude: z.string().optional()
  }).optional(),
  evse_id: z.string().optional(),
  connector_id: z.string().optional()
});

const TokenSchema = z.object({
  uid: z.string().optional(),
  type: z.string().optional(),
  contract_id: z.string().optional(),
  country_code: z.string().optional(),
  party_id: z.string().optional()
});

const ChargingPeriodSchema = z.object({
  start_date_time: z.string().datetime().optional(),
  dimensions: z.array(
    z.object({
      type: z.string(),
      volume: z.number()
    })
  ).optional()
});

const CdrSchema = z.object({
  id: z.string().optional(),
  session_id: z.string().optional(),
  country_code: z.string().optional(),
  party_id: z.string().optional(),
  start_date_time: z.string().datetime().optional(),
  end_date_time: z.string().datetime().optional(),
  cdr_token: TokenSchema.optional(),
  auth_method: z.string().optional(),
  authorization_reference: z.string().optional(),
  cdr_location: LocationSchema.optional(),
  currency: z.string().optional(),
  tariffs: z.array(z.any()).optional(),
  charging_periods: z.array(ChargingPeriodSchema).optional(),
  total_cost: PriceSchema.optional(),
  total_fixed_cost: PriceSchema.optional(),
  total_energy_cost: PriceSchema.optional(),
  total_time_cost: PriceSchema.optional(),
  total_parking_cost: PriceSchema.optional(),
  total_energy: z.number(),
  total_time: z.number(),
  status: z.enum(['PENDING', 'ACCEPTED', 'REJECTED']).optional(),
  last_updated: z.iso.datetime().optional()
}).refine(data => data.id || data.session_id, {
  message: "At least one of 'id' or 'session_id' must be provided",
  path: ["id"]
});

// Helper-Funktionen für OCPI-Antworten wurden in ocpi-common/utils/response-utils.ts ausgelagert

// OCPI CDR Controller
module.exports =  {

  /**
   * OCPI CDR-Endpoint GET (CPO → eMSP)
   * Liefert alle CDRs oder einen spezifischen CDR nach ID
   */
  async getCdrs(ctx) {
    try {
      // Token-Validator-Service referenzieren
      const tokenValidator = strapi.service('api::ocpi-common.token-validator');

      // Token validieren
      const validationResult = await tokenValidator.validateRequestToken(ctx, 'receiving');

      if (!validationResult.valid) {
        return ctx.send(createErrorResponse(2000, "Unauthorized"), { status: 401 });
      }

      // Log für den OCPI-Aufruf erstellen
      await strapi.service('api::ocpi-log.ocpi-logger').logRequest(ctx, 'GET', '/cdrs');

      // Prüfen, ob ein spezifischer CDR angefragt wurde
      const cdrId = ctx.params.cdrId;

      let response;
      if (cdrId) {
        // Einen spezifischen CDR suchen
        const cdr = await strapi.documents('api::ocpi-cdr.ocpi-cdr').findMany({
          filters: { cdrId },
        });

        if (!cdr || cdr.length === 0) {
          return ctx.send(createErrorResponse(2001, "CDR not found"), { status: 404 });
        }

        // CDR in OCPI-Format konvertieren
        const ocpiCdr = convertToOcpiCdr(cdr[0]);
        response = createSuccessResponse(ocpiCdr);
      } else {
        // Alle CDRs abrufen (ggf. mit Paginierung)
        const page = parseInt(ctx.query.offset || '0');
        const limit = parseInt(ctx.query.limit || '50');

        const cdrs = await strapi.documents('api::ocpi-cdr.ocpi-cdr').findMany({
          sort: { createdAt: 'desc' },
          offset: page,
          limit: limit
        });

        // CDRs in OCPI-Format konvertieren
        const ocpiCdrs = cdrs.map(convertToOcpiCdr);
        response = createSuccessResponse(ocpiCdrs);
      }

      return ctx.send(response);
    } catch (error) {
      console.error('Error in getCdrs:', error);
      return ctx.send(createErrorResponse(3000, "Server error"), { status: 500 });
    }
  },

  /**
   * OCPI CDR-Endpoint POST (CPO → eMSP)
   * Einen neuen CDR erstellen
   */
  async createCdr(ctx) {
    try {
      // Token-Validator-Service referenzieren
      const tokenValidator = strapi.service('api::ocpi-common.token-validator');

      // Token validieren
      const validationResult = await tokenValidator.validateRequestToken(ctx, 'receiving');

      if (!validationResult.valid) {
        return ctx.send(createErrorResponse(OCPIStatusCode.CLIENT_ERROR, "Unauthorized"), { status: 401 });
      }

      const cdrData = ctx.request.body;
      console.log('Received CDR data:', JSON.stringify(cdrData, null, 2));

      // Validiere die Daten mit Zod
      const validationResult2 = CdrSchema.safeParse(cdrData);
      if (!validationResult2.success) {
        console.error('CDR validation failed:', z.treeifyError(validationResult2.error))
        return ctx.send(createErrorResponse(OCPIStatusCode.INVALID_PARAMETERS, `Invalid CDR data: ${validationResult2.error.message}`), { status: 400 });
      }

      // Verwende die validierten Daten
      const validatedCdrData = validationResult2.data;

      // Verwende id oder session_id als cdrId
      const cdrId = validatedCdrData.id || validatedCdrData.session_id;

      // Prüfen, ob der CDR bereits existiert
      const existingCdrs = await strapi.documents('api::ocpi-cdr.ocpi-cdr').findMany({
        filters: { cdrId: cdrId },
      });

      if (existingCdrs && existingCdrs.length > 0) {
        console.log(`CDR with ID ${cdrId} already exists, returning success without updating`);
        // CDR existiert bereits, gib einfach eine Erfolgsantwort zurück ohne Daten
        return ctx.send(createSuccessResponse());
      }

      // Suche nach der PaymentSession anhand der authorization_reference
      let paymentSession = null;
      let mandantId = null;

      if (validatedCdrData.authorization_reference) {
        try {

          paymentSession = await strapi.documents('api::payment-session.payment-session').findOne({
            documentId: validatedCdrData.authorization_reference,
            populate: {
              mandant: true,
              terminal: {
                populate: {
                    payter_connection: true,
                }
              }
            }
          });

          if (paymentSession && paymentSession.mandant) {
            mandantId = paymentSession.mandant.documentId;
          }
        } catch (error) {
          console.warn(`Could not find payment session for authorization_reference ${validatedCdrData.authorization_reference}:`, error.message);
        }
      }

      // Konvertiere die CDR-Daten
      const convertedData = convertFromOcpiCdr(validatedCdrData);

      // Erstelle die Daten für den neuen CDR
      const createData = {
        // Pflichtfelder explizit angeben
        cdrId,
        totalCost: convertedData.totalCost,
        currency: convertedData.currency,
        totalTime: convertedData.totalTime,
        timestamp: convertedData.timestamp,
        status: convertedData.status || 'pending',

        // Beziehungen
        payment_session: paymentSession?.documentId || null,
        mandant: mandantId,
        publishedAt: new Date(),

        // Alle anderen Felder
        ...convertedData
      };


      // Neuen CDR erstellen
      const cdr = await strapi.documents('api::ocpi-cdr.ocpi-cdr').create({
        data: createData
      });

      const session = await strapi.documents('api::ocpi-session.ocpi-session').findFirst({
        where: { sessionId: cdr.sessionId },
        populate: {
          payment_session: {
            populate: ['ocpi_evse']
          }
        }
      });

      if (!session || !session.payment_session || !session.payment_session.ocpi_evse) {
        return ctx.send(createErrorResponse(OCPIStatusCode.CLIENT_ERROR, "Session not found"), { status: 404 });
      }

      if(validatedCdrData.total_energy < 0.2 && Math.floor(validatedCdrData.total_time) * 60 + (validatedCdrData?.total_time % 1) * 60 < 120 ) {
        console.log(`CDR energy < 0.2 and time < 120. Not captured`);

        const cancelResponse = cancelPayment(session.payment_session);

        return ctx.send(createSuccessResponse({
          status_code: 1000,
          status_message: 'Successful transmitted - but not captured',
          timestamp: new Date().toISOString()
        }));
      }


      // Zeitpunkt für die Preisermittlung aus payment_session.showPriceDateTime oder fallback auf timestamp
      let priceDateTime;
      if (session.payment_session.showPriceDateTime) {
        priceDateTime = new Date(session.payment_session.showPriceDateTime);
      } else {
        priceDateTime = new Date(createData.timestamp);
      }

      // Preis für den Ladevorgang ermitteln
      const price = await strapi.service('api::tariff.tariff').getTariff(
        session.payment_session.ocpi_evse.documentId,
        priceDateTime
      );

      // Berechne den zu belastenden Betrag


      const im = new InvoiceManager();
      const newInvoice = await im.createPreviewInvoice(session.payment_session.documentId);

      let amountToCapture = Math.round(newInvoice.sum_gross * 100);
      //const amountToCapture = await strapi.service('api::payment-session.payment-session').calculateAmountToCaptchaByPriceAndCdr(price, createData);
      console.log(`Zu belastender Betrag: ${amountToCapture / 100} €`);

      // Belaste die Kreditkarte
      const chargeCreditCardResult= await strapi.service('api::payment-session.payment-session').chargeCreditCard(
        session.payment_session.documentId,
        amountToCapture
      );

      if (!chargeCreditCardResult.success) {
        console.error(`Fehler beim Belasten der Kreditkarte: ${chargeCreditCardResult.message}`);
        // Wir setzen die Verarbeitung trotzdem fort, da der CDR gespeichert werden soll
        // Die Zahlung kann später manuell abgewickelt werden
      } else {
        console.log('Kreditkarte erfolgreich belastet:', chargeCreditCardResult.message);
        const finalizedInvoice = await im.finalize(newInvoice?.documentId);
        if(finalizedInvoice) {
          console.log(`Rechnung wurde finalisiert, invoice = ${finalizedInvoice?.invoice_number}`)
          const paidInvoice = await im.setStatusToPaid(finalizedInvoice.documentId);
        }


      }


      await strapi.service('api::terminal.push-terminal-screen').pushQrCode(paymentSession)

      ctx["state"]["terminal"] = paymentSession.terminal;

      const { apiClient, callback } = await strapi.service('api::payter.api-client').getPayterApiClient(ctx);

      void await displayScreen(ctx, apiClient, callback.ui, 'screen-init');

      return ctx.send(createSuccessResponse());
    } catch (error) {
      console.error('Error in createCdr:', error);
      return ctx.send(createErrorResponse(OCPIStatusCode.SERVER_ERROR, "Server error"), { status: 500 });
    }
  },

};

/**
 * Konvertiert einen Strapi-CDR in ein OCPI-CDR-Objekt
 */
function convertToOcpiCdr(cdr: any): z.infer<typeof CdrSchema> {
  // Basis-Objekt mit den Pflichtfeldern
  const result = {
    id: cdr.cdrId,
    session_id: cdr.sessionId,
    total_cost: {
      excl_vat: parseFloat(cdr.totalCost) || 0,
      currency: cdr.currency
    },
    currency: cdr.currency,
    total_energy: parseFloat(cdr.totalEnergy) || 0,
    total_time: parseFloat(cdr.totalTime) || 0, // Zeit in Stunden
    status: mapToOcpiStatus(cdr.status),
    last_updated: cdr.lastUpdated || cdr.updatedAt || cdr.timestamp
  } as z.infer<typeof CdrSchema>;

  // Wenn rawData vorhanden ist, versuche es zu parsen und zu verwenden
  if (cdr.rawData) {
    try {
      const rawData = JSON.parse(cdr.rawData);

      // Füge alle Felder aus rawData hinzu, die nicht bereits im result sind
      Object.keys(rawData).forEach(key => {
        if (result[key] === undefined) {
          result[key] = rawData[key];
        }
      });
    } catch (error) {
      console.warn('Could not parse rawData:', error.message);
    }
  }

  // Füge weitere Felder hinzu, wenn sie vorhanden sind
  if (cdr.countryCode) result.country_code = cdr.countryCode;
  if (cdr.partyId) result.party_id = cdr.partyId;
  if (cdr.startDateTime) result.start_date_time = cdr.startDateTime;
  if (cdr.endDateTime) result.end_date_time = cdr.endDateTime;
  if (cdr.authMethod) result.auth_method = cdr.authMethod;
  if (cdr.authorizationReference) result.authorization_reference = cdr.authorizationReference;

  // Füge Standortdaten aus der Component hinzu, wenn vorhanden
  if (cdr.cdrLocation) {
    result.cdr_location = {
      id: cdr.cdrLocation.locationId,
      name: cdr.cdrLocation.name,
      address: cdr.cdrLocation.address,
      city: cdr.cdrLocation.city,
      postal_code: cdr.cdrLocation.postalCode,
      country: cdr.cdrLocation.country,
      coordinates: cdr.cdrLocation.coordinates,
      evse_id: cdr.cdrLocation.evseId,
      connector_id: cdr.cdrLocation.connectorId,
    };
  }
  // Fallback auf die alten Felder, wenn die Component nicht vorhanden ist
  else if (cdr.locationId || cdr.evseId) {
    result.cdr_location = {};
    if (cdr.locationId) result.cdr_location.id = cdr.locationId;
    if (cdr.locationName) result.cdr_location.name = cdr.locationName;
    if (cdr.locationAddress) result.cdr_location.address = cdr.locationAddress;
    if (cdr.locationCity) result.cdr_location.city = cdr.locationCity;
    if (cdr.locationPostalCode) result.cdr_location.postal_code = cdr.locationPostalCode;
    if (cdr.locationCountry) result.cdr_location.country = cdr.locationCountry;
    if (cdr.evseId) result.cdr_location.evse_id = cdr.evseId;
    if (cdr.connectorId) result.cdr_location.connector_id = cdr.connectorId;
  }

  // Füge Token-Daten aus der Component hinzu, wenn vorhanden
  if (cdr.cdrToken) {
    result.cdr_token = {
      uid: cdr.cdrToken.uid,
      type: cdr.cdrToken.type,
      contract_id: cdr.cdrToken.contractId,
      country_code: cdr.cdrToken.countryCode || cdr.countryCode,
      party_id: cdr.cdrToken.partyId || cdr.partyId,
    };
  }
  // Fallback auf die alten Felder, wenn die Component nicht vorhanden ist
  else if (cdr.tokenUid || cdr.tokenType) {
    result.cdr_token = {};
    if (cdr.tokenUid) result.cdr_token.uid = cdr.tokenUid;
    if (cdr.tokenType) result.cdr_token.type = cdr.tokenType;
    if (cdr.tokenContractId) result.cdr_token.contract_id = cdr.tokenContractId;
    if (cdr.countryCode) result.cdr_token.country_code = cdr.countryCode;
    if (cdr.partyId) result.cdr_token.party_id = cdr.partyId;
  }

  // Füge Kostendaten hinzu, wenn vorhanden
  if (cdr.totalFixedCost) {
    result.total_fixed_cost = {
      excl_vat: parseFloat(cdr.totalFixedCost) || 0,
      currency: cdr.currency
    };
  }

  if (cdr.totalEnergyCost) {
    result.total_energy_cost = {
      excl_vat: parseFloat(cdr.totalEnergyCost) || 0,
      currency: cdr.currency
    };
  }

  if (cdr.totalTimeCost) {
    result.total_time_cost = {
      excl_vat: parseFloat(cdr.totalTimeCost) || 0,
      currency: cdr.currency
    };
  }

  if (cdr.totalParkingCost) {
    result.total_parking_cost = {
      excl_vat: parseFloat(cdr.totalParkingCost) || 0,
      currency: cdr.currency
    };
  }

  // Füge weitere Felder hinzu
  if (cdr.totalEnergy !== undefined) result.total_energy = parseFloat(cdr.totalEnergy);

  // Füge Ladeperioden und Tarife als JSON hinzu, wenn vorhanden
  if (cdr.chargingPeriods) {
    // Wenn chargingPeriods bereits ein Array ist, verwende es direkt
    if (Array.isArray(cdr.chargingPeriods)) {
      result.charging_periods = cdr.chargingPeriods;
    }
    // Wenn chargingPeriods ein String ist, versuche es zu parsen
    else if (typeof cdr.chargingPeriods === 'string') {
      try {
        result.charging_periods = JSON.parse(cdr.chargingPeriods);
      } catch (error) {
        console.warn('Could not parse chargingPeriods:', error.message);
      }
    }
  }

  if (cdr.tariffs) {
    // Wenn tariffs bereits ein Array ist, verwende es direkt
    if (Array.isArray(cdr.tariffs)) {
      result.tariffs = cdr.tariffs;
    }
    // Wenn tariffs ein String ist, versuche es zu parsen
    else if (typeof cdr.tariffs === 'string') {
      try {
        result.tariffs = JSON.parse(cdr.tariffs);
      } catch (error) {
        console.warn('Could not parse tariffs:', error.message);
      }
    }
  }

  return result;
}

/**
 * Konvertiert ein OCPI-CDR-Objekt in ein Strapi-CDR-Objekt
 * Speichert alle relevanten Daten aus dem komplexen JSON-Payload
 */
function convertFromOcpiCdr(ocpiCdr: z.infer<typeof CdrSchema>) {
  // Extrahiere die Basisdaten
  const result: Record<string, any> = {
    // Pflichtfelder für das Strapi-Modell
    cdrId: ocpiCdr.id || ocpiCdr.session_id,
    sessionId: ocpiCdr.session_id,
    totalCost: ocpiCdr.total_cost?.excl_vat || 0,
    currency: ocpiCdr.currency || "EUR",
    totalTime: ocpiCdr.total_time || 0, // Zeit in Stunden
    timestamp: new Date(ocpiCdr.last_updated || ocpiCdr.end_date_time || new Date()).toISOString(),
    status: mapFromOcpiStatus(ocpiCdr.status) || 'pending',

    // Zusätzliche Felder als JSON speichern
    rawData: JSON.stringify(ocpiCdr), // Speichere den gesamten Payload als JSON

    // Extrahiere weitere wichtige Daten
    countryCode: ocpiCdr.country_code,
    partyId: ocpiCdr.party_id,
    startDateTime: ocpiCdr.start_date_time,
    endDateTime: ocpiCdr.end_date_time,
    authMethod: ocpiCdr.auth_method,
    authorizationReference: ocpiCdr.authorization_reference,
    lastUpdated: ocpiCdr.last_updated,

    // Speichere die Ladeperioden und Tarife als JSON
    chargingPeriods: ocpiCdr.charging_periods || [],
    tariffs: ocpiCdr.tariffs || [],

    // Extrahiere Kostendaten
    totalFixedCost: ocpiCdr.total_fixed_cost?.excl_vat || 0,
    totalEnergyCost: ocpiCdr.total_energy_cost?.excl_vat || 0,
    totalTimeCost: ocpiCdr.total_time_cost?.excl_vat || 0,
    totalParkingCost: ocpiCdr.total_parking_cost?.excl_vat || 0,
    totalEnergy: ocpiCdr.total_energy || 0,
  };

  // Erstelle die cdrLocation-Komponente, wenn Standortdaten vorhanden sind
  if (ocpiCdr.cdr_location) {
    result.cdrLocation = {
      locationId: ocpiCdr.cdr_location.id,
      name: ocpiCdr.cdr_location.name,
      address: ocpiCdr.cdr_location.address,
      city: ocpiCdr.cdr_location.city,
      postalCode: ocpiCdr.cdr_location.postal_code,
      country: ocpiCdr.cdr_location.country,
      coordinates: ocpiCdr.cdr_location.coordinates,
      evseId: ocpiCdr.cdr_location.evse_id,
      connectorId: ocpiCdr.cdr_location.connector_id,
    };
  }

  // Erstelle die cdrToken-Komponente, wenn Token-Daten vorhanden sind
  if (ocpiCdr.cdr_token) {
    result.cdrToken = {
      uid: ocpiCdr.cdr_token.uid,
      type: ocpiCdr.cdr_token.type,
      contractId: ocpiCdr.cdr_token.contract_id,
      countryCode: ocpiCdr.cdr_token.country_code,
      partyId: ocpiCdr.cdr_token.party_id
    };
  }

  // Entferne undefined-Werte
  Object.keys(result).forEach(key => {
    if (result[key] === undefined) {
      delete result[key];
    }
  });

  return result;
}

/**
 * Mapping von Strapi-Status zu OCPI-Status
 */
function mapToOcpiStatus(strapiStatus: string): 'PENDING' | 'ACCEPTED' | 'REJECTED' {
  const statusMap: Record<string, 'PENDING' | 'ACCEPTED' | 'REJECTED'> = {
    'pending': 'PENDING',
    'confirmed': 'ACCEPTED',
    'rejected': 'REJECTED'
  };
  return statusMap[strapiStatus] || 'PENDING';
}

/**
 * Mapping von OCPI-Status zu Strapi-Status
 */
function mapFromOcpiStatus(ocpiStatus: string | undefined): 'pending' | 'confirmed' | 'rejected' {
  const statusMap: Record<string, 'pending' | 'confirmed' | 'rejected'> = {
    'PENDING': 'pending',
    'ACCEPTED': 'confirmed',
    'REJECTED': 'rejected'
  };
  return ocpiStatus ? (statusMap[ocpiStatus] || 'pending') : 'pending';
}
