/**
 * mandant controller
 */

import { factories } from '@strapi/strapi'

export default factories.createCoreController('api::ocpi-location.ocpi-location', ({ strapi }) => ({
    async find(ctx) {
        try {
            console.log('OCPI-Location find method called');
            const { data, meta } = await super.find(ctx);
            return { data, meta };
        } catch (error) {
            console.error('Error in OCPI-Location find:', error);
            ctx.throw(500, '<PERSON>hler beim Abrufen der OCPI-Locations');
        }
    }
}));
