import screens from "../../config/payter-screens.json";
import { AxiosInstance } from 'axios';
import {PayterScreen, ScreensConfig} from "../../types/types";
import apiClientService, {handleApiError} from "../../services/api-client";

/**
 * <PERSON><PERSON><PERSON> einen spezifischen Screen auf dem Terminal an
 *
 * @param ctx   - Context
 * @param apiClient - Der API-Client
 * @param callbackUrl - Die Callback-URL
 * @param screenId - Die Screen-ID
 * @language - Die Sprache des Screens
 * @returns Die Antwort der API
 */
export async function displayScreen(ctx, apiClient: AxiosInstance, callbackUrl: string, screenId: string) {
    // Basis-Screen aus der Konfiguration laden
    const typedScreens = screens as ScreensConfig;

    // Prüfe, ob die angeforderte Sprache existiert, sonst verwende Deutsch als Fallback
    let screen = typedScreens.screens[ctx.state.terminal.currentLanguage || 'de'].find(screen => screen.id === screenId) as PayterScreen | undefined;
    if (!screen) {
        throw new Error(`Screen ${screenId} not found in configuration for language ${ctx.state.terminal.language}`);
    }

    // Wenn es sich um den Ladepunkte-Screen handelt, dynamisch die EVSEs laden
    if (screenId === 'screen-list-charging-points') {
        try {
            // Terminal anhand der serialNumber suchen
            const terminals = await strapi.documents('api::terminal.terminal').findMany({
                filters: { serialNumber: ctx.state.terminal.serialNumber },
                populate: { evses: true }
            });

            if (terminals && terminals.length > 0) {
                const terminal = terminals[0];
                const evses = terminal.evses || [];

                // Neues Screen-Objekt erstellen, um das Original nicht zu verändern
                // Wir erstellen eine Kopie des Original-Screens, um sicherzustellen, dass alle erforderlichen Eigenschaften vorhanden sind
                screen = {
                    ...screen,
                    properties: {
                        ...screen.properties,
                    }
                }

                if (evses.length > 0) {
                    // Sortiere die EVSEs nach PhysicalReference oder EvseId
                    const sortedEvses = [...evses].sort((a, b) => {
                        const aRef = a.physicalReference || a.evseId || '';
                        const bRef = b.physicalReference || b.evseId || '';
                        return aRef.localeCompare(bRef);
                    });

                    // Füge jeden EVSE als Ladepunkt hinzu
                    sortedEvses.forEach((evse, index) => {
                        const itemKey = `items.${evse.documentId}`;
                        const labelKey = `${itemKey}.label`;

                        // Verwende PhysicalReference oder EvseId oder einen Standardwert
                        let displayName = evse.labelForTerminal || evse.physicalReference || evse.evseId || `Ladepunkt *${(index + 1).toString().padStart(2, '0')}`;

                        // Kürze displayName auf 14 Zeichen, zeige die letzten 14 Zeichen mit "..." am Anfang wenn nötig
                        if (displayName.length > 14) {
                            displayName = '...' + displayName.slice(-11);
                        }

                        // Füge den Ladepunkt zum Screen hinzu
                        screen.properties[itemKey] = "";
                        screen.properties[labelKey] = displayName;
                    });
                }
                // Hinweis: Der Fallback für keine EVSEs ist nicht mehr notwendig, da wir bereits Standardwerte gesetzt haben

                // Log für Debugging
                console.log(`Dynamically generated charging points screen for terminal ${ctx.state.terminal.serialNumber} with ${evses.length} EVSEs`);
            }
        } catch (error) {
            console.error(`Error loading EVSEs for terminal ${ctx.state.terminal.serialNumber}:`, error);
            // Bei Fehler den Standard-Screen verwenden
        }
    }
    else if (screenId === 'screen-stop-charging') {
        //todo
        callbackUrl+=`?evseDocId=${ctx.response}`
    }
    
    else if (screenId === 'screen-language') {
        //  "items.prod1": "",
        //  "items.prod1.label": "Netherlands",
        //  "items.prod1.ico": "nl",

        const list = typedScreens["list"]

        /*const list = {
            "items.de": "",
            "items.de.label": "Deutsch",
            "items.de.ico": "de",

            "items.fr": "",
            "items.fr.label": "Französisch",
            "items.fr.ico": "fr",

            "items.pl": "",
            "items.pl.label": "Polnisch",
            "items.pl.ico": "pl",

            "items.cz": "",
            "items.cz.label": "Tschechisch",
            "items.cz.ico": "cz",

            "items.dk": "",
            "items.dk.label": "Dänisch",
            "items.dk.ico": "dk",

            "items.nl": "",
            "items.nl.label": "Niederländisch",
            "items.nl.ico": "nl",

            "items.it": "",
            "items.it.label": "Italienisch",
            "items.it.ico": "it",

            "items.es": "",
            "items.es.label": "Spanisch",
            "items.es.ico": "es",

            "items.sv": "",
            "items.sv.label": "Schwedisch",
            "items.sv.ico": "sv",

            "items.no": "",
            "items.no.label": "Norwegisch",
            "items.no.ico": "no"
        }*/

        screen.properties = {
            ...screen.properties,
            ...list
        }
    }

    const url = `/terminals/${ctx.state.terminal.serialNumber}/ui`;
    const params = new URLSearchParams({ callbackUrl: callbackUrl });

    return await apiClient.post(`${url}?${params.toString()}`, screen);
}

/**
 * Zeigt den Idle-Screen auf dem Terminal an
 */
export async function getInit(ctx) {
    const { terminalId } = ctx.query;
    if (!terminalId) {
        ctx.status = 400;
        return ctx.send({
            status_code: 400,
            status_message: "Bad Request: Missing 'terminalId' query parameter."
        });
    }

    const terminal = await strapi.documents('api::terminal.terminal').findFirst({
        filters: {
            serialNumber: terminalId
        },
        populate: ['mandant']
    });
    ctx.state.terminal = terminal;

    try {
        // API-Client und Callback-URLs für das Terminal holen
        const { apiClient, callback } = await apiClientService.getPayterApiClient(ctx);
        if (!apiClient || !callback.ui) {
            ctx.status = 500;
            return ctx.send({
                status_code: 500,
                status_message: "Failed to create API client for terminal."
            });
        }

        // Screen anzeigen
        const response = await displayScreen(ctx, apiClient, callback.ui, 'screen-init');

        // Terminal-Status auf ACTIVE setzen
        await strapi.documents('api::terminal.terminal').update({
            documentId: terminal.documentId,
            data: {
                terminalState: 'ACTIVE',
                lastUpdate: new Date()
            }
        });

        console.log(`Terminal ${terminalId}: Status auf ACTIVE gesetzt`);

        return ctx.send({
            status_code: 1000,
            status_message: `OK - Idle Screen displayed on terminal ${terminalId}`,
            payter_response_status: response.status,
            payter_response_data: response.data
        });
    } catch (error) {
        return handleApiError(ctx, error, terminalId);
    }
}

/**
 * Zeigt den Charging-Points-Screen auf dem Terminal an
 */
export async function getChargingPoints(ctx) {
    const { terminalId } = ctx.query;
    if (!terminalId) {
        ctx.status = 400;
        return ctx.send({
            status_code: 400,
            status_message: "Bad Request: Missing 'terminalId' query parameter."
        });
    }

    ctx.state.terminal = await strapi.documents('api::terminal.terminal').findFirst({
        filters: {
            serialNumber: terminalId
        },
        populate: ['mandant']
    });

    try {
        // API-Client und Callback-URLs für das Terminal holen
        const { apiClient, callback } = await apiClientService.getPayterApiClient(ctx);
        if (!apiClient || !callback.ui) {
            ctx.status = 500;
            return ctx.send({
                status_code: 500,
                status_message: "Failed to create API client for terminal."
            });
        }

        // Screen anzeigen
        const response = await displayScreen(ctx,apiClient, callback.ui, 'screen-list-charging-points');

        return ctx.send({
            status_code: 1000,
            status_message: `OK - Charging Points Screen displayed on terminal ${terminalId}`,
            payter_response_status: response.status,
            payter_response_data: response.data
        });
    } catch (error) {
        return handleApiError(ctx, error, terminalId);
    }
}

/**
 * Zeigt den Reading-Screen auf dem Terminal an
 */
export async function getReading(ctx) {
    const { terminalId } = ctx.query;
    if (!terminalId) {
        ctx.status = 400;
        return ctx.send({
            status_code: 400,
            status_message: "Bad Request: Missing 'terminalId' query parameter."
        });
    }

    ctx.state.terminal = await strapi.documents('api::terminal.terminal').findFirst({
        filters: {
            serialNumber: terminalId
        },
        populate: ['mandant']
    });

    try {
        // API-Client und Callback-URLs für das Terminal holen
        const { apiClient, callback } = await apiClientService.getPayterApiClient(ctx);
        if (!apiClient || !callback.ui) {
            ctx.status = 500;
            return ctx.send({
                status_code: 500,
                status_message: "Failed to create API client for terminal."
            });
        }

        // Screen anzeigen
        const response = await displayScreen(ctx, apiClient, callback.ui, 'screen-reading');

        return ctx.send({
            status_code: 1000,
            status_message: `OK - Reading Screen displayed on terminal ${terminalId}`,
            payter_response_status: response.status,
            payter_response_data: response.data
        });
    } catch (error) {
        return handleApiError(ctx, error, terminalId);
    }
}

/**
 * Zeigt den Charging-Started-Screen auf dem Terminal an
 */
export async function getChargingStarted(ctx) {
    const { terminalId, sessionId } = ctx.query;
    if (!terminalId) {
        ctx.status = 400;
        return ctx.send({
            status_code: 400,
            status_message: "Bad Request: Missing 'terminalId' query parameter."
        });
    }
    ctx = {
        state: {
            terminal: await strapi.documents('api::terminal.terminal').findFirst({
                filters: {
                    serialNumber: terminalId
                },
                populate: ['mandant']
            })
        }
    };

    try {
        // API-Client und Callback-URLs für das Terminal holen
        const { apiClient, callback } = await apiClientService.getPayterApiClient(ctx);
        if (!apiClient || !callback.ui) {
            ctx.status = 500;
            return ctx.send({
                status_code: 500,
                status_message: "Failed to create API client for terminal."
            });
        }

        // Lade den Charging-Started-Screen aus der Konfiguration
        const chargingStartedScreen = screens.screens[ctx.state.terminal.currentLanguage].find(screen => screen.id === 'screen-charging-started');
        if (!chargingStartedScreen) {
            ctx.status = 500;
            return ctx.send({
                status_code: 500,
                status_message: "Charging Started Screen not configured."
            });
        }

        // Ersetze den Platzhalter für die Session-ID im QR-Code
        const screenWithSessionId = JSON.parse(JSON.stringify(chargingStartedScreen));
        if (sessionId && screenWithSessionId.properties.qrcode) {
            screenWithSessionId.properties.qrcode = screenWithSessionId.properties.qrcode.replace('{{session_id}}', sessionId);
        }

        // Sende die Anfrage an die Payter API
        const url = `/terminals/${terminalId}/ui`;
        const params = new URLSearchParams({ callbackUrl: callback.ui });
        const response = await apiClient.post(`${url}?${params.toString()}`, screenWithSessionId);

        return ctx.send({
            status_code: 1000,
            status_message: `OK - Charging Started Screen displayed on terminal ${terminalId}`,
            payter_response_status: response.status,
            payter_response_data: response.data
        });
    } catch (error) {
        return handleApiError(ctx, error, terminalId);
    }
}

/**
 * Zeigt den Out-of-Order-Screen auf dem Terminal an und setzt den Status auf OUT_OF_ORDER
 */
export async function getOutOfOrder(ctx) {
    const { terminalId } = ctx.query;
    if (!terminalId) {
        ctx.status = 400;
        return ctx.send({
            status_code: 400,
            status_message: "Bad Request: Missing 'terminalId' query parameter."
        });
    }

    ctx.state.terminal = await strapi.documents('api::terminal.terminal').findFirst({
                filters: {
                    serialNumber: terminalId
                },
                populate: ['mandant']
            });


    try {
        // Rufe die setOutOfOrderScreen-Funktion auf
        await strapi.service('api::terminal.terminal-status').setOutOfOrderScreen(terminalId);

        return ctx.send({
            status_code: 1000,
            status_message: `OK - Out-of-Order Screen displayed on terminal ${terminalId} and status set to OUT_OF_ORDER`
        });
    } catch (error) {
        return handleApiError(ctx, error, terminalId);
    }
}
