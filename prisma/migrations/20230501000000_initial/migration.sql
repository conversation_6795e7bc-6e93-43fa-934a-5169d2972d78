-- CreateTable
CREATE TABLE `ChargePoint` (
    `id` VARCHAR(191) NOT NULL,
    `name` VA<PERSON>HAR(191) NOT NULL,
    `model` VARCHAR(191),
    `vendor` VARCHAR(191),
    `firmwareVersion` VARCHAR(191),
    `ocppVersion` VARCHAR(191),
    `ipAddress` VARCHAR(191),
    `lastHeartbeat` DATETIME(3),
    `connectorIds` JSON,
    `phaseConfig` JSO<PERSON>,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,
    `groupId` VARCHAR(191),

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `Group` (
    `id` VARCHAR(191) NOT NULL,
    `name` VARCHAR(191) NOT NULL,
    `description` TEXT,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `PhaseReading` (
    `id` VARCHAR(191) NOT NULL,
    `timestamp` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `phase` VARCHAR(191) NOT NULL,
    `voltage` DOUBLE NOT NULL,
    `current` DOUBLE NOT NULL,
    `power` DOUBLE NOT NULL,
    `chargePointId` VARCHAR(191) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `LimitProfile` (
    `id` VARCHAR(191) NOT NULL,
    `phase` VARCHAR(191) NOT NULL,
    `type` VARCHAR(191) NOT NULL,
    `value` DOUBLE NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,
    `groupId` VARCHAR(191) NOT NULL,
    `scheduleId` VARCHAR(191),

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE "TimeSchedule" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,
    "timezone" TEXT NOT NULL DEFAULT 'UTC',
    "startTime" TEXT NOT NULL,
    "endTime" TEXT NOT NULL,
    "daysOfWeek" INTEGER[],
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "TimeSchedule_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ChargingProfileState" (
    "id" TEXT NOT NULL,
    "connectorId" INTEGER NOT NULL,
    "profileJson" JSONB NOT NULL,
    "validUntil" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "chargePointId" TEXT NOT NULL,

    CONSTRAINT "ChargingProfileState_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "PhaseReading_chargePointId_timestamp_idx" ON "PhaseReading"("chargePointId", "timestamp");

-- CreateIndex
CREATE INDEX "PhaseReading_timestamp_idx" ON "PhaseReading"("timestamp");

-- CreateIndex
CREATE UNIQUE INDEX "ChargingProfileState_chargePointId_connectorId_key" ON "ChargingProfileState"("chargePointId", "connectorId");

-- AddForeignKey
ALTER TABLE "ChargePoint" ADD CONSTRAINT "ChargePoint_groupId_fkey" FOREIGN KEY ("groupId") REFERENCES "Group"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "PhaseReading" ADD CONSTRAINT "PhaseReading_chargePointId_fkey" FOREIGN KEY ("chargePointId") REFERENCES "ChargePoint"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "LimitProfile" ADD CONSTRAINT "LimitProfile_groupId_fkey" FOREIGN KEY ("groupId") REFERENCES "Group"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "LimitProfile" ADD CONSTRAINT "LimitProfile_scheduleId_fkey" FOREIGN KEY ("scheduleId") REFERENCES "TimeSchedule"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "ChargingProfileState" ADD CONSTRAINT "ChargingProfileState_chargePointId_fkey" FOREIGN KEY ("chargePointId") REFERENCES "ChargePoint"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
