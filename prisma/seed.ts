import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  console.log('Seeding database...');

  // Create groups
  const mainGroup = await prisma.group.upsert({
    where: { id: 'demo-1' },
    update: {},
    create: {
      id: 'demo-1',
      name: 'Main Building',
      description: 'Charging stations in the main building',
    },
  });

  const visitorGroup = await prisma.group.upsert({
    where: { id: 'demo-2' },
    update: {},
    create: {
      id: 'demo-2',
      name: 'Visitor Parking',
      description: 'Charging stations in the visitor parking area',
    },
  });

  console.log('Created groups:', { mainGroup, visitorGroup });

  // Create charge points
  const chargePoints = await Promise.all([
    prisma.chargePoint.upsert({
      where: { id: 'CP001' },
      update: {
        groupId: mainGroup.id,
      },
      create: {
        id: 'CP001',
        name: 'Charger 1',
        model: 'EV Charger Pro 2000',
        vendor: 'ChargeTech',
        connectorIds: [1, 2],
        groupId: mainGroup.id,
      },
    }),
    prisma.chargePoint.upsert({
      where: { id: 'CP002' },
      update: {
        groupId: mainGroup.id,
      },
      create: {
        id: 'CP002',
        name: 'Charger 2',
        model: 'EV Charger Pro 2000',
        vendor: 'ChargeTech',
        connectorIds: [1, 2],
        groupId: mainGroup.id,
      },
    }),
    prisma.chargePoint.upsert({
      where: { id: 'CP003' },
      update: {
        groupId: mainGroup.id,
      },
      create: {
        id: 'CP003',
        name: 'Charger 3',
        model: 'EV Charger Pro 2000',
        vendor: 'ChargeTech',
        connectorIds: [1, 2],
        groupId: mainGroup.id,
      },
    }),
    prisma.chargePoint.upsert({
      where: { id: 'CP004' },
      update: {
        groupId: visitorGroup.id,
      },
      create: {
        id: 'CP004',
        name: 'Visitor 1',
        model: 'EV Charger Lite',
        vendor: 'ChargeTech',
        connectorIds: [1],
        groupId: visitorGroup.id,
      },
    }),
    prisma.chargePoint.upsert({
      where: { id: 'CP005' },
      update: {
        groupId: visitorGroup.id,
      },
      create: {
        id: 'CP005',
        name: 'Visitor 2',
        model: 'EV Charger Lite',
        vendor: 'ChargeTech',
        connectorIds: [1],
        groupId: visitorGroup.id,
      },
    }),
  ]);

  console.log(`Created ${chargePoints.length} charge points`);

  // Create schedules
  const defaultSchedule = await prisma.timeSchedule.upsert({
    where: { id: 'default' },
    update: {},
    create: {
      id: 'default',
      name: 'Default',
      description: 'Always active',
      startTime: '00:00',
      endTime: '23:59',
      daysOfWeek: [0, 1, 2, 3, 4, 5, 6], // All days
    },
  });

  const nightSchedule = await prisma.timeSchedule.upsert({
    where: { id: 'night' },
    update: {},
    create: {
      id: 'night',
      name: 'Night Reduction',
      description: '22:00 - 06:00, All days',
      startTime: '22:00',
      endTime: '06:00',
      daysOfWeek: [0, 1, 2, 3, 4, 5, 6], // All days
    },
  });

  const peakSchedule = await prisma.timeSchedule.upsert({
    where: { id: 'peak' },
    update: {},
    create: {
      id: 'peak',
      name: 'Peak Hours',
      description: '17:00 - 20:00, Weekdays',
      startTime: '17:00',
      endTime: '20:00',
      daysOfWeek: [1, 2, 3, 4, 5], // Weekdays
    },
  });

  console.log('Created schedules:', { defaultSchedule, nightSchedule, peakSchedule });

  // Create limits
  const limits = await Promise.all([
    // Main Building - Default limits
    prisma.limitProfile.upsert({
      where: { id: 'main-l1-default' },
      update: {},
      create: {
        id: 'main-l1-default',
        phase: 'L1',
        type: 'KW',
        value: 50,
        groupId: mainGroup.id,
        scheduleId: defaultSchedule.id,
      },
    }),
    prisma.limitProfile.upsert({
      where: { id: 'main-l2-default' },
      update: {},
      create: {
        id: 'main-l2-default',
        phase: 'L2',
        type: 'KW',
        value: 50,
        groupId: mainGroup.id,
        scheduleId: defaultSchedule.id,
      },
    }),
    prisma.limitProfile.upsert({
      where: { id: 'main-l3-default' },
      update: {},
      create: {
        id: 'main-l3-default',
        phase: 'L3',
        type: 'KW',
        value: 50,
        groupId: mainGroup.id,
        scheduleId: defaultSchedule.id,
      },
    }),

    // Main Building - Peak hours limits
    prisma.limitProfile.upsert({
      where: { id: 'main-l1-peak' },
      update: {},
      create: {
        id: 'main-l1-peak',
        phase: 'L1',
        type: 'KW',
        value: 30,
        groupId: mainGroup.id,
        scheduleId: peakSchedule.id,
      },
    }),
    prisma.limitProfile.upsert({
      where: { id: 'main-l2-peak' },
      update: {},
      create: {
        id: 'main-l2-peak',
        phase: 'L2',
        type: 'KW',
        value: 30,
        groupId: mainGroup.id,
        scheduleId: peakSchedule.id,
      },
    }),
    prisma.limitProfile.upsert({
      where: { id: 'main-l3-peak' },
      update: {},
      create: {
        id: 'main-l3-peak',
        phase: 'L3',
        type: 'KW',
        value: 30,
        groupId: mainGroup.id,
        scheduleId: peakSchedule.id,
      },
    }),

    // Visitor Parking - Default limits
    prisma.limitProfile.upsert({
      where: { id: 'visitor-l1-default' },
      update: {},
      create: {
        id: 'visitor-l1-default',
        phase: 'L1',
        type: 'KW',
        value: 25,
        groupId: visitorGroup.id,
        scheduleId: defaultSchedule.id,
      },
    }),
    prisma.limitProfile.upsert({
      where: { id: 'visitor-l2-default' },
      update: {},
      create: {
        id: 'visitor-l2-default',
        phase: 'L2',
        type: 'KW',
        value: 25,
        groupId: visitorGroup.id,
        scheduleId: defaultSchedule.id,
      },
    }),
    prisma.limitProfile.upsert({
      where: { id: 'visitor-l3-default' },
      update: {},
      create: {
        id: 'visitor-l3-default',
        phase: 'L3',
        type: 'KW',
        value: 25,
        groupId: visitorGroup.id,
        scheduleId: defaultSchedule.id,
      },
    }),
  ]);

  console.log(`Created ${limits.length} limits`);

  console.log('Database seeding completed');
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
