-- Seed data for OCPP Load Manager

-- Create groups
INSERT INTO `Group` (id, name, description, createdAt, updatedAt)
VALUES
  ('demo-1', 'Main Building', 'Charging stations in the main building', NOW(), NOW()),
  ('demo-2', 'Visitor Parking', 'Charging stations in the visitor parking area', NOW(), NOW())
ON DUPLICATE KEY UPDATE id=id;

-- Create charge points
INSERT INTO "ChargePoint" (id, name, model, vendor, "connectorIds", "groupId", "createdAt", "updatedAt")
VALUES
  ('CP001', 'Charger 1', 'EV Charger Pro 2000', 'ChargeTech', ARRAY[1, 2], 'demo-1', NOW(), NOW()),
  ('CP002', 'Charger 2', 'EV Charger Pro 2000', 'ChargeTech', ARRAY[1, 2], 'demo-1', NOW(), NOW()),
  ('CP003', 'Charger 3', 'EV Charger Pro 2000', 'ChargeTech', ARRAY[1, 2], 'demo-1', NOW(), NOW()),
  ('CP004', 'Visitor 1', 'EV Charger Lite', 'ChargeTech', ARRAY[1], 'demo-2', NOW(), NOW()),
  ('CP005', 'Visitor 2', 'EV Charger Lite', 'ChargeTech', ARRAY[1], 'demo-2', NOW(), NOW())
ON CONFLICT (id) DO NOTHING;

-- Create schedules
INSERT INTO "TimeSchedule" (id, name, description, timezone, "startTime", "endTime", "daysOfWeek", "createdAt", "updatedAt")
VALUES
  ('default', 'Default', 'Always active', 'UTC', '00:00', '23:59', ARRAY[0, 1, 2, 3, 4, 5, 6], NOW(), NOW()),
  ('night', 'Night Reduction', '22:00 - 06:00, All days', 'UTC', '22:00', '06:00', ARRAY[0, 1, 2, 3, 4, 5, 6], NOW(), NOW()),
  ('peak', 'Peak Hours', '17:00 - 20:00, Weekdays', 'UTC', '17:00', '20:00', ARRAY[1, 2, 3, 4, 5], NOW(), NOW())
ON CONFLICT (id) DO NOTHING;

-- Create limits
INSERT INTO "LimitProfile" (id, phase, type, value, "groupId", "scheduleId", "createdAt", "updatedAt")
VALUES
  -- Main Building - Default limits
  ('main-l1-default', 'L1', 'KW', 50, 'demo-1', 'default', NOW(), NOW()),
  ('main-l2-default', 'L2', 'KW', 50, 'demo-1', 'default', NOW(), NOW()),
  ('main-l3-default', 'L3', 'KW', 50, 'demo-1', 'default', NOW(), NOW()),

  -- Main Building - Peak hours limits
  ('main-l1-peak', 'L1', 'KW', 30, 'demo-1', 'peak', NOW(), NOW()),
  ('main-l2-peak', 'L2', 'KW', 30, 'demo-1', 'peak', NOW(), NOW()),
  ('main-l3-peak', 'L3', 'KW', 30, 'demo-1', 'peak', NOW(), NOW()),

  -- Visitor Parking - Default limits
  ('visitor-l1-default', 'L1', 'KW', 25, 'demo-2', 'default', NOW(), NOW()),
  ('visitor-l2-default', 'L2', 'KW', 25, 'demo-2', 'default', NOW(), NOW()),
  ('visitor-l3-default', 'L3', 'KW', 25, 'demo-2', 'default', NOW(), NOW())
ON CONFLICT (id) DO NOTHING;
