// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
  seed     = "ts-node --compiler-options \"{\\\"module\\\":\\\"CommonJS\\\"}\" prisma/seed.ts"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

// ChargePoint represents a charging station
model ChargePoint {
  id              String         @id
  name            String
  model           String?
  vendor          String?
  firmwareVersion String?
  ocppVersion     String?
  ipAddress       String?
  lastHeartbeat   DateTime?
  connectorIds    Json
  phaseConfig     Json?          // Configuration for phases (L1, L2, L3)
  createdAt       DateTime       @default(now())
  updatedAt       DateTime       @updatedAt

  // Relations
  group           Group?         @relation(fields: [groupId], references: [id])
  groupId         String?
  phaseReadings   PhaseReading[]
  chargingProfiles ChargingProfileState[]
}

// Group represents a logical grouping of ChargePoints
model Group {
  id             String       @id @default(uuid())
  name           String
  description    String?
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt

  // Relations
  chargePoints   ChargePoint[]
  limitProfiles  LimitProfile[]
}

// PhaseReading represents a measurement from a ChargePoint for a specific phase
model PhaseReading {
  id            String      @id @default(uuid())
  timestamp     DateTime    @default(now())
  phase         String      // L1, L2, or L3
  voltage       Float       // Volts
  current       Float       // Amps
  power         Float       // kW

  // Relations
  chargePoint   ChargePoint @relation(fields: [chargePointId], references: [id])
  chargePointId String

  @@index([chargePointId, timestamp])
  @@index([timestamp])
}

// LimitProfile represents a power limit for a group on a specific phase
model LimitProfile {
  id          String       @id @default(uuid())
  phase       String       // L1, L2, or L3
  type        String       // KW or A
  value       Float
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt

  // Relations
  group       Group        @relation(fields: [groupId], references: [id])
  groupId     String
  schedule    TimeSchedule? @relation(fields: [scheduleId], references: [id])
  scheduleId  String?
}

// TimeSchedule represents a time-based schedule for when limits are active
model TimeSchedule {
  id          String        @id @default(uuid())
  name        String
  description String?
  timezone    String        @default("UTC")
  startTime   String        // HH:MM format
  endTime     String        // HH:MM format
  daysOfWeek  Json          // 0-6 for Sunday-Saturday
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt

  // Relations
  limitProfiles LimitProfile[]
}

// ChargingProfileState represents the current charging profile for a ChargePoint
model ChargingProfileState {
  id            String      @id @default(uuid())
  connectorId   Int
  profileJson   Json        // The full OCPP ChargingProfile object
  validUntil    DateTime?
  createdAt     DateTime    @default(now())
  updatedAt     DateTime    @updatedAt

  // Relations
  chargePoint   ChargePoint @relation(fields: [chargePointId], references: [id])
  chargePointId String

  @@unique([chargePointId, connectorId])
}
