#!/bin/bash

# ====================
# BETA KONFIGURATION
# ====================

DOMAIN1="api.terminal.beta.eulektro.de"
PORT1=1338

DOMAIN2="terminal.beta.eulektro.de"
PORT2=3001

EMAIL="<EMAIL>"  # Für Let's Encrypt Registrierung
ENABLE_HTTPS=true

# ====================
# INSTALLATION
# ====================

echo "👉 Installiere NGINX (falls noch nicht installiert)..."
sudo apt update
sudo apt install -y nginx

# ====================
# NGINX KONFIGURATION
# ====================

echo "👉 Erstelle NGINX-Konfigurationen für Beta..."

sudo tee /etc/nginx/sites-available/$DOMAIN1 > /dev/null <<EOF
server {
    listen 80;
    server_name $DOMAIN1;

    location / {
        proxy_pass http://localhost:$PORT1;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_cache_bypass \$http_upgrade;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
}
EOF

sudo tee /etc/nginx/sites-available/$DOMAIN2 > /dev/null <<EOF
server {
    listen 80;
    server_name $DOMAIN2;

    location / {
        proxy_pass http://localhost:$PORT2;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_cache_bypass \$http_upgrade;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
}
EOF

# ====================
# AKTIVIEREN UND TESTEN
# ====================

echo "👉 Aktiviere Beta-Sites..."
sudo ln -sf /etc/nginx/sites-available/$DOMAIN1 /etc/nginx/sites-enabled/
sudo ln -sf /etc/nginx/sites-available/$DOMAIN2 /etc/nginx/sites-enabled/

echo "👉 Teste NGINX-Konfiguration..."
sudo nginx -t
sudo systemctl reload nginx

# ====================
# HTTPS (Let's Encrypt)
# ====================

if [ "$ENABLE_HTTPS" = true ]; then
  echo "🔒 Installiere Certbot (falls noch nicht installiert) und richte HTTPS für Beta ein..."
  sudo apt install -y certbot python3-certbot-nginx
  sudo certbot --nginx \
    --non-interactive --agree-tos \
    --redirect \
    -m "$EMAIL" \
    -d "$DOMAIN1" -d "$DOMAIN2"
fi

echo "✅ Beta-Setup abgeschlossen. Deine Beta-Anwendungen sind jetzt erreichbar:"
echo "   🌐 Frontend: https://$DOMAIN2"
echo "   🔧 Backend:  https://$DOMAIN1"
