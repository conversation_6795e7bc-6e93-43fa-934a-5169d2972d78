module.exports = {
    apps: [
        {
            name: 'etv-backend-beta',
            cwd: './backend',
            script: "npm",
            args: 'start',
            node_args: '--no-warnings',
            env: {
                NODE_ENV: 'production',
                PORT: '1338',
                STRAPI_DISABLE_SOURCE_MAPS: 'true',
            },
            instances: 1,
            exec_mode: 'fork',
            watch: false,
            autorestart: true,
            max_memory_restart: '250M',
            log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
            combine_logs: true
        },
        {
            name: 'etv-frontend-beta',
            script: "npm",
            args: "start",
            cwd: './frontend',
            env: {
                NODE_ENV: 'production',
                PORT: 3001,
                NEXT_PUBLIC_STRAPI_URL: 'https://api.terminal.beta.eulektro.de'
            },
            instances: 1,
            exec_mode: 'fork',
            watch: false,
            max_memory_restart: '250M',
            log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
            combine_logs: true
        }
    ],

    deploy: {
        beta: {
            user: 'eulektro',
            host: 'terminal.eulektro.de',
            ref: 'origin/develop',
            repo: 'ssh://************************:30001/eulektro/etv.git',
            path: '/home/<USER>/terminal.beta.eulektro.de',
            "pre-setup": `echo "Setting up beta deployment directories"`,
            "post-setup": `echo "Beta setup complete"`,
            'pre-deploy-local': '',
            'post-deploy': "./deploy-beta.sh"
        }
    }
};
