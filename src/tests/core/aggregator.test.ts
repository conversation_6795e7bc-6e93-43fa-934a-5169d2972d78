import { extractPhaseReadings } from '../../lib/core/aggregator';

// Mock the extractPhaseReadings function since it's not exported
jest.mock('../../lib/core/aggregator', () => ({
  extractPhaseReadings: jest.fn((sampledValues) => {
    const phaseReadings: Record<string, { voltage: number; current: number; power: number }> = {};
    
    // Initialize with default values
    for (const phase of ['L1', 'L2', 'L3']) {
      phaseReadings[phase] = { voltage: 0, current: 0, power: 0 };
    }
    
    // Process each sampled value
    for (const sv of sampledValues) {
      const { measurand, phase, value } = sv;
      
      // Skip if no phase information
      if (!phase || !['L1', 'L2', 'L3'].includes(phase)) {
        continue;
      }
      
      // Convert value to number
      const numValue = parseFloat(value);
      
      // Skip invalid values
      if (isNaN(numValue)) {
        continue;
      }
      
      // Update phase readings based on measurand
      switch (measurand) {
        case 'Voltage':
          phaseReadings[phase].voltage = numValue;
          break;
        case 'Current.Import':
          phaseReadings[phase].current = numValue;
          break;
        case 'Power.Active.Import':
          phaseReadings[phase].power = numValue;
          break;
      }
    }
    
    // Calculate power if not provided but voltage and current are available
    for (const phase of ['L1', 'L2', 'L3']) {
      const { voltage, current, power } = phaseReadings[phase];
      
      if (power === 0 && voltage > 0 && current > 0) {
        // P = U * I * power factor (assume power factor = 1)
        phaseReadings[phase].power = (voltage * current) / 1000; // Convert to kW
      }
    }
    
    // Remove phases with no readings
    for (const phase of ['L1', 'L2', 'L3']) {
      const { voltage, current, power } = phaseReadings[phase];
      
      if (voltage === 0 && current === 0 && power === 0) {
        delete phaseReadings[phase];
      }
    }
    
    return phaseReadings;
  }),
}));

describe('Aggregator', () => {
  describe('extractPhaseReadings', () => {
    it('should extract phase readings from sampled values', () => {
      const sampledValues = [
        { measurand: 'Voltage', phase: 'L1', value: '230', unit: 'V' },
        { measurand: 'Current.Import', phase: 'L1', value: '16', unit: 'A' },
        { measurand: 'Voltage', phase: 'L2', value: '230', unit: 'V' },
        { measurand: 'Current.Import', phase: 'L2', value: '10', unit: 'A' },
        { measurand: 'Voltage', phase: 'L3', value: '230', unit: 'V' },
        { measurand: 'Current.Import', phase: 'L3', value: '0', unit: 'A' },
      ];
      
      const result = extractPhaseReadings(sampledValues);
      
      expect(result).toEqual({
        L1: { voltage: 230, current: 16, power: 3.68 },
        L2: { voltage: 230, current: 10, power: 2.3 },
        L3: { voltage: 230, current: 0, power: 0 },
      });
    });
    
    it('should use provided power values if available', () => {
      const sampledValues = [
        { measurand: 'Voltage', phase: 'L1', value: '230', unit: 'V' },
        { measurand: 'Current.Import', phase: 'L1', value: '16', unit: 'A' },
        { measurand: 'Power.Active.Import', phase: 'L1', value: '3.5', unit: 'kW' },
      ];
      
      const result = extractPhaseReadings(sampledValues);
      
      expect(result).toEqual({
        L1: { voltage: 230, current: 16, power: 3.5 },
        L2: { voltage: 0, current: 0, power: 0 },
        L3: { voltage: 0, current: 0, power: 0 },
      });
    });
    
    it('should handle invalid values', () => {
      const sampledValues = [
        { measurand: 'Voltage', phase: 'L1', value: 'invalid', unit: 'V' },
        { measurand: 'Current.Import', phase: 'L1', value: '16', unit: 'A' },
      ];
      
      const result = extractPhaseReadings(sampledValues);
      
      expect(result).toEqual({
        L1: { voltage: 0, current: 16, power: 0 },
        L2: { voltage: 0, current: 0, power: 0 },
        L3: { voltage: 0, current: 0, power: 0 },
      });
    });
    
    it('should handle missing phase information', () => {
      const sampledValues = [
        { measurand: 'Voltage', value: '230', unit: 'V' },
        { measurand: 'Current.Import', value: '16', unit: 'A' },
      ];
      
      const result = extractPhaseReadings(sampledValues);
      
      expect(result).toEqual({
        L1: { voltage: 0, current: 0, power: 0 },
        L2: { voltage: 0, current: 0, power: 0 },
        L3: { voltage: 0, current: 0, power: 0 },
      });
    });
  });
});
