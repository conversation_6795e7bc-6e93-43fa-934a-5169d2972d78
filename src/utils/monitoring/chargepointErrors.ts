import prisma from "~/server/db/prisma";
import { ChargePointError, MonitoringEvent } from "@prisma/client";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";
import { env } from "~/env";

export async function getChargepointErrors() {
  const errors: Pick<ChargePointError, "chargePointId" | "error">[] = await prisma.$queryRawUnsafe(`
        SELECT cpe.chargePointId,
               cpe.error
        FROM ChargePointError cpe
        WHERE cpe.createdAt = (SELECT MAX(createdAt) FROM ChargePointError)
          and error not like 'eth interface had 5 link%';
    `);

  return errors;
}

export async function checkZerokWh() {
  const events: Pick<MonitoringEvent, "evseId" | "lastevent">[] = await prisma.$queryRawUnsafe(`
      SELECT Start_datetime, CDR_ID, Charge_Point_ID as 'evseId', Charge_Point_Address, count(*) as 'Muster gefunden', UNIX_TIMESTAMP(max(End_datetime)) as "lastevent"
      FROM (
               SELECT
                   t.*,
                   SUM(volume = 0) OVER (PARTITION BY Charge_Point_ID ORDER BY End_datetime ROWS BETWEEN 3 PRECEDING AND 1 PRECEDING) AS prev_3_zero_count
               FROM Cdr t
           ) subquery
      WHERE subquery.volume = 0 AND subquery.prev_3_zero_count = 3 and Date(Start_datetime) > CURDATE() - 3 group by Charge_Point_ID;
    `);

  const entries = events.map((event) => {
    return {
      message: "0 kWh Event detected",
      evseId: event.evseId,
      type: "0_KWH",
      lastevent: event.lastevent,
    };
  });

  await prisma.monitoringEvent.createMany({ data: entries, skipDuplicates: true });
}

export async function checkAdhocPaymentIntents() {
  Logger(
    "Checking Adhoc CDRs for missing PaymentIntents",
    "Error Monitoring",
    "cron",
    LogType.INFO,
  );

  // Find CDRs with TariffName "Adhoc" or "Eulektro Adhoc" from the last 7 days

  const cdrs: { CDR_ID: string; Charge_Point_ID: string; End_datetime: Date }[] =
    await prisma.$queryRawUnsafe(`
    SELECT c.CDR_ID, c.Charge_Point_ID, c.End_datetime
    FROM Cdr c
    LEFT JOIN PaymentIntent pi ON c.CDR_ID = pi.cdrId
    WHERE c.Tariff_Name IN ('Adhoc', 'Eulektro Adhoc')
      AND c.End_datetime >= DATE_SUB(NOW(), INTERVAL 2 DAY)
      AND pi.id IS NULL
  `);

  Logger(
    `Found ${cdrs.length} Adhoc CDRs without PaymentIntents`,
    "Error Monitoring",
    "cron",
    LogType.INFO,
  );

  if (cdrs.length === 0) {
    return;
  }

  // Create monitoring events for each CDR without a PaymentIntent
  const entries = cdrs.map((cdr) => {
    return {
      message: `Adhoc CDR without PaymentIntent (${cdr.CDR_ID})`,
      evseId: cdr.Charge_Point_ID || "unknown",
      type: "ADHOC_NOT_CAPTURED",
      href: `${env.LONGSHIP_PORTAL_URL}/cdrs/${cdr.CDR_ID}`,
      lastevent: BigInt(Math.floor(cdr.End_datetime.getTime() / 1000)), // Convert to UNIX timestamp
    };
  });

  await prisma.monitoringEvent.createMany({ data: entries, skipDuplicates: true });
  Logger(
    `Created ${entries.length} monitoring events for Adhoc CDRs without PaymentIntents`,
    "Error Monitoring",
    "cron",
    LogType.INFO,
  );
}
