import Link from 'next/link';
import { prisma } from '@/lib/db/prisma';
import { getOCPPAdapter } from '@/lib/ocpp/adapter';
import { formatDistanceToNow } from 'date-fns';
import { getLatestPhaseReadings } from '@/lib/core/aggregator';

async function getChargePoints() {
  const chargePoints = await prisma.chargePoint.findMany({
    include: {
      group: {
        select: {
          id: true,
          name: true,
        },
      },
    },
  });

  const ocppAdapter = getOCPPAdapter();

  // Enhance charge points with additional data
  const enhancedChargePoints = await Promise.all(chargePoints.map(async (cp) => {
    // Get connection status
    const isConnected = ocppAdapter.isChargePointConnected(cp.id);

    // Get latest phase readings
    const phaseReadings = await getLatestPhaseReadings(cp.id);

    // Calculate current load
    const totalPower = phaseReadings.reduce((sum, reading) => sum + (reading?.power || 0), 0);

    // Parse connectorIds from JSON
    const connectorIds = typeof cp.connectorIds === 'string'
      ? JSON.parse(cp.connectorIds)
      : Array.isArray(cp.connectorIds)
        ? cp.connectorIds
        : Object.values(cp.connectorIds || {});

    // Format last heartbeat
    const lastHeartbeatFormatted = cp.lastHeartbeat
      ? formatDistanceToNow(new Date(cp.lastHeartbeat), { addSuffix: true })
      : 'Never';

    return {
      ...cp,
      status: isConnected ? 'Connected' : 'Disconnected',
      connectors: connectorIds.length,
      currentLoad: `${totalPower.toFixed(1)} kW`,
      lastHeartbeat: lastHeartbeatFormatted
    };
  }));

  return enhancedChargePoints;
}

export default async function ChargePointsPage() {
  const chargePoints = await getChargePoints();

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold">Charge Points</h1>
        <Link
          href="/chargepoints/new"
          className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 transition-colors"
        >
          Add Charge Point
        </Link>
      </div>

      <div className="bg-white shadow overflow-hidden rounded-lg">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  ID / Name
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Group
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Connectors
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Current Load
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Last Heartbeat
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {chargePoints.map((cp) => (
                <tr key={cp.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <Link href={`/chargepoints/${cp.id}`} className="text-primary-600 font-medium hover:text-primary-800">
                        {cp.name}
                      </Link>
                      <p className="text-sm text-gray-500">{cp.id}</p>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <Link href={`/groups/${cp.group === 'Main Building' ? 'demo-1' : 'demo-2'}`} className="text-primary-600 hover:text-primary-800">
                      {cp.group}
                    </Link>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                      cp.status === 'Charging' ? 'bg-green-100 text-green-800' :
                      cp.status === 'Available' ? 'bg-blue-100 text-blue-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {cp.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {cp.connectors}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {cp.currentLoad}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {cp.lastHeartbeat}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
