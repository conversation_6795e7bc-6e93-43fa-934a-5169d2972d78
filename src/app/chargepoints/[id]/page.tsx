import Link from 'next/link';
import { notFound } from 'next/navigation';
import { prisma } from '@/lib/db/prisma';
import { getOCPPAdapter } from '@/lib/ocpp/adapter';
import { formatDistanceToNow } from 'date-fns';
import { getLatestPhaseReadings } from '@/lib/core/aggregator';

async function getChargePoint(id: string) {
  // Get charge point from database
  const chargePoint = await prisma.chargePoint.findUnique({
    where: { id },
    include: {
      group: {
        select: {
          id: true,
          name: true,
        },
      },
      chargingProfiles: true,
    },
  });

  if (!chargePoint) {
    return null;
  }

  // Get OCPP connection status
  const ocppAdapter = getOCPPAdapter();
  const isConnected = ocppAdapter.isChargePointConnected(id);

  // Get latest phase readings
  const phaseReadings = await getLatestPhaseReadings(id);

  // Format phase readings for display
  const formattedPhaseReadings = phaseReadings.map(reading => ({
    phase: reading.phase,
    voltage: `${reading.voltage}V`,
    current: `${reading.current}A`,
    power: `${reading.power} kW`,
  }));

  // Parse connectorIds from JSON
  const connectorIds = typeof chargePoint.connectorIds === 'string'
    ? JSON.parse(chargePoint.connectorIds)
    : Array.isArray(chargePoint.connectorIds)
      ? chargePoint.connectorIds
      : Object.values(chargePoint.connectorIds || {});

  // Create connector objects
  const connectors = connectorIds.map((id: number) => ({
    id,
    status: isConnected ? 'Available' : 'Unavailable',
    currentUser: null,
    startTime: null,
    duration: null,
    energy: null,
    power: null,
  }));

  // Format last heartbeat
  const lastHeartbeat = chargePoint.lastHeartbeat
    ? formatDistanceToNow(new Date(chargePoint.lastHeartbeat), { addSuffix: true })
    : 'Never';

  // Get charging profile if available
  const chargingProfile = chargePoint.chargingProfiles.length > 0
    ? {
        ...chargePoint.chargingProfiles[0],
        profileJson: typeof chargePoint.chargingProfiles[0].profileJson === 'string'
          ? JSON.parse(chargePoint.chargingProfiles[0].profileJson)
          : chargePoint.chargingProfiles[0].profileJson
      }
    : null;

  return {
    ...chargePoint,
    lastHeartbeat,
    status: isConnected ? 'Connected' : 'Disconnected',
    connectors,
    phaseReadings: formattedPhaseReadings.length > 0 ? formattedPhaseReadings : [
      { phase: 'L1', voltage: '0V', current: '0A', power: '0 kW' },
      { phase: 'L2', voltage: '0V', current: '0A', power: '0 kW' },
      { phase: 'L3', voltage: '0V', current: '0A', power: '0 kW' },
    ],
    chargingProfile: chargingProfile?.profileJson || null,
  };
}

export default async function ChargePointDetailPage({ params }: { params: { id: string } }) {
  const chargePointId = params.id;
  const chargePoint = await getChargePoint(chargePointId);

  if (!chargePoint) {
    notFound();
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <div>
          <Link href="/chargepoints" className="text-primary-600 hover:text-primary-800">
            ← Back to Charge Points
          </Link>
          <h1 className="text-2xl font-semibold mt-2">{chargePoint.name}</h1>
          <p className="text-gray-500">ID: {chargePoint.id}</p>
        </div>
        <div className="flex space-x-3">
          <Link
            href={`/chargepoints/${chargePointId}/edit`}
            className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
          >
            Edit
          </Link>
          <button
            className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 transition-colors"
          >
            Send Command
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div className="bg-white shadow rounded-lg p-6">
          <h3 className="text-lg font-medium mb-4">Status</h3>
          <div className="flex items-center mb-4">
            <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
              chargePoint.status === 'Charging' ? 'bg-green-100 text-green-800' :
              chargePoint.status === 'Available' ? 'bg-blue-100 text-blue-800' :
              'bg-red-100 text-red-800'
            }`}>
              {chargePoint.status}
            </span>
            <span className="ml-2 text-sm text-gray-500">Last heartbeat: {chargePoint.lastHeartbeat}</span>
          </div>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-sm text-gray-500">Group:</span>
              <Link href={`/groups/${chargePoint.group.id}`} className="text-sm text-primary-600 hover:text-primary-800">
                {chargePoint.group.name}
              </Link>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-500">Model:</span>
              <span className="text-sm">{chargePoint.model}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-500">Vendor:</span>
              <span className="text-sm">{chargePoint.vendor}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-500">Firmware:</span>
              <span className="text-sm">{chargePoint.firmwareVersion}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-500">OCPP Version:</span>
              <span className="text-sm">{chargePoint.ocppVersion}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-500">IP Address:</span>
              <span className="text-sm">{chargePoint.ipAddress}</span>
            </div>
          </div>
        </div>

        <div className="bg-white shadow rounded-lg p-6 md:col-span-2">
          <h3 className="text-lg font-medium mb-4">Phase Readings</h3>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Phase
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Voltage
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Current
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Power
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {chargePoint.phaseReadings.map((reading, index) => (
                  <tr key={index}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {reading.phase}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {reading.voltage}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {reading.current}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {reading.power}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
          <div className="mt-4 h-48 flex items-center justify-center bg-gray-100 rounded">
            <p className="text-gray-500">Phase readings chart will be displayed here</p>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <div className="bg-white shadow rounded-lg p-6">
          <h3 className="text-lg font-medium mb-4">Connectors</h3>
          <div className="space-y-4">
            {chargePoint.connectors.map((connector) => (
              <div key={connector.id} className="border rounded-lg p-4">
                <div className="flex justify-between items-center mb-2">
                  <h4 className="font-medium">Connector {connector.id}</h4>
                  <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                    connector.status === 'Charging' ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800'
                  }`}>
                    {connector.status}
                  </span>
                </div>
                {connector.status === 'Charging' ? (
                  <div className="space-y-1">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-500">User:</span>
                      <span className="text-sm">{connector.currentUser}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-500">Start time:</span>
                      <span className="text-sm">{connector.startTime}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-500">Duration:</span>
                      <span className="text-sm">{connector.duration}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-500">Energy:</span>
                      <span className="text-sm">{connector.energy}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-500">Power:</span>
                      <span className="text-sm">{connector.power}</span>
                    </div>
                  </div>
                ) : (
                  <p className="text-sm text-gray-500">No active charging session</p>
                )}
              </div>
            ))}
          </div>
        </div>

        <div className="bg-white shadow rounded-lg p-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium">Charging Profile</h3>
            <button className="text-sm text-primary-600 hover:text-primary-800">
              Update Profile
            </button>
          </div>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-sm text-gray-500">Profile ID:</span>
              <span className="text-sm">{chargePoint.chargingProfile.id}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-500">Stack Level:</span>
              <span className="text-sm">{chargePoint.chargingProfile.stackLevel}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-500">Purpose:</span>
              <span className="text-sm">{chargePoint.chargingProfile.chargingProfilePurpose}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-500">Kind:</span>
              <span className="text-sm">{chargePoint.chargingProfile.chargingProfileKind}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-500">Rate Unit:</span>
              <span className="text-sm">{chargePoint.chargingProfile.chargingSchedule.chargingRateUnit}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-gray-500">Min Rate:</span>
              <span className="text-sm">{chargePoint.chargingProfile.chargingSchedule.minChargingRate}</span>
            </div>
          </div>
          <div className="mt-4">
            <h4 className="text-sm font-medium mb-2">Schedule Periods</h4>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Start Period
                    </th>
                    <th scope="col" className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Limit
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {chargePoint.chargingProfile.chargingSchedule.chargingSchedulePeriod.map((period, index) => (
                    <tr key={index}>
                      <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-500">
                        {period.startPeriod}
                      </td>
                      <td className="px-4 py-2 whitespace-nowrap text-sm text-gray-500">
                        {period.limit} {chargePoint.chargingProfile.chargingSchedule.chargingRateUnit}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-white shadow rounded-lg p-6">
        <h3 className="text-lg font-medium mb-4">Transaction History</h3>
        <div className="h-64 flex items-center justify-center bg-gray-100 rounded">
          <p className="text-gray-500">Transaction history will be displayed here</p>
        </div>
      </div>
    </div>
  );
}
