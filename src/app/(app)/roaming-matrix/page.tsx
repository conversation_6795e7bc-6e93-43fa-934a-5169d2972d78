"use client";

import React, { useState, useEffect } from "react";
import { FaChevronDown, FaChevronRight } from "react-icons/fa";
import { Chip } from "~/app/(app)/component/chip";

interface TariffData {
  id: string;
  name: string;
  kwh: number;
  sessionFee: number;
  kindOfTarif: string;
  validFrom: string;
  validTo: string;
  validOus: Array<{
    id: string;
    name: string;
    code: string;
  }>;
}

interface RoamingMatrixData {
  id: string;
  name: string;
  companyName: string;
  customerNumber: string;
  supplierNumber: string;
  cpo: boolean;
  noRoaming: boolean;
  ou: {
    id: string;
    name: string;
    code: string;
  } | null;
  acTariff: TariffData | null;
  dcTariff: TariffData | null;
  validCreditTariffs: Array<{
    id: string;
    name: string;
    tarifType: string;
    powerType: string;
    sessionCredit: number;
    energyCredit: number;
    kindOfTarif: string;
    validFrom: string;
    validTo: string | null;
  }>;
}

const RoamingMatrixPage = () => {
  const [data, setData] = useState<RoamingMatrixData[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchRoamingMatrixData();
  }, []);

  const fetchRoamingMatrixData = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/roaming-matrix");
      if (response.ok) {
        const data = await response.json();
        setData(data);
      } else {
        console.error("Failed to fetch roaming matrix data");
      }
    } catch (error) {
      console.error("Error fetching roaming matrix data:", error);
    } finally {
      setLoading(false);
    }
  };

  // Generate color for tariff based on tariff ID
  const getTariffColor = (tariffId: string | null, type: "AC" | "DC") => {
    if (!tariffId) return "gray";

    // Create a simple hash from tariff ID to get consistent colors
    let hash = 0;
    for (let i = 0; i < tariffId.length; i++) {
      hash = tariffId.charCodeAt(i) + ((hash << 5) - hash);
    }

    const colors = [
      "red", "blue", "green", "yellow", "purple", "pink",
      "indigo", "orange", "teal", "cyan", "lime", "amber"
    ];

    return colors[Math.abs(hash) % colors.length];
  };

  const ContactItem = ({ contact }: { contact: ContactData }) => {
    return (
      <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 border border-gray-200 dark:border-gray-600">
        <div className="flex items-center justify-between">
          <div>
            <h4 className="font-medium text-gray-900 dark:text-white">
              {contact.companyName || contact.name || "Unbekannter Contact"}
            </h4>
            <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400 mt-1">
              {contact.customerNumber && (
                <span>Kunde: {contact.customerNumber}</span>
              )}
              {contact.ou && (
                <span>OU: {contact.ou.name}</span>
              )}
            </div>
          </div>
          <div className="flex items-center space-x-2">
            {contact.cpo && (
              <Chip label="CPO" className="bg-purple-100 text-purple-800 text-xs" />
            )}
            {contact.noRoaming && (
              <Chip label="Kein Roaming" className="bg-red-100 text-red-800 text-xs" />
            )}
          </div>
        </div>
      </div>
    );
  };

  const TariffGroup = ({ tariff, type }: { tariff: TariffGroup, type: "AC" | "DC" }) => {
    const tariffKey = `${tariff.id}-${type}`;
    const isExpanded = expandedTariffs.has(tariffKey);
    const typeColor = type === "AC" ? "blue" : "green";

    return (
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 mb-4">
        {/* Tariff Header */}
        <div
          className="flex items-center justify-between p-4 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
          onClick={() => toggleTariffExpanded(tariffKey)}
        >
          <div className="flex items-center space-x-4">
            <div className="flex-shrink-0">
              {isExpanded ? (
                <FaChevronDown className="text-gray-400" />
              ) : (
                <FaChevronRight className="text-gray-400" />
              )}
            </div>
            <div>
              <div className="flex items-center space-x-3">
                <span className={`bg-${typeColor}-100 text-${typeColor}-800 px-2 py-1 rounded text-sm font-medium`}>
                  {type}
                </span>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  {tariff.name}
                </h3>
              </div>
              <div className="flex items-center space-x-6 text-sm text-gray-500 dark:text-gray-400 mt-1">
                <span className="font-medium">
                  kWh: <span className="text-gray-900 dark:text-white">{tariff.kwh.toFixed(4)}€</span>
                </span>
                <span className="font-medium">
                  Session: <span className="text-gray-900 dark:text-white">{tariff.sessionFee?.toFixed(2) || '0.00'}€</span>
                </span>
                <span className="font-medium">
                  Art: <span className="text-gray-900 dark:text-white">{tariff.kindOfTarif}</span>
                </span>
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Chip
              label={`${tariff.assignedContacts.length} EMPs`}
              className={`bg-${typeColor}-100 text-${typeColor}-800 text-sm font-medium`}
            />
          </div>
        </div>

        {/* Expanded Content - Assigned Contacts */}
        {isExpanded && (
          <div className="border-t border-gray-200 dark:border-gray-700 p-4">
            <h4 className="text-md font-semibold text-gray-900 dark:text-white mb-3">
              Zugeordnete EMPs ({tariff.assignedContacts.length})
            </h4>
            <div className="space-y-2">
              {tariff.assignedContacts.map((contact) => (
                <ContactItem key={contact.id} contact={contact} />
              ))}
            </div>

            {/* Tariff Details */}
            <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-600">
              <h5 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Tarif-Details</h5>
              <div className="grid grid-cols-2 gap-2 text-sm">
                <div>
                  <span className="font-medium text-gray-600 dark:text-gray-300">Gültig von:</span>
                  <span className="ml-2">{new Date(tariff.validFrom).toLocaleDateString()}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-600 dark:text-gray-300">Gültig bis:</span>
                  <span className="ml-2">{new Date(tariff.validTo).toLocaleDateString()}</span>
                </div>
              </div>
              {tariff.validOus && tariff.validOus.length > 0 && (
                <div className="mt-2">
                  <span className="font-medium text-gray-600 dark:text-gray-300">Beschränkt auf OUs:</span>
                  <div className="mt-1 flex flex-wrap gap-1">
                    {tariff.validOus.map((ou) => (
                      <Chip
                        key={ou.id}
                        label={`${ou.name} (${ou.code})`}
                        className="bg-orange-100 text-orange-800 text-xs"
                      />
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    );
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
            Roaming Matrix
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            Übersicht aller Contacts mit ihren gültigen zugeordneten Tarifen
          </p>
        </div>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          Roaming Matrix
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mt-2">
          Übersicht aller Tarife mit ihren zugeordneten EMPs (Contacts)
        </p>
        {data && (
          <div className="mt-4 flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
            <span>{data.totalContacts} Contacts gefunden</span>
            <span>•</span>
            <span>{data.totalTariffs} Tarife</span>
            <span>•</span>
            <span>{expandedTariffs.size} aufgeklappt</span>
            <button
              onClick={() => {
                const allTariffKeys = [
                  ...data.acTariffs.map(t => `${t.id}-AC`),
                  ...data.dcTariffs.map(t => `${t.id}-DC`)
                ];
                setExpandedTariffs(new Set(allTariffKeys));
              }}
              className="text-primary hover:underline"
            >
              Alle aufklappen
            </button>
            <button
              onClick={() => setExpandedTariffs(new Set())}
              className="text-primary hover:underline"
            >
              Alle zuklappen
            </button>
          </div>
        )}
      </div>

      <div className="space-y-6">
        {!data || (data.acTariffs.length === 0 && data.dcTariffs.length === 0) ? (
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 p-8 text-center">
            <div className="text-gray-400 text-lg mb-2">🌐</div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              Keine Tarife in der Roaming Matrix
            </h3>
            <p className="text-gray-500 dark:text-gray-400">
              Es wurden noch keine Contacts für die Roaming Matrix markiert oder keine gültigen Tarife gefunden.
              Bearbeiten Sie Contacts und setzen Sie das Flag "Sichtbar in Roaming Matrix".
            </p>
          </div>
        ) : (
          <>
            {/* AC Tarife Section */}
            {data.acTariffs.length > 0 && (
              <div>
                <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-4 flex items-center">
                  <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded text-lg font-medium mr-3">AC</span>
                  Wechselstrom-Tarife ({data.acTariffs.length})
                </h2>
                <div className="space-y-4">
                  {data.acTariffs.map((tariff) => (
                    <TariffGroup key={`${tariff.id}-AC`} tariff={tariff} type="AC" />
                  ))}
                </div>
              </div>
            )}

            {/* DC Tarife Section */}
            {data.dcTariffs.length > 0 && (
              <div>
                <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-4 flex items-center">
                  <span className="bg-green-100 text-green-800 px-3 py-1 rounded text-lg font-medium mr-3">DC</span>
                  Gleichstrom-Tarife ({data.dcTariffs.length})
                </h2>
                <div className="space-y-4">
                  {data.dcTariffs.map((tariff) => (
                    <TariffGroup key={`${tariff.id}-DC`} tariff={tariff} type="DC" />
                  ))}
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default RoamingMatrixPage;
