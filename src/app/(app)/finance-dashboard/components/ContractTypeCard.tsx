"use client";

import React, { useState, useMemo } from 'react';
import { formatEuro } from '~/utils/format/numbers';
import type { FinanceDashboardInvoice } from '~/app/api/finance-dashboard/route';
import InvoiceTable from './InvoiceTable';

interface ContractTypeCardProps {
  title: string;
  icon: string;
  totalAmount: number;
  invoices: FinanceDashboardInvoice[];
  color: string;
}

const ContractTypeCard: React.FC<ContractTypeCardProps> = ({
  title,
  icon,
  totalAmount,
  invoices,
  color
}) => {
  return (
    <div className="mb-6 w-full">
      <div className="relative flex min-w-0 flex-col break-words rounded-md bg-white bg-clip-border shadow-md-all dark:bg-gray-950 dark:shadow-soft-dark-xl">
        {/* Header */}
        <div className="flex items-center justify-between rounded-t-md p-4 pb-2">
          <div className="flex items-center">
            <span className="mr-3 text-2xl">{icon}</span>
            <div>
              <h6 className="mb-0 text-lg font-semibold text-primary dark:text-white">
                {title}
              </h6>
              <p className="mb-0 text-sm text-gray-600 dark:text-gray-400">
                {invoices.length} offene Rechnung{invoices.length !== 1 ? 'en' : ''}
              </p>
            </div>
          </div>
          <div className="text-right">
            <h4 className={`mb-0 font-bold ${color}`}>
              {formatEuro(totalAmount)}
            </h4>
          </div>
        </div>

        {/* Content */}
        <div className="flex-auto p-4 pt-0">
          {invoices.length > 0 ? (
            <InvoiceTable invoices={invoices} />
          ) : (
            <div className="py-8 text-center text-gray-500 dark:text-gray-400">
              <p>Keine offenen Rechnungen</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ContractTypeCard;
