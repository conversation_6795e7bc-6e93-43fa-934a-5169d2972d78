"use client";

import React, { useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, FaExclamation<PERSON>ircle, FaSearch } from "react-icons/fa";
import { ApiResponse } from "~/types/api/apiType";

interface SingleOuAggregationCommandProps {
  onStatusUpdate: (commandName: string, status: any) => void;
}

const SingleOuAggregationCommand = ({ onStatusUpdate }: SingleOuAggregationCommandProps) => {
  const [ouCode, setOuCode] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [response, setResponse] = useState<ApiResponse<any> | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [missingMonthsData, setMissingMonthsData] = useState<any>(null);
  const [showMissingMonths, setShowMissingMonths] = useState(false);
  const [availableOus, setAvailableOus] = useState<any[]>([]);
  const [showOuList, setShowOuList] = useState(false);

  const loadAvailableOus = async () => {
    try {
      setIsLoading(true);
      const response = await fetch("/api/forecast/ou-list");

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      setAvailableOus(result.data.ous);
      setShowOuList(true);
    } catch (err) {
      console.error("Error loading OUs:", err);
      setError(err instanceof Error ? err.message : "Unknown error occurred");
    } finally {
      setIsLoading(false);
    }
  };

  const checkMissingMonths = async () => {
    if (!ouCode.trim()) {
      alert("Bitte geben Sie einen OU-Code ein");
      return;
    }

    try {
      setIsLoading(true);
      const response = await fetch(`/api/forecast/missing-months?ouCode=${encodeURIComponent(ouCode.trim())}`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      setMissingMonthsData(result.data);
      setShowMissingMonths(true);
    } catch (err) {
      console.error("Error checking missing months:", err);
      setError(err instanceof Error ? err.message : "Unknown error occurred");
    } finally {
      setIsLoading(false);
    }
  };

  const executeAggregation = async () => {
    if (!ouCode.trim()) {
      alert("Bitte geben Sie einen OU-Code ein");
      return;
    }

    try {
      setIsLoading(true);
      setError(null);
      setResponse(null);

      const response = await fetch("/api/forecast/aggregate-single-ou", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ouCode: ouCode.trim(),
        }),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json() as ApiResponse<any>;
      setResponse(result);
      
      // Update parent component status
      onStatusUpdate("Aggregate Single OU Forecast", {
        isLoading: false,
        response: result,
        error: null,
      });

      // Refresh missing months data if it was shown
      if (showMissingMonths) {
        await checkMissingMonths();
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Error executing aggregation";
      setError(errorMessage);
      
      const errorResponse: ApiResponse<any> = {
        status: "error",
        message: errorMessage,
        errorCode: errorMessage,
        errorDetails: err?.toString(),
      };
      
      setResponse(errorResponse);
      onStatusUpdate("Aggregate Single OU Forecast", {
        isLoading: false,
        response: errorResponse,
        error: errorMessage,
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <tr className="hover:bg-gray-100">
      <td className="border-b border-gray-200 bg-white px-5 py-2 text-sm">
        <div className="space-y-2">
          <div className="font-medium">Aggregate Single OU Forecast</div>
          <div className="flex items-center space-x-2">
            <input
              type="text"
              placeholder="OU Code (z.B. 0001)"
              value={ouCode}
              onChange={(e) => setOuCode(e.target.value)}
              className="rounded border border-gray-300 px-2 py-1 text-sm focus:border-blue-500 focus:outline-none"
              disabled={isLoading}
            />
            <button
              onClick={loadAvailableOus}
              disabled={isLoading}
              className="flex items-center space-x-1 rounded bg-gray-500 px-2 py-1 text-xs text-white hover:bg-gray-600 disabled:opacity-50"
              title="Verfügbare OUs anzeigen"
            >
              <span>OUs</span>
            </button>
            <button
              onClick={checkMissingMonths}
              disabled={isLoading || !ouCode.trim()}
              className="flex items-center space-x-1 rounded bg-blue-500 px-2 py-1 text-xs text-white hover:bg-blue-600 disabled:opacity-50"
            >
              <FaSearch size={10} />
              <span>Check</span>
            </button>
          </div>
        </div>
      </td>
      <td className="border-b border-gray-200 bg-white px-5 py-2 text-sm">
        <div className="space-y-1">
          <div>Aggregate monthly forecast data for a specific OU. Skips existing data except current month.</div>
          {showOuList && availableOus.length > 0 && (
            <div className="mt-2 rounded bg-blue-50 p-2 text-xs">
              <div className="font-medium mb-1">Verfügbare OUs:</div>
              <div className="grid grid-cols-3 gap-1 max-h-20 overflow-y-auto">
                {availableOus.slice(0, 12).map((ou) => (
                  <button
                    key={ou.code}
                    onClick={() => {setOuCode(ou.code); setShowOuList(false);}}
                    className="text-left hover:bg-blue-100 p-1 rounded text-xs"
                    title={`${ou.name} - ${ou.aggregationStatus.completionPercentage}% aggregiert`}
                  >
                    {ou.code} ({ou.aggregationStatus.completionPercentage}%)
                  </button>
                ))}
              </div>
              {availableOus.length > 12 && (
                <div className="text-gray-500 mt-1">...und {availableOus.length - 12} weitere</div>
              )}
            </div>
          )}
          {missingMonthsData && showMissingMonths && (
            <div className="mt-2 rounded bg-gray-50 p-2 text-xs">
              <div className="font-medium">OU {missingMonthsData.ouCode} Status:</div>
              <div>Existing: {missingMonthsData.existingMonths}/{missingMonthsData.totalExpectedMonths} months</div>
              <div>Missing: {missingMonthsData.missingMonths} months</div>
              {missingMonthsData.missingMonths > 0 && (
                <div className="text-orange-600">
                  Missing months: {missingMonthsData.missingMonthsList.slice(0, 5).join(", ")}
                  {missingMonthsData.missingMonthsList.length > 5 && "..."}
                </div>
              )}
            </div>
          )}
        </div>
      </td>
      <td className="border-b border-gray-200 bg-white px-5 py-2 text-sm">
        <button
          onClick={executeAggregation}
          disabled={isLoading || !ouCode.trim()}
          className="hover:text-blue-800 disabled:opacity-50"
        >
          <FaPlay />
        </button>
      </td>
      <td className="border-b border-gray-200 bg-white px-5 py-2 text-sm">
        {isLoading ? (
          <FaSpinner className="animate-spin" />
        ) : response ? (
          response.status === "success" ? (
            <FaCheck className="text-green-500" />
          ) : (
            <FaExclamationCircle className="text-red-500" />
          )
        ) : null}
      </td>
    </tr>
  );
};

export default SingleOuAggregationCommand;
