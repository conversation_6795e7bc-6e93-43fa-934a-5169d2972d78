"use client";

import React, { useState, useEffect } from "react";
import Card from "~/component/card";
import RealtimeWidget from "~/app/(app)/component/RealtimeWidget";
import MonthlyForecastWrapper from "./MonthlyForecastWrapper";
import { MdAttachMoney } from "react-icons/md";
import { formatEuro, formatKwh } from "~/utils/format/numbers";
import { userStore } from "~/server/zustand/store";

interface ForecastData {
  thgUntilNow: number;
  energyGrossMarginUntilNow: number;
  kWhUntilNow: number;
  thgCurrentYear: number;
  energyGrossMarginCurrentYear: number;
  grossMarginCurrentYear: number;
  kWhCurrentYear: number;
  thgUntilNowMonth: number;
  energyGrossMarginUntilNowMonth: number;
  kWhUntilNowMonth: number;
  thgCurrentMonth: number;
  energyGrossMarginCurrentMonth: number;
  grossMarginCurrentMonth: number;
  kWhCurrentMonth: number;
}

const ForecastClientWrapper = () => {
  const [forecastData, setForecastData] = useState<ForecastData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Subscribe to OU changes
  const { selectedOuId, selectedOuName } = userStore();

  useEffect(() => {
    const fetchForecastData = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const response = await fetch("/api/forecast/current-data");
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const result = await response.json();
        
        if (result.status === "success") {
          setForecastData(result.data);
        } else {
          throw new Error(result.message || "Failed to fetch data");
        }
      } catch (err) {
        console.error("Error fetching forecast data:", err);
        setError(err instanceof Error ? err.message : "Unknown error occurred");
      } finally {
        setLoading(false);
      }
    };

    fetchForecastData();
  }, [selectedOuId]); // Re-fetch when OU changes

  if (loading) {
    return (
      <div className="space-y-5">
        <Card className="mt-5">
          <div className="flex items-center justify-center p-8">
            <span>Lade Forecast-Daten...</span>
          </div>
        </Card>
      </div>
    );
  }

  if (error || !forecastData) {
    return (
      <div className="space-y-5">
        <Card className="mt-5">
          <div className="flex items-center justify-center p-8 text-red-600">
            <span>Fehler beim Laden der Daten: {error || "Unbekannter Fehler"}</span>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <>
      <Card
        className={"mt-5"}
        header_left={
          <h2 className={"mb-0 mb-2 font-bold dark:text-white"}>
            Hochrechnung {new Date().getFullYear()}
          </h2>
        }
      >
        <div className={"flex w-full flex-col gap-1  sm:flex-row sm:gap-2"}>
          <RealtimeWidget
            caption={"Energie Marge"}
            loading={false}
            icon={<MdAttachMoney size={21} />}
            primaryValue={formatEuro(forecastData.energyGrossMarginCurrentYear ?? 0)}
          >
            <span className={"text-xs"}>
              <span className={"font-bold"}>Hochrechnung</span> der Marge von Ein- und Verkauf,
              basierend auf den hinterlegten Stromverträgen und bereits erfassten Ladevorgängen
            </span>
          </RealtimeWidget>
          <RealtimeWidget
            caption={"THG"}
            loading={false}
            icon={<MdAttachMoney size={21} />}
            primaryValue={formatEuro(forecastData.thgCurrentYear ?? 0)}
          >
            <span className={"text-xs"}>
              <span className={"font-bold"}>Hochrechnung</span> der THG Erlöse der bereits erfassten
              Ladevorgänge unter Annahme von 5ct/kWh
            </span>
          </RealtimeWidget>
          <RealtimeWidget
            caption={"Energie"}
            loading={false}
            icon={<MdAttachMoney size={21} />}
            primaryValue={formatKwh(forecastData.kWhCurrentYear ?? 0)}
          >
            <span className={"text-xs"}>
              <span className={"font-bold"}>Hochrechnung</span> der Energie aller bereits erfassten
              Ladevorgängen
            </span>
          </RealtimeWidget>
        </div>
      </Card>
      <Card
        className={"mt-5"}
        header_left={
          <h2 className={"mb-0 mb-2 font-bold dark:text-white"}>
            Hochrechnung {new Date().toLocaleString("de-DE", { month: "long", year: "numeric" })}
          </h2>
        }
      >
        <div className={"flex w-full flex-col gap-1  sm:flex-row sm:gap-2"}>
          <RealtimeWidget
            caption={"Energie Marge"}
            loading={false}
            icon={<MdAttachMoney size={21} />}
            primaryValue={formatEuro(forecastData.energyGrossMarginCurrentMonth ?? 0)}
          >
            <span className={"text-xs"}>
              <span className={"font-bold"}>Hochrechnung</span> der Marge von Ein- und Verkauf für den aktuellen Monat,
              basierend auf den hinterlegten Stromverträgen und bereits erfassten Ladevorgängen
            </span>
          </RealtimeWidget>
          <RealtimeWidget
            caption={"THG"}
            loading={false}
            icon={<MdAttachMoney size={21} />}
            primaryValue={formatEuro(forecastData.thgCurrentMonth ?? 0)}
          >
            <span className={"text-xs"}>
              <span className={"font-bold"}>Hochrechnung</span> der THG Erlöse für den aktuellen Monat der bereits erfassten
              Ladevorgänge unter Annahme von 5ct/kWh
            </span>
          </RealtimeWidget>
          <RealtimeWidget
            caption={"Energie"}
            loading={false}
            icon={<MdAttachMoney size={21} />}
            primaryValue={formatKwh(forecastData.kWhCurrentMonth ?? 0)}
          >
            <span className={"text-xs"}>
              <span className={"font-bold"}>Hochrechnung</span> der Energie für den aktuellen Monat aller bereits erfassten
              Ladevorgängen
            </span>
          </RealtimeWidget>
        </div>
      </Card>
      <Card
        className={"mt-5"}
        header_left={
          <h2 className={"mb-0 mb-2 font-bold dark:text-white"}>Aktuelles Jahr bis heute</h2>
        }
      >
        <div className={"flex w-full flex-col gap-1  sm:flex-row sm:gap-2"}>
          <RealtimeWidget
            caption={"Energie Marge"}
            loading={false}
            icon={<MdAttachMoney size={21} />}
            primaryValue={formatEuro(forecastData.energyGrossMarginUntilNow ?? 0)}
          >
            <span className={"text-xs"}>
              Marge von Ein- und Verkauf, basierend auf den hinterlegten Stromverträgen und bereits
              erfassten Ladevorgängen
            </span>
          </RealtimeWidget>
          <RealtimeWidget
            caption={"THG"}
            loading={false}
            icon={<MdAttachMoney size={21} />}
            primaryValue={formatEuro(forecastData.thgUntilNow ?? 0)}
          >
            <span className={"text-xs"}>
              THG Erlöse der bereits erfassten Ladevorgänge unter Annahme von 5ct/kWh
            </span>
          </RealtimeWidget>
          <RealtimeWidget
            caption={"Energie"}
            loading={false}
            icon={<MdAttachMoney size={21} />}
            primaryValue={formatKwh(forecastData.kWhUntilNow ?? 0)}
          >
            <span className={"text-xs"}>Energie aller bereits erfassten Ladevorgängen</span>
          </RealtimeWidget>
        </div>
      </Card>
      <MonthlyForecastWrapper />
    </>
  );
};

export default ForecastClientWrapper;
