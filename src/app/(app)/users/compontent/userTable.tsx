import Link from "next/link";
import type { UserWithOu } from "../page";
import StyledLink from "~/app/(app)/util/StyledLink";
import Headline from "~/component/Headline";
import React from "react";
import Card from "~/component/card";
import { Role } from "@prisma/client";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";

interface UserTableProps {
  users: UserWithOu[];
}
export const revalidate = 0;
const UserTable = async ({ users }: UserTableProps) => {
  const session = await getServerSession(authOptions);
  const isAdmin = session?.user?.role == Role.ADMIN;

  const getRoleLabel = (role: Role) => {
    switch (role) {
      case Role.ADMIN:
        return "Admin";
      case Role.USER:
        return "Standardbenutzer";
      case Role.CARD_HOLDER:
        return "Mitarbeiter/Clubmitglied";
      case Role.CARD_MANAGER:
        return "Manager";
      case Role.CPO:
        return "Betreiber";
      default:
        return "Benutzer";
    }
  };

  return (
    <Card>
      <Headline title={"Benutzerverwaltung"} />
      <div className="mb-4 flex justify-end gap-4">
        {session?.user?.selectedOu?.allowOrderEMPCards && (
          <StyledLink href={"/users/invite"}>Person einladen</StyledLink>
        )}
        {isAdmin && <StyledLink href={"/users/new"}>Benutzer anlegen</StyledLink>}
      </div>

      <table className="min-w-full divide-y">
        <thead className="bg-primary font-bold text-white">
          <tr>
            <th scope="col" className="px-6 py-3 text-left text-xs uppercase tracking-wider">
              Name
            </th>
            <th scope="col" className="px-6 py-3 text-left text-xs uppercase tracking-wider">
              Nachname
            </th>
            <th scope="col" className="px-6 py-3 text-left text-xs uppercase tracking-wider">
              Kennung
            </th>
            <th scope="col" className="px-6 py-3 text-left text-xs uppercase tracking-wider">
              E-Mail
            </th>
            <th scope="col" className="px-6 py-3 text-left text-xs uppercase tracking-wider">
              Rolle
            </th>
            <th scope="col" className="px-6 py-3 text-left text-xs uppercase tracking-wider">
              Nutzergruppe
            </th>
            <th scope="col" className="px-6 py-3 text-left text-xs uppercase tracking-wider">
              Organisationseinheit
            </th>
          </tr>
        </thead>
        <tbody className="divide-y divide-gray-500">
          {users.map((user) => (
            <tr key={user.id} className={"border-b border-gray-200 "}>
              <td className="whitespace-nowrap border-b border-gray-200 bg-white px-5 py-2 text-sm">
                {isAdmin ? (
                  <Link href={`/users/${user.id}`}>{user.name}</Link>
                ) : (
                  <span>{user.name}</span>
                )}
              </td>
              <td className="whitespace-nowrap border-b border-gray-200 bg-white px-5 py-2 text-sm">
                {isAdmin ? (
                  <Link href={`/users/${user.id}`}>{user.lastName}</Link>
                ) : (
                  <span>{user.lastName}</span>
                )}
              </td>
              <td className="whitespace-nowrap border-b border-gray-200 bg-white px-5 py-2 text-sm">
                {isAdmin ? (
                  <Link href={`/users/${user.id}`}>{user.identifier}</Link>
                ) : (
                  <span>{user.identifier}</span>
                )}
              </td>
              <td className="whitespace-nowrap border-b border-gray-200 bg-white px-5 py-2 text-sm">
                {isAdmin ? (
                  <Link href={`/users/${user.id}`}>{user.email}</Link>
                ) : (
                  <span>{user.email}</span>
                )}
              </td>
              <td className="whitespace-nowrap border-b border-gray-200 bg-white px-5 py-2 text-sm">
                {isAdmin ? (
                  <Link href={`/users/${user.id}`}>{getRoleLabel(user.role)}</Link>
                ) : (
                  <span>{getRoleLabel(user.role)}</span>
                )}
              </td>
              <td className="whitespace-nowrap border-b border-gray-200 bg-white px-5 py-2 text-sm">
                {isAdmin ? (
                  <Link href={`/users/${user.id}`}>{user.ou.name}</Link>
                ) : (
                  <span>{user.ou.name}</span>
                )}
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </Card>
  );
};

export default UserTable;
