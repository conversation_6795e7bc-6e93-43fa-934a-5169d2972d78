POST https://beta.ocpi.longship.io/ocpi/2.2/credentials
Authorization: Token 7a90ff88c29e4482995f67b99310e7e1 # Longship inintal Token, wird nur hier gebraucht
Content-Type: application/json

{
  "url": "http://jkhq.myddns.me/ocpi/versions",
  "token": "3ef18550639545f39b9e484316abb4ff", // unser token für die OCPI anfragen von longship
  "roles": [{
    "role": "EMSP",
    "party_id": "EUL",
    "country_code": "DE",
    "business_details": {
      "name": "Eulektro GmbH"
    }
  }]
}
