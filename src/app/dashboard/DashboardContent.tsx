'use client';

import { useState, useEffect } from 'react';
import { useWebSocket } from '@/lib/hooks/useWebSocket';

interface DashboardData {
  groups: {
    id: string;
    name: string;
    chargePointCount: number;
    currentLoad: number;
    maxLimit: number;
  }[];
  totalActiveStations: number;
  totalLoad: number;
  availableCapacity: number;
  phaseLoads: {
    phase: string;
    load: number;
    limit: number;
  }[];
}

export default function DashboardContent() {
  const [data, setData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [serverRunning, setServerRunning] = useState(false);

  // Connect to WebSocket for live updates
  const { isConnected, lastMessage } = useWebSocket(
    'ws://localhost:3001',
    ['chargepoint.events']
  );

  // Fetch initial data
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        
        // Check server status
        const statusResponse = await fetch('/api/server/status');
        const statusData = await statusResponse.json();
        setServerRunning(statusData.running);
        
        // Fetch groups
        const groupsResponse = await fetch('/api/groups');
        const groupsData = await groupsResponse.json();
        
        // Fetch limits
        const limitsResponse = await fetch('/api/limits');
        const limitsData = await limitsResponse.json();
        
        // Process data
        const processedData: DashboardData = {
          groups: groupsData.map((group: any) => {
            const chargePointCount = group.chargePoints?.length || 0;
            const currentLoad = 0; // This would come from real-time data
            
            // Find the max limit for this group
            const groupLimits = limitsData.filter((limit: any) => limit.groupId === group.id);
            const maxLimit = groupLimits.length > 0
              ? Math.max(...groupLimits.map((limit: any) => limit.value))
              : 0;
            
            return {
              id: group.id,
              name: group.name,
              chargePointCount,
              currentLoad,
              maxLimit,
            };
          }),
          totalActiveStations: 0, // This would come from real-time data
          totalLoad: 0, // This would come from real-time data
          availableCapacity: 100, // This would come from real-time data
          phaseLoads: [
            { phase: 'L1', load: 0, limit: 0 },
            { phase: 'L2', load: 0, limit: 0 },
            { phase: 'L3', load: 0, limit: 0 },
          ],
        };
        
        setData(processedData);
        setError(null);
      } catch (err) {
        console.error('Error fetching dashboard data:', err);
        setError('Failed to load dashboard data');
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  }, []);

  // Update data when receiving WebSocket messages
  useEffect(() => {
    if (lastMessage && data) {
      // In a real implementation, we would update the data based on the message
      console.log('Received WebSocket message:', lastMessage);
    }
  }, [lastMessage, data]);

  // Start the server
  const handleStartServer = async () => {
    try {
      const response = await fetch('/api/server/start', {
        method: 'POST',
      });
      
      if (response.ok) {
        setServerRunning(true);
      } else {
        const errorData = await response.json();
        setError(errorData.error || 'Failed to start server');
      }
    } catch (err) {
      console.error('Error starting server:', err);
      setError('Failed to start server');
    }
  };

  // Stop the server
  const handleStopServer = async () => {
    try {
      const response = await fetch('/api/server/stop', {
        method: 'POST',
      });
      
      if (response.ok) {
        setServerRunning(false);
      } else {
        const errorData = await response.json();
        setError(errorData.error || 'Failed to stop server');
      }
    } catch (err) {
      console.error('Error stopping server:', err);
      setError('Failed to stop server');
    }
  };

  if (loading) {
    return <div className="flex justify-center items-center h-64">Loading...</div>;
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 text-red-800 rounded-lg p-4 mb-6">
        <p>{error}</p>
      </div>
    );
  }

  if (!data) {
    return <div>No data available</div>;
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold">Dashboard</h1>
        <div className="flex items-center space-x-4">
          <div className={`flex items-center ${isConnected ? 'text-green-600' : 'text-gray-400'}`}>
            <span className={`inline-block w-3 h-3 rounded-full mr-2 ${isConnected ? 'bg-green-600' : 'bg-gray-400'}`}></span>
            <span>{isConnected ? 'Connected' : 'Disconnected'}</span>
          </div>
          {serverRunning ? (
            <button
              onClick={handleStopServer}
              className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
            >
              Stop Server
            </button>
          ) : (
            <button
              onClick={handleStartServer}
              className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
            >
              Start Server
            </button>
          )}
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <DashboardCard title="Total Active Stations" value={`${data.totalActiveStations}`} />
        <DashboardCard title="Current Total Load" value={`${data.totalLoad} kW`} />
        <DashboardCard title="Available Capacity" value={`${data.availableCapacity}%`} />
      </div>
      
      <div className="bg-white shadow rounded-lg p-6 mb-8">
        <h2 className="text-xl font-medium mb-4">Phase Load Overview</h2>
        <div className="h-64 flex items-center justify-center bg-gray-100 rounded">
          <p className="text-gray-500">Phase load chart will be displayed here</p>
        </div>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-xl font-medium mb-4">Group Status</h2>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Group
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Charge Points
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Current Load
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Max Limit
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {data.groups.map((group) => (
                  <tr key={group.id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {group.name}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {group.chargePointCount}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {group.currentLoad} kW
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {group.maxLimit} kW
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
        
        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-xl font-medium mb-4">Upcoming Schedule Changes</h2>
          <div className="h-64 flex items-center justify-center bg-gray-100 rounded">
            <p className="text-gray-500">Schedule changes will be displayed here</p>
          </div>
        </div>
      </div>
    </div>
  );
}

function DashboardCard({ title, value }: { title: string; value: string }) {
  return (
    <div className="bg-white shadow rounded-lg p-6">
      <h3 className="text-sm font-medium text-gray-500">{title}</h3>
      <p className="mt-2 text-3xl font-semibold">{value}</p>
    </div>
  );
}
