import Link from 'next/link';
import { notFound } from 'next/navigation';
import { prisma } from '@/lib/db/prisma';
import { getOCPPAdapter } from '@/lib/ocpp/adapter';
import { getLatestPhaseReadings } from '@/lib/core/aggregator';

async function getGroup(id: string) {
  // Get group from database
  const group = await prisma.group.findUnique({
    where: { id },
    include: {
      chargePoints: true,
    },
  });

  if (!group) {
    return null;
  }

  // Get OCPP adapter
  const ocppAdapter = getOCPPAdapter();

  // Enhance charge points with additional data
  const enhancedChargePoints = await Promise.all(group.chargePoints.map(async (cp) => {
    // Get connection status
    const isConnected = ocppAdapter.isChargePointConnected(cp.id);

    // Get latest phase readings
    const phaseReadings = await getLatestPhaseReadings(cp.id);

    // Calculate current load
    const totalPower = phaseReadings.reduce((sum, reading) => sum + (reading?.power || 0), 0);

    return {
      id: cp.id,
      name: cp.name,
      status: isConnected ? 'Connected' : 'Disconnected',
      currentLoad: `${totalPower.toFixed(1)} kW`,
    };
  }));

  // Get limits for this group
  const limits = await prisma.limitProfile.findMany({
    where: { groupId: id },
  });

  // Format limits for display
  const formattedLimits = limits.map(limit => ({
    phase: limit.phase,
    type: limit.type,
    value: `${limit.value}`,
  }));

  return {
    ...group,
    chargePoints: enhancedChargePoints,
    limits: formattedLimits.length > 0 ? formattedLimits : [
      { phase: 'L1', type: 'kW', value: '0' },
      { phase: 'L2', type: 'kW', value: '0' },
      { phase: 'L3', type: 'kW', value: '0' },
    ],
  };
}

export default async function GroupDetailPage({ params }: { params: { id: string } }) {
  const groupId = params.id;
  const group = await getGroup(groupId);

  if (!group) {
    notFound();
  }

  // If using real data, handle not found case
  if (groupId !== 'demo-1' && groupId !== 'demo-2') {
    notFound();
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <div>
          <Link href="/groups" className="text-primary-600 hover:text-primary-800">
            ← Back to Groups
          </Link>
          <h1 className="text-2xl font-semibold mt-2">{group.name}</h1>
        </div>
        <div className="flex space-x-3">
          <Link
            href={`/groups/${groupId}/edit`}
            className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
          >
            Edit Group
          </Link>
          <Link
            href={`/groups/${groupId}/limits`}
            className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 transition-colors"
          >
            Manage Limits
          </Link>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-xl font-medium mb-4">Group Limits</h2>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Phase
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Type
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Value
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {group.limits.map((limit, index) => (
                  <tr key={index}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {limit.phase}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {limit.type}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {limit.value}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-xl font-medium mb-4">Current Load</h2>
          <div className="h-48 flex items-center justify-center bg-gray-100 rounded">
            <p className="text-gray-500">Load chart will be displayed here</p>
          </div>
        </div>
      </div>

      <div className="bg-white shadow rounded-lg overflow-hidden">
        <div className="px-6 py-5 border-b border-gray-200">
          <h2 className="text-xl font-medium">Charge Points</h2>
        </div>
        <ul className="divide-y divide-gray-200">
          {group.chargePoints.map((cp) => (
            <li key={cp.id} className="px-6 py-4">
              <div className="flex items-center justify-between">
                <div>
                  <Link href={`/chargepoints/${cp.id}`} className="text-primary-600 font-medium hover:text-primary-800">
                    {cp.name}
                  </Link>
                  <p className="text-sm text-gray-500">ID: {cp.id}</p>
                </div>
                <div className="flex items-center">
                  <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                    cp.status === 'Charging' ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800'
                  }`}>
                    {cp.status}
                  </span>
                  <span className="ml-4 text-sm text-gray-500">{cp.currentLoad}</span>
                </div>
              </div>
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
}
