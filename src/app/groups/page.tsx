import Link from 'next/link';

export default function GroupsPage() {
  // This would be replaced with actual data fetching
  const groups = [
    { id: 'demo-1', name: 'Main Building', chargePointCount: 4, currentLoad: '45 kW', maxLimit: '100 kW' },
    { id: 'demo-2', name: 'Visitor Parking', chargePointCount: 2, currentLoad: '22 kW', maxLimit: '50 kW' },
  ];

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold">Charging Groups</h1>
        <Link 
          href="/groups/new" 
          className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 transition-colors"
        >
          Create Group
        </Link>
      </div>
      
      <div className="bg-white shadow overflow-hidden sm:rounded-md">
        <ul className="divide-y divide-gray-200">
          {groups.length > 0 ? (
            groups.map((group) => (
              <li key={group.id}>
                <Link href={`/groups/${group.id}`} className="block hover:bg-gray-50">
                  <div className="px-4 py-4 sm:px-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <p className="text-lg font-medium text-primary-600 truncate">{group.name}</p>
                      </div>
                      <div className="ml-2 flex-shrink-0 flex">
                        <p className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                          Active
                        </p>
                      </div>
                    </div>
                    <div className="mt-2 sm:flex sm:justify-between">
                      <div className="sm:flex">
                        <p className="flex items-center text-sm text-gray-500">
                          {group.chargePointCount} charge points
                        </p>
                      </div>
                      <div className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0">
                        <p>Current: {group.currentLoad} / Max: {group.maxLimit}</p>
                      </div>
                    </div>
                  </div>
                </Link>
              </li>
            ))
          ) : (
            <li className="px-4 py-6 text-center text-gray-500">
              No groups found. Create your first group to get started.
            </li>
          )}
        </ul>
      </div>
    </div>
  );
}
