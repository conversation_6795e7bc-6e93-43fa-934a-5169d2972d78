import type { NextRequest } from "next/server";
import { env } from "../../../env.js";
import { NextResponse } from "next/server";
import url from "url";
import { stripe } from "../../../utils/stripe/stripe";
import type Strip<PERSON> from "stripe";
import { downloadReportFile, createPayoutExportFile } from "../../../utils/stripe/reportHelper";
import prisma from "../../../server/db/prisma";
import { LogType, PayoutStatus } from "@prisma/client";
import Logger from "~/server/logger/logger";
import fs from "fs";
import { PaymentIntent } from "@stripe/stripe-js";

const onReportRunEvent = async (event: any) => {
  let report: Stripe.Reporting.ReportRun;
  try {
    Logger("Event Type Valid", "Event Type Valid", "stripe", LogType.DEBUG);

    // cast necessary. If not javascript default object is assumed
    report = event.data.object as Stripe.Reporting.ReportRun;
    try {
      fs.writeFileSync(
        `raw_report_object_${report.parameters.payout}.json`,
        JSON.stringify(report),
      );
    } catch (e) {
      if (report?.result?.url && report.result.filename) {
        const parsedUrl = url.parse(report?.result?.url);
        Logger(
          `Storing raw Report.Run object failed`,
          "Dumping Report.Run object to disk failed",
          "stripe",
          LogType.ERROR,
        );
      }
    }

    if (report?.result?.url && report.result.filename) {
      const parsedUrl = url.parse(report?.result?.url);
      Logger(`parsed url is ${parsedUrl.path}`, "Report URL", "stripe", LogType.DEBUG);

      const options = {
        hostname: parsedUrl.hostname,
        path: parsedUrl.path,
        method: "GET",
        headers: {
          Authorization: `Bearer ${env.STRIPE_SECRET_KEY}`,
        },
      };
      if (report.parameters.payout) {
        await prisma.stripePayout.update({
          where: { id: report.parameters.payout },
          data: { status: PayoutStatus.STRIPE_REPORT_RECEIVED },
        });

        const reportFilePath = `${env.STRIPE_PAYOUT_REPORTS}/${report.result.filename}`;

        await downloadReportFile(options, reportFilePath, report.parameters.payout);

        const success = createPayoutExportFile(report);
        if (!success) {
          NextResponse.json("Error creating payoutfile. Details see Logger entries", {
            status: 404,
          });
        }
      } else {
        NextResponse.json("No payout found", { status: 404 });
      }
    }
  } catch (err) {
    NextResponse.json("error", { status: 500 });
  }
};

const onPaymentSucceededEvent = async (event: any) => {
  Logger(
    `Payment Intent Succeeded ${event.data.object}`,
    "Payment Intent Succeeded",
    "stripe",
    LogType.ERROR,
  );
  const paymentIntent = event.data.object as PaymentIntent & {
    metadata?: {
      cdr_id?: string;
    };
  };
  if (paymentIntent?.metadata?.cdr_id && paymentIntent?.id) {
    try {
      await prisma.paymentIntent.create({
        data: {
          id: paymentIntent.id,
          cdrId: paymentIntent.metadata.cdr_id,
          status: paymentIntent.status,
          invoiceId: null,
        },
      });
    } catch (e) {
      return NextResponse.json("error1", { status: 500 });
    }
  } else {
    try {
      await prisma.paymentIntent.update({
        where: { id: paymentIntent.id },
        data: { status: paymentIntent.status },
      });
    } catch (e) {
      return NextResponse.json("error2", { status: 500 });
    }
  }
};

export async function POST(request: NextRequest) {
  Logger("Received Stripe Webhook", "New Webhook", "stripe", LogType.INFO);
  const sig = request.headers.get("stripe-signature") as string | string[];
  // TEST
  const endpointSecret = env.STRIPE_WEBHOOK_SECRET;

  const raw = await request.text();

  let event;

  try {
    Logger("Trying to construct Event", "Event Construction", "stripe", LogType.INFO);

    event = stripe.webhooks.constructEvent(raw, sig, endpointSecret);

    if (event.type == "reporting.report_run.succeeded") {
      await onReportRunEvent(event);
    } else if (event.type == "payment_intent.succeeded") {
      await onPaymentSucceededEvent(event);
    } else {
      Logger(
        "Event it not report_run.succeeded or payment_intent.succeeded so return",
        "Event Type Ignored",
        "stripe",
        LogType.INFO,
      );

      NextResponse.json("success");
    }
  } catch (e) {
    NextResponse.json("error", { status: 500 });
  }

  return NextResponse.json("success");
}
