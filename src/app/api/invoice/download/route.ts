import { type NextRequest, NextResponse } from "next/server";

import { prisma } from "~/server/db";

import { readFileSync } from "fs";

export const revalidate = 0;

export async function GET(request: NextRequest) {
  const invoiceId = request.nextUrl.searchParams.get("invoiceId");

  if (invoiceId) {
    try {
      const invoice = await prisma.invoice.findUnique({
        where: { id: invoiceId },
      });
      if (
        invoice &&
        Array.isArray(invoice?.files) &&
        invoice.files.length > 0
      ) {
        const fileObj = invoice.files[0];

        if (fileObj) {
          const fileStream = readFileSync(fileObj.path);
          return new NextResponse(fileStream, {
            headers: {
              "Content-Type": `${fileObj.application_type}`,
              "Content-Disposition": `attachment; filename=${fileObj.name}`,
            },
          });
        }
      }
    } catch (e) {
      return new Response("Error - Invoice not found", { status: 404 });
    }
  }

  return new Response("No parameter", { status: 404 });
}
