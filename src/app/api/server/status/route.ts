import { NextRequest, NextResponse } from 'next/server';
import { getOCPPAdapter } from '@/lib/ocpp/adapter';

// Server state (shared with start/stop routes)
let serverRunning = false;

/**
 * GET /api/server/status
 * Get the server status
 */
export async function GET(request: NextRequest) {
  try {
    // Get connected charge points if server is running
    let connectedChargePoints: string[] = [];
    
    if (serverRunning) {
      const ocppAdapter = getOCPPAdapter();
      connectedChargePoints = ocppAdapter.getConnectedChargePoints();
    }
    
    return NextResponse.json({
      running: serverRunning,
      connectedChargePoints,
      connectedCount: connectedChargePoints.length,
    });
  } catch (error) {
    return NextResponse.json(
      { error: 'Failed to get server status' },
      { status: 500 }
    );
  }
}
