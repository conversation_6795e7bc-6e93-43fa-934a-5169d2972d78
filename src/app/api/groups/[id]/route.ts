import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db/prisma';
import { logger } from '@/lib/utils/logger';

/**
 * GET /api/groups/[id]
 * Get a specific group
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const groupId = params.id;
    
    const group = await prisma.group.findUnique({
      where: { id: groupId },
      include: {
        chargePoints: {
          select: {
            id: true,
            name: true,
            lastHeartbeat: true,
          },
        },
        limitProfiles: {
          include: {
            schedule: true,
          },
        },
      },
    });
    
    if (!group) {
      return NextResponse.json(
        { error: 'Group not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(group);
  } catch (error) {
    logger.error(`Error getting group ${params.id}:`, error);
    return NextResponse.json(
      { error: 'Failed to get group' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/groups/[id]
 * Update a group
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const groupId = params.id;
    const body = await request.json();
    
    // Validate request body
    if (!body.name) {
      return NextResponse.json(
        { error: 'Group name is required' },
        { status: 400 }
      );
    }
    
    // Check if group exists
    const existingGroup = await prisma.group.findUnique({
      where: { id: groupId },
    });
    
    if (!existingGroup) {
      return NextResponse.json(
        { error: 'Group not found' },
        { status: 404 }
      );
    }
    
    // Update group
    const group = await prisma.group.update({
      where: { id: groupId },
      data: {
        name: body.name,
        description: body.description,
      },
    });
    
    logger.info(`Updated group: ${group.name}`);
    
    return NextResponse.json(group);
  } catch (error) {
    logger.error(`Error updating group ${params.id}:`, error);
    return NextResponse.json(
      { error: 'Failed to update group' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/groups/[id]
 * Delete a group
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const groupId = params.id;
    
    // Check if group exists
    const existingGroup = await prisma.group.findUnique({
      where: { id: groupId },
      include: {
        chargePoints: true,
        limitProfiles: true,
      },
    });
    
    if (!existingGroup) {
      return NextResponse.json(
        { error: 'Group not found' },
        { status: 404 }
      );
    }
    
    // Check if group has charge points
    if (existingGroup.chargePoints.length > 0) {
      return NextResponse.json(
        { error: 'Cannot delete group with charge points' },
        { status: 400 }
      );
    }
    
    // Delete limit profiles
    if (existingGroup.limitProfiles.length > 0) {
      await prisma.limitProfile.deleteMany({
        where: { groupId },
      });
    }
    
    // Delete group
    await prisma.group.delete({
      where: { id: groupId },
    });
    
    logger.info(`Deleted group: ${existingGroup.name}`);
    
    return NextResponse.json({ success: true });
  } catch (error) {
    logger.error(`Error deleting group ${params.id}:`, error);
    return NextResponse.json(
      { error: 'Failed to delete group' },
      { status: 500 }
    );
  }
}
