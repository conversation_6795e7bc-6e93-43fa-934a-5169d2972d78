import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db/prisma';
import { logger } from '@/lib/utils/logger';
import { createTimeSchedule } from '@/lib/scheduler/timeSchedule';

/**
 * GET /api/schedules
 * Get all schedules
 */
export async function GET(request: NextRequest) {
  try {
    const schedules = await prisma.timeSchedule.findMany({
      include: {
        limitProfiles: {
          select: {
            id: true,
            group: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
      },
    });

    return NextResponse.json(schedules);
  } catch (error) {
    logger.error('Error getting schedules:', error);
    return NextResponse.json(
      { error: 'Failed to get schedules' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/schedules
 * Create a new schedule
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate request body
    if (!body.name) {
      return NextResponse.json(
        { error: 'Schedule name is required' },
        { status: 400 }
      );
    }

    if (!body.startTime) {
      return NextResponse.json(
        { error: 'Start time is required' },
        { status: 400 }
      );
    }

    if (!body.endTime) {
      return NextResponse.json(
        { error: 'End time is required' },
        { status: 400 }
      );
    }

    if (!body.daysOfWeek || !Array.isArray(body.daysOfWeek) || body.daysOfWeek.length === 0) {
      return NextResponse.json(
        { error: 'Days of week are required' },
        { status: 400 }
      );
    }

    // Create schedule
    const schedule = await createTimeSchedule({
      name: body.name,
      description: body.description,
      timezone: body.timezone,
      startTime: body.startTime,
      endTime: body.endTime,
      daysOfWeek: JSON.parse(JSON.stringify(body.daysOfWeek)),
    });

    return NextResponse.json(schedule, { status: 201 });
  } catch (error) {
    logger.error('Error creating schedule:', error);
    const errorMessage = error instanceof Error ? error.message : 'Failed to create schedule';
    return NextResponse.json(
      { error: errorMessage },
      { status: 500 }
    );
  }
}
