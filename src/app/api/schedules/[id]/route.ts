import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db/prisma';
import { logger } from '@/lib/utils/logger';
import { updateTimeSchedule, deleteTimeSchedule } from '@/lib/scheduler/timeSchedule';

/**
 * GET /api/schedules/[id]
 * Get a specific schedule
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const scheduleId = params.id;
    
    const schedule = await prisma.timeSchedule.findUnique({
      where: { id: scheduleId },
      include: {
        limitProfiles: {
          include: {
            group: {
              select: {
                id: true,
                name: true,
              },
            },
          },
        },
      },
    });
    
    if (!schedule) {
      return NextResponse.json(
        { error: 'Schedule not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(schedule);
  } catch (error) {
    logger.error(`Error getting schedule ${params.id}:`, error);
    return NextResponse.json(
      { error: 'Failed to get schedule' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/schedules/[id]
 * Update a schedule
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const scheduleId = params.id;
    const body = await request.json();
    
    // Check if schedule exists
    const existingSchedule = await prisma.timeSchedule.findUnique({
      where: { id: scheduleId },
    });
    
    if (!existingSchedule) {
      return NextResponse.json(
        { error: 'Schedule not found' },
        { status: 404 }
      );
    }
    
    // Update schedule
    const schedule = await updateTimeSchedule(scheduleId, {
      name: body.name,
      description: body.description,
      timezone: body.timezone,
      startTime: body.startTime,
      endTime: body.endTime,
      daysOfWeek: body.daysOfWeek,
    });
    
    return NextResponse.json(schedule);
  } catch (error) {
    logger.error(`Error updating schedule ${params.id}:`, error);
    return NextResponse.json(
      { error: error.message || 'Failed to update schedule' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/schedules/[id]
 * Delete a schedule
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const scheduleId = params.id;
    
    // Delete schedule
    await deleteTimeSchedule(scheduleId);
    
    return NextResponse.json({ success: true });
  } catch (error) {
    logger.error(`Error deleting schedule ${params.id}:`, error);
    return NextResponse.json(
      { error: error.message || 'Failed to delete schedule' },
      { status: 500 }
    );
  }
}
