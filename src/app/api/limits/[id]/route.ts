import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db/prisma';
import { logger } from '@/lib/utils/logger';
import { checkGroupLimits } from '@/lib/core/throttlingStrategy';

/**
 * GET /api/limits/[id]
 * Get a specific limit
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const limitId = params.id;
    
    const limit = await prisma.limitProfile.findUnique({
      where: { id: limitId },
      include: {
        group: {
          select: {
            id: true,
            name: true,
          },
        },
        schedule: true,
      },
    });
    
    if (!limit) {
      return NextResponse.json(
        { error: 'Limit not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(limit);
  } catch (error) {
    logger.error(`Error getting limit ${params.id}:`, error);
    return NextResponse.json(
      { error: 'Failed to get limit' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/limits/[id]
 * Update a limit
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const limitId = params.id;
    const body = await request.json();
    
    // Validate request body
    if (body.phase && !['L1', 'L2', 'L3'].includes(body.phase)) {
      return NextResponse.json(
        { error: 'Phase must be L1, L2, or L3' },
        { status: 400 }
      );
    }
    
    if (body.type && !['KW', 'A'].includes(body.type)) {
      return NextResponse.json(
        { error: 'Type must be KW or A' },
        { status: 400 }
      );
    }
    
    if (body.value !== undefined && (typeof body.value !== 'number' || body.value <= 0)) {
      return NextResponse.json(
        { error: 'Value must be a positive number' },
        { status: 400 }
      );
    }
    
    // Check if limit exists
    const existingLimit = await prisma.limitProfile.findUnique({
      where: { id: limitId },
    });
    
    if (!existingLimit) {
      return NextResponse.json(
        { error: 'Limit not found' },
        { status: 404 }
      );
    }
    
    // Check if schedule exists if provided
    if (body.scheduleId) {
      const schedule = await prisma.timeSchedule.findUnique({
        where: { id: body.scheduleId },
      });
      
      if (!schedule) {
        return NextResponse.json(
          { error: 'Schedule not found' },
          { status: 404 }
        );
      }
    }
    
    // Update limit
    const limit = await prisma.limitProfile.update({
      where: { id: limitId },
      data: {
        phase: body.phase,
        type: body.type,
        value: body.value,
        scheduleId: body.scheduleId,
      },
    });
    
    logger.info(`Updated limit ${limitId}`);
    
    // Check if limit affects current group state
    await checkGroupLimits(existingLimit.groupId);
    
    return NextResponse.json(limit);
  } catch (error) {
    logger.error(`Error updating limit ${params.id}:`, error);
    return NextResponse.json(
      { error: 'Failed to update limit' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/limits/[id]
 * Delete a limit
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const limitId = params.id;
    
    // Check if limit exists
    const existingLimit = await prisma.limitProfile.findUnique({
      where: { id: limitId },
    });
    
    if (!existingLimit) {
      return NextResponse.json(
        { error: 'Limit not found' },
        { status: 404 }
      );
    }
    
    // Store group ID for later
    const groupId = existingLimit.groupId;
    
    // Delete limit
    await prisma.limitProfile.delete({
      where: { id: limitId },
    });
    
    logger.info(`Deleted limit ${limitId}`);
    
    // Check if limit removal affects current group state
    await checkGroupLimits(groupId);
    
    return NextResponse.json({ success: true });
  } catch (error) {
    logger.error(`Error deleting limit ${params.id}:`, error);
    return NextResponse.json(
      { error: 'Failed to delete limit' },
      { status: 500 }
    );
  }
}
