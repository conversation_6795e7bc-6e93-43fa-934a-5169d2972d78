import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db/prisma';
import { logger } from '@/lib/utils/logger';
import { checkGroupLimits } from '@/lib/core/throttlingStrategy';

/**
 * GET /api/limits
 * Get all limits, optionally filtered by group
 */
export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url);
    const groupId = url.searchParams.get('groupId');
    
    const whereClause = groupId ? { groupId } : {};
    
    const limits = await prisma.limitProfile.findMany({
      where: whereClause,
      include: {
        group: {
          select: {
            id: true,
            name: true,
          },
        },
        schedule: true,
      },
    });
    
    return NextResponse.json(limits);
  } catch (error) {
    logger.error('Error getting limits:', error);
    return NextResponse.json(
      { error: 'Failed to get limits' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/limits
 * Create a new limit
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate request body
    if (!body.groupId) {
      return NextResponse.json(
        { error: 'Group ID is required' },
        { status: 400 }
      );
    }
    
    if (!body.phase || !['L1', 'L2', 'L3'].includes(body.phase)) {
      return NextResponse.json(
        { error: 'Phase must be L1, L2, or L3' },
        { status: 400 }
      );
    }
    
    if (!body.type || !['KW', 'A'].includes(body.type)) {
      return NextResponse.json(
        { error: 'Type must be KW or A' },
        { status: 400 }
      );
    }
    
    if (typeof body.value !== 'number' || body.value <= 0) {
      return NextResponse.json(
        { error: 'Value must be a positive number' },
        { status: 400 }
      );
    }
    
    // Check if group exists
    const group = await prisma.group.findUnique({
      where: { id: body.groupId },
    });
    
    if (!group) {
      return NextResponse.json(
        { error: 'Group not found' },
        { status: 404 }
      );
    }
    
    // Check if schedule exists if provided
    if (body.scheduleId) {
      const schedule = await prisma.timeSchedule.findUnique({
        where: { id: body.scheduleId },
      });
      
      if (!schedule) {
        return NextResponse.json(
          { error: 'Schedule not found' },
          { status: 404 }
        );
      }
    }
    
    // Check if limit already exists for this group and phase
    const existingLimit = await prisma.limitProfile.findFirst({
      where: {
        groupId: body.groupId,
        phase: body.phase,
        scheduleId: body.scheduleId || null,
      },
    });
    
    if (existingLimit) {
      return NextResponse.json(
        { error: 'Limit already exists for this group, phase, and schedule' },
        { status: 400 }
      );
    }
    
    // Create limit
    const limit = await prisma.limitProfile.create({
      data: {
        groupId: body.groupId,
        phase: body.phase,
        type: body.type,
        value: body.value,
        scheduleId: body.scheduleId,
      },
    });
    
    logger.info(`Created limit for group ${group.name}, phase ${body.phase}`);
    
    // Check if limit affects current group state
    await checkGroupLimits(body.groupId);
    
    return NextResponse.json(limit, { status: 201 });
  } catch (error) {
    logger.error('Error creating limit:', error);
    return NextResponse.json(
      { error: 'Failed to create limit' },
      { status: 500 }
    );
  }
}
