import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db/prisma';
import { logger } from '@/lib/utils/logger';
import { getOCPPAdapter } from '@/lib/ocpp/adapter';
import { getLatestPhaseReadings } from '@/lib/core/aggregator';

/**
 * GET /api/chargepoints
 * Get all charge points
 */
export async function GET(request: NextRequest) {
  try {
    // Get all charge points
    const chargePoints = await prisma.chargePoint.findMany({
      include: {
        group: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

    // Get OCPP adapter
    const ocppAdapter = getOCPPAdapter();

    // Enhance charge points with additional data
    const enhancedChargePoints = await Promise.all(chargePoints.map(async (cp) => {
      // Get connection status
      const isConnected = ocppAdapter.isChargePointConnected(cp.id);

      // Get latest phase readings
      const phaseReadings = await getLatestPhaseReadings(cp.id);

      return {
        ...cp,
        isConnected,
        phaseReadings,
      };
    }));

    return NextResponse.json(enhancedChargePoints);
  } catch (error) {
    logger.error('Error getting charge points:', error);
    return NextResponse.json(
      { error: 'Failed to get charge points' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/chargepoints
 * Create a new charge point
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Validate request body
    if (!body.id) {
      return NextResponse.json(
        { error: 'Charge point ID is required' },
        { status: 400 }
      );
    }

    if (!body.name) {
      return NextResponse.json(
        { error: 'Charge point name is required' },
        { status: 400 }
      );
    }

    // Check if charge point already exists
    const existingChargePoint = await prisma.chargePoint.findUnique({
      where: { id: body.id },
    });

    if (existingChargePoint) {
      return NextResponse.json(
        { error: 'Charge point with this ID already exists' },
        { status: 400 }
      );
    }

    // Create charge point
    const chargePoint = await prisma.chargePoint.create({
      data: {
        id: body.id,
        name: body.name,
        model: body.model,
        vendor: body.vendor,
        connectorIds: body.connectorIds ? JSON.parse(JSON.stringify(body.connectorIds)) : JSON.parse(JSON.stringify([1])),
        groupId: body.groupId,
      },
    });

    logger.info(`Created charge point: ${chargePoint.name}`);

    return NextResponse.json(chargePoint, { status: 201 });
  } catch (error) {
    logger.error('Error creating charge point:', error);
    return NextResponse.json(
      { error: 'Failed to create charge point' },
      { status: 500 }
    );
  }
}
