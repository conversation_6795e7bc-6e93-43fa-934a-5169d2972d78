import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db/prisma';
import { logger } from '@/lib/utils/logger';
import { getLatestPhaseReadings } from '@/lib/core/aggregator';
import { getOCPPAdapter } from '@/lib/ocpp/adapter';

/**
 * GET /api/chargepoints/[id]
 * Get a specific charge point
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const chargePointId = params.id;
    
    const chargePoint = await prisma.chargePoint.findUnique({
      where: { id: chargePointId },
      include: {
        group: {
          select: {
            id: true,
            name: true,
          },
        },
        chargingProfiles: true,
      },
    });
    
    if (!chargePoint) {
      return NextResponse.json(
        { error: 'Charge point not found' },
        { status: 404 }
      );
    }
    
    // Get latest phase readings
    const phaseReadings = await getLatestPhaseReadings(chargePointId);
    
    // Check if charge point is connected
    const ocppAdapter = getOCPPAdapter();
    const isConnected = ocppAdapter.isChargePointConnected(chargePointId);
    
    // Combine data
    const result = {
      ...chargePoint,
      phaseReadings,
      isConnected,
    };
    
    return NextResponse.json(result);
  } catch (error) {
    logger.error(`Error getting charge point ${params.id}:`, error);
    return NextResponse.json(
      { error: 'Failed to get charge point' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/chargepoints/[id]
 * Update a charge point
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const chargePointId = params.id;
    const body = await request.json();
    
    // Validate request body
    if (!body.name) {
      return NextResponse.json(
        { error: 'Charge point name is required' },
        { status: 400 }
      );
    }
    
    // Check if charge point exists
    const existingChargePoint = await prisma.chargePoint.findUnique({
      where: { id: chargePointId },
    });
    
    if (!existingChargePoint) {
      return NextResponse.json(
        { error: 'Charge point not found' },
        { status: 404 }
      );
    }
    
    // Update charge point
    const chargePoint = await prisma.chargePoint.update({
      where: { id: chargePointId },
      data: {
        name: body.name,
        model: body.model,
        vendor: body.vendor,
        connectorIds: body.connectorIds,
        groupId: body.groupId,
      },
    });
    
    logger.info(`Updated charge point: ${chargePoint.name}`);
    
    return NextResponse.json(chargePoint);
  } catch (error) {
    logger.error(`Error updating charge point ${params.id}:`, error);
    return NextResponse.json(
      { error: 'Failed to update charge point' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/chargepoints/[id]
 * Delete a charge point
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const chargePointId = params.id;
    
    // Check if charge point exists
    const existingChargePoint = await prisma.chargePoint.findUnique({
      where: { id: chargePointId },
    });
    
    if (!existingChargePoint) {
      return NextResponse.json(
        { error: 'Charge point not found' },
        { status: 404 }
      );
    }
    
    // Delete related data
    await prisma.$transaction([
      prisma.phaseReading.deleteMany({
        where: { chargePointId },
      }),
      prisma.chargingProfileState.deleteMany({
        where: { chargePointId },
      }),
      prisma.chargePoint.delete({
        where: { id: chargePointId },
      }),
    ]);
    
    logger.info(`Deleted charge point: ${existingChargePoint.name}`);
    
    return NextResponse.json({ success: true });
  } catch (error) {
    logger.error(`Error deleting charge point ${params.id}:`, error);
    return NextResponse.json(
      { error: 'Failed to delete charge point' },
      { status: 500 }
    );
  }
}
