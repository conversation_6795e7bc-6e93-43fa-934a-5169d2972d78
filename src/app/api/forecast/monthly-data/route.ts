import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role } from "@prisma/client";
import prisma from "~/server/db/prisma";
import { getOusBelowOu } from "~/server/model/ou/func";

export interface MonthlyForecastData {
  month: string;
  revenue: number;
  energyGrossMargin: number;
  thgRevenue: number;
  totalGrossMargin: number;
  kWh: number;
  sessions: number;
}

export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session || !session.user) {
      return NextResponse.json("Unauthorized", { status: 401 });
    }

    // Get OUs that the user has access to
    const ouWithChildren = await getOusBelowOu(session.user.selectedOu);
    const ouCodes = ouWithChildren?.map((ou) => ou.code) || [];

    // Get aggregated data for the last 24 months
    const endDate = new Date();
    const startDate = new Date();
    startDate.setMonth(startDate.getMonth() - 24);
    
    const startMonthKey = `${startDate.getFullYear()}-${String(startDate.getMonth() + 1).padStart(2, '0')}`;
    const endMonthKey = `${endDate.getFullYear()}-${String(endDate.getMonth() + 1).padStart(2, '0')}`;

    const aggregations = await prisma.monthlyForecastAggregation.findMany({
      where: {
        month: {
          gte: startMonthKey,
          lte: endMonthKey,
        },
        ouCode: {
          in: ouCodes,
        },
      },
      orderBy: {
        month: 'asc',
      },
    });

    // Group by month and sum values across OUs
    const monthlyData = new Map<string, MonthlyForecastData>();

    for (const aggregation of aggregations) {
      const existing = monthlyData.get(aggregation.month);
      
      if (existing) {
        existing.revenue += aggregation.revenue;
        existing.energyGrossMargin += aggregation.energyGrossMargin;
        existing.thgRevenue += aggregation.thgRevenue;
        existing.totalGrossMargin += aggregation.totalGrossMargin;
        existing.kWh += aggregation.kWh;
        existing.sessions += aggregation.sessions;
      } else {
        monthlyData.set(aggregation.month, {
          month: aggregation.month,
          revenue: aggregation.revenue,
          energyGrossMargin: aggregation.energyGrossMargin,
          thgRevenue: aggregation.thgRevenue,
          totalGrossMargin: aggregation.totalGrossMargin,
          kWh: aggregation.kWh,
          sessions: aggregation.sessions,
        });
      }
    }

    // Convert to array and ensure we have all months (fill gaps with zeros)
    const result: MonthlyForecastData[] = [];
    const currentDate = new Date(startDate);
    
    while (currentDate <= endDate) {
      const monthKey = `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}`;
      
      const data = monthlyData.get(monthKey) || {
        month: monthKey,
        revenue: 0,
        energyGrossMargin: 0,
        thgRevenue: 0,
        totalGrossMargin: 0,
        kWh: 0,
        sessions: 0,
      };
      
      result.push(data);
      currentDate.setMonth(currentDate.getMonth() + 1);
    }

    return NextResponse.json({
      status: "success",
      data: result,
    });
  } catch (error) {
    console.error("Error fetching monthly forecast data:", error);
    return NextResponse.json(
      {
        status: "error",
        message: "Internal server error",
        error: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}
