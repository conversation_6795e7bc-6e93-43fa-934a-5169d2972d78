import { type NextRequest, NextResponse } from "next/server";
import { prisma } from "~/server/db";

import { stripe } from "~/utils/stripe/stripe";
import { InvoiceManager } from "~/utils/invoice";

import { Cdr, Invoice, StatusEnum } from "../../../../../../prisma/client";

export const revalidate = 0;

export async function GET(request: NextRequest) {
  const token = request.nextUrl.searchParams.get("token");

  if (token !== "490327urzh0iqwg3f32mf03q2ne") {
    return NextResponse.json("error", { status: 500 });
  }

  const cdr_pi = [
    {
      cdrid: "DEEUL506857C4C6DBD0A341EBD3CEE80E430",
      pi: "pi_3Q4mO4INwl1ZJuJd1NlSzx50",
    },
    {
      cdrid: "DEEUL0ABE3104E7C152ABC567AA588B67904",
      pi: "pi_3Q4mYjINwl1ZJuJd1HZRU6cu",
    },
  ];
  for (const cdr_pi_element of cdr_pi) {
    const { cdrid, pi } = cdr_pi_element;
    const cdr = await prisma.cdr.findUnique({ where: { id: cdrid } });
    const stripePi = await stripe.paymentIntents.retrieve(pi);
    let paymentIntent;
    try {
      paymentIntent = await prisma.paymentIntent.create({
        data: {
          id: pi,
          amount: stripePi.amount,
          amount_capturable: stripePi.amount_capturable,
          amount_received: stripePi.amount_received,
          authorization_reference: cdr?.authorization_reference ?? "",
          session_start_token_uid: cdr?.cdr_token?.uid ?? "",
          status: StatusEnum.START_REQUESTED,
          evseId: cdr?.cdr_location?.evse_id ?? "",
          energy_price: 0.42,
          session_fee: 1,
          blocking_fee: 0.05,
          blocking_fee_max: 12,
          blocking_fee_start: 240,
          tax_rate: 19,
          min_kwh_in_kwh: 0.2,
          min_time_in_min: 2,
          cdrId: cdrid,
          sessionId: cdrid,
          locationId: null,
        },
      });
    } catch (e) {}

    if (!paymentIntent) {
      paymentIntent = await prisma.paymentIntent.findUnique({
        where: { id: pi },
      });
    }
    const invoiceManager = new InvoiceManager();

    const invoice = await invoiceManager.createInvoice({
      subject: `Ladevorgang am Ladepunkt ${
        cdr?.cdr_location?.evse_id ?? ""
      } am Standort ${cdr?.cdr_location.address ?? ""}, ${
        cdr?.cdr_location.postal_code ?? ""
      } ${cdr?.cdr_location?.city ?? ""}`,
      emp_country_code: "DE",
      emp_party_id: "EUL",
      service_period_from: new Date(cdr?.start_date_time ?? ""),
      service_period_to: new Date(cdr?.end_date_time ?? ""),
      local_start_datetime:
        new Date(cdr?.start_date_time ?? "").toLocaleString("de-DE") ?? "",
      local_end_datetime:
        new Date(cdr?.end_date_time ?? "").toLocaleString("de-DE") ?? "",
      authorization_reference: cdr?.authorization_reference ?? "",
      metadata: {
        paymentIntent: pi,
      },
    });

    if (paymentIntent) {
      //there is always a kWh invoice item
      await invoiceManager.addInvoiceItem(invoice.id, {
        title: "Preis für geladene kWh",
        unit: "kWh",
        unit_price_gross: paymentIntent?.energy_price,
        amount: Number(cdr?.total_energy) ?? 0,
        tax_rate: paymentIntent.tax_rate,
        description: "Kosten für geladene kWh",
      });
    }

    if (paymentIntent?.session_fee && paymentIntent.session_fee > 0) {
      await invoiceManager.addInvoiceItem(invoice.id, {
        title: "Grundpreis",
        unit: "Sitzung",
        unit_price_gross: paymentIntent.session_fee,
        amount: 1,
        tax_rate: paymentIntent.tax_rate,
        description: "Startgebühr",
      });
    }

    // if there are blocking fees, add invoice item
    if (paymentIntent?.blocking_fee && paymentIntent.blocking_fee > 0 && cdr) {
      const minutes = cdr?.total_time ?? 0 * 60;
      let blocking_minutes = minutes - paymentIntent.blocking_fee_start;
      if (blocking_minutes > 0) {
        const blocking_costs = blocking_minutes * paymentIntent.blocking_fee;
        if (
          paymentIntent.blocking_fee_max > 0 &&
          blocking_costs > paymentIntent.blocking_fee_max
        ) {
          //max minutes needs to be calculated, because only max price is in DB
          blocking_minutes =
            paymentIntent.blocking_fee_max / paymentIntent.blocking_fee;
        }

        const max_block_text = paymentIntent.blocking_fee_max
          ? ` ab ${
              paymentIntent.blocking_fee_start
            } Minuten (max. ${paymentIntent.blocking_fee_max.toLocaleString(
              "de",
              {
                style: "currency",
                currency: "EUR",
              }
            )})`
          : "";
        await invoiceManager.addInvoiceItem(invoice.id, {
          title: `Blockiergebühr${max_block_text}`,
          unit: "Minute",
          unit_price_gross: paymentIntent.blocking_fee,
          amount: blocking_minutes,
          tax_rate: paymentIntent.tax_rate,
          description: `Blockiergebühr${max_block_text}`,
        });
      }
    }

    const invoiceWithPositions = await prisma.invoice.findUnique({
      where: {
        id: invoice.id,
      },
    });

    await invoiceManager.finalize(invoice.id);
    await invoiceManager.setStatusToPaid(invoice.id);
    await invoiceManager.send(invoice.id, pi);

    return NextResponse.json("ok");
  }
  // cdrs + paymentIntentID aus stripe
  //price
  //paymentIntent erzeugen in db
  // rechnung erstellen aus cdr + paymentIntent
  // stripe capture
  // rechnung finalize
}
