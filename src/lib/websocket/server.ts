import { WebSocketServer, WebSocket } from 'ws';
import { logger } from '../utils/logger';
import { subscribe, unsubscribe } from '../messageBus/subscriber';

// WebSocket server instance
let wss: WebSocketServer | null = null;

// Connected clients
const clients: Map<WebSocket, Set<string>> = new Map();

/**
 * Start the WebSocket server
 */
export function startWebSocketServer(port: number = 3001): void {
  if (wss) {
    logger.warn('WebSocket server is already running');
    return;
  }
  
  wss = new WebSocketServer({ port });
  
  logger.info(`WebSocket server started on port ${port}`);
  
  wss.on('connection', (ws) => {
    logger.debug('Client connected to WebSocket server');
    
    // Initialize client subscriptions
    clients.set(ws, new Set());
    
    // Handle messages
    ws.on('message', (data) => {
      try {
        const message = JSON.parse(data.toString());
        
        if (message.type === 'subscribe') {
          handleSubscribe(ws, message.topic);
        } else if (message.type === 'unsubscribe') {
          handleUnsubscribe(ws, message.topic);
        }
      } catch (error) {
        logger.error('Error handling WebSocket message:', error);
      }
    });
    
    // Handle disconnection
    ws.on('close', () => {
      logger.debug('Client disconnected from WebSocket server');
      
      // Clean up subscriptions
      const topics = clients.get(ws);
      if (topics) {
        clients.delete(ws);
      }
    });
  });
  
  // Subscribe to all relevant topics
  subscribeToTopics();
}

/**
 * Stop the WebSocket server
 */
export function stopWebSocketServer(): void {
  if (wss) {
    wss.close();
    wss = null;
    clients.clear();
    logger.info('WebSocket server stopped');
  }
}

/**
 * Handle subscribe message
 */
function handleSubscribe(ws: WebSocket, topic: string): void {
  const topics = clients.get(ws);
  
  if (topics) {
    topics.add(topic);
    logger.debug(`Client subscribed to topic: ${topic}`);
  }
}

/**
 * Handle unsubscribe message
 */
function handleUnsubscribe(ws: WebSocket, topic: string): void {
  const topics = clients.get(ws);
  
  if (topics) {
    topics.delete(topic);
    logger.debug(`Client unsubscribed from topic: ${topic}`);
  }
}

/**
 * Subscribe to all relevant topics
 */
function subscribeToTopics(): void {
  // Subscribe to chargepoint events
  subscribe('chargepoint.boot', (data) => {
    broadcastToTopic('chargepoint.events', {
      type: 'boot',
      data,
    });
  });
  
  subscribe('chargepoint.status', (data) => {
    broadcastToTopic('chargepoint.events', {
      type: 'status',
      data,
    });
  });
  
  subscribe('chargepoint.metervalues', (data) => {
    broadcastToTopic('chargepoint.events', {
      type: 'metervalues',
      data,
    });
    
    // Also broadcast to specific chargepoint topic
    broadcastToTopic(`chargepoint.${data.chargePointId}`, {
      type: 'metervalues',
      data,
    });
  });
  
  subscribe('chargepoint.transaction.start', (data) => {
    broadcastToTopic('chargepoint.events', {
      type: 'transaction.start',
      data,
    });
    
    // Also broadcast to specific chargepoint topic
    broadcastToTopic(`chargepoint.${data.chargePointId}`, {
      type: 'transaction.start',
      data,
    });
  });
  
  subscribe('chargepoint.transaction.stop', (data) => {
    broadcastToTopic('chargepoint.events', {
      type: 'transaction.stop',
      data,
    });
    
    // Also broadcast to specific chargepoint topic
    broadcastToTopic(`chargepoint.${data.chargePointId}`, {
      type: 'transaction.stop',
      data,
    });
  });
}

/**
 * Broadcast a message to all clients subscribed to a topic
 */
function broadcastToTopic(topic: string, message: any): void {
  if (!wss) {
    return;
  }
  
  const payload = JSON.stringify({
    topic,
    data: message,
    timestamp: new Date().toISOString(),
  });
  
  let count = 0;
  
  clients.forEach((topics, client) => {
    if (topics.has(topic) && client.readyState === WebSocket.OPEN) {
      client.send(payload);
      count++;
    }
  });
  
  if (count > 0) {
    logger.debug(`Broadcasted message to ${count} clients for topic ${topic}`);
  }
}
