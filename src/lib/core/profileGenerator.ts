import { logger } from '../utils/logger';

/**
 * Generate a charging profile for a charge point
 */
export function generateChargingProfile(
  chargePoint: any,
  throttlingFactor: number,
  limitType: string
): any {
  try {
    // Ensure throttling factor is between 0 and 1
    const factor = Math.max(0, Math.min(1, throttlingFactor));
    
    // Default values
    const defaultMaxCurrent = 16; // 16A
    const minCurrent = 6; // 6A minimum as per standard
    
    // Calculate limit based on throttling factor
    let limit: number;
    let chargingRateUnit: string;
    
    if (limitType === 'A') {
      // Limit in Amps
      limit = Math.max(minCurrent, Math.floor(defaultMaxCurrent * factor));
      chargingRateUnit = 'A';
    } else {
      // Limit in kW (assuming 230V and 3-phase)
      const maxPower = (defaultMaxCurrent * 230 * 3) / 1000; // kW
      const minPower = (minCurrent * 230 * 3) / 1000; // kW
      limit = Math.max(minPower, maxPower * factor);
      chargingRateUnit = 'W';
      
      // Convert to watts
      limit = Math.floor(limit * 1000);
    }
    
    logger.info(`Generated charging profile for ${chargePoint.id} with limit ${limit} ${chargingRateUnit}`);
    
    // Create charging profile
    return {
      chargingProfileId: Math.floor(Math.random() * 1000000),
      stackLevel: 0,
      chargingProfilePurpose: 'TxProfile',
      chargingProfileKind: 'Absolute',
      recurrencyKind: 'Daily',
      validFrom: new Date().toISOString(),
      validTo: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // Valid for 24 hours
      chargingSchedule: {
        duration: 86400, // 24 hours in seconds
        startSchedule: new Date().toISOString(),
        chargingRateUnit,
        minChargingRate: limitType === 'A' ? minCurrent : (minCurrent * 230 * 3),
        chargingSchedulePeriod: [
          {
            startPeriod: 0,
            limit,
          },
        ],
      },
    };
  } catch (error) {
    logger.error(`Error generating charging profile for ${chargePoint.id}:`, error);
    
    // Return a default profile
    return {
      chargingProfileId: Math.floor(Math.random() * 1000000),
      stackLevel: 0,
      chargingProfilePurpose: 'TxProfile',
      chargingProfileKind: 'Absolute',
      recurrencyKind: 'Daily',
      validFrom: new Date().toISOString(),
      validTo: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
      chargingSchedule: {
        duration: 86400,
        startSchedule: new Date().toISOString(),
        chargingRateUnit: 'A',
        minChargingRate: 6,
        chargingSchedulePeriod: [
          {
            startPeriod: 0,
            limit: 16,
          },
        ],
      },
    };
  }
}
