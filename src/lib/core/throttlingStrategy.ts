import { prisma } from '../db/prisma';
import { logger } from '../utils/logger';
import { getGroupPowerConsumption } from './aggregator';
import { generateChargingProfile } from './profileGenerator';
import { getOCPPAdapter } from '../ocpp/adapter';

/**
 * Check if a group exceeds its limits and apply throttling if needed
 */
export async function checkGroupLimits(groupId: string): Promise<void> {
  try {
    // Get current power consumption for the group
    const consumption = await getGroupPowerConsumption(groupId);

    // Get active limits for the group
    const limits = await getActiveLimits(groupId);

    // If no limits found, nothing to check
    if (!limits || limits.length === 0) {
      return;
    }

    // Check each phase against its limit
    const exceededPhases: { phase: string; current: number; limit: number; type: string }[] = [];

    for (const limit of limits) {
      const { phase, type, value } = limit;

      // Skip if no consumption data for this phase
      if (!consumption[phase]) {
        continue;
      }

      // Check if limit is exceeded
      if (type === 'KW' && consumption[phase] > value) {
        exceededPhases.push({
          phase,
          current: consumption[phase],
          limit: value,
          type,
        });
      } else if (type === 'A') {
        // Convert current power to amps (assuming 230V)
        const currentAmps = (consumption[phase] * 1000) / 230;

        if (currentAmps > value) {
          exceededPhases.push({
            phase,
            current: currentAmps,
            limit: value,
            type,
          });
        }
      }
    }

    // If no limits exceeded, nothing to do
    if (exceededPhases.length === 0) {
      return;
    }

    // Apply throttling
    await applyThrottling(groupId, exceededPhases);
  } catch (error) {
    logger.error(`Error checking group limits for ${groupId}:`, error);
  }
}

/**
 * Get active limits for a group, considering time schedules
 */
async function getActiveLimits(groupId: string): Promise<any[]> {
  try {
    // Get all limits for the group
    const allLimits = await prisma.limitProfile.findMany({
      where: { groupId },
      include: { schedule: true },
    });

    // If no limits found, return empty array
    if (!allLimits || allLimits.length === 0) {
      return [];
    }

    // Filter limits by active schedules
    const now = new Date();
    const activeLimits = allLimits.filter((limit) => {
      // If no schedule, limit is always active
      if (!limit.schedule) {
        return true;
      }

      // Check if current time is within schedule
      return isScheduleActive(limit.schedule, now);
    });

    return activeLimits;
  } catch (error) {
    logger.error(`Error getting active limits for group ${groupId}:`, error);
    return [];
  }
}

/**
 * Check if a schedule is active at the given time
 */
function isScheduleActive(schedule: any, now: Date): boolean {
  try {
    // Get current day of week (0-6, Sunday-Saturday)
    const dayOfWeek = now.getDay();

    // Check if current day is in schedule
    const daysOfWeek = Array.isArray(schedule.daysOfWeek) ? schedule.daysOfWeek : JSON.parse(schedule.daysOfWeek as string);
    if (!daysOfWeek.includes(dayOfWeek)) {
      return false;
    }

    // Parse start and end times
    const [startHour, startMinute] = schedule.startTime.split(':').map(Number);
    const [endHour, endMinute] = schedule.endTime.split(':').map(Number);

    // Create Date objects for start and end times on the current day
    const startTime = new Date(now);
    startTime.setHours(startHour, startMinute, 0, 0);

    const endTime = new Date(now);
    endTime.setHours(endHour, endMinute, 0, 0);

    // Handle overnight schedules
    if (endHour < startHour || (endHour === startHour && endMinute < startMinute)) {
      endTime.setDate(endTime.getDate() + 1);
    }

    // Check if current time is within schedule
    return now >= startTime && now <= endTime;
  } catch (error) {
    logger.error(`Error checking if schedule is active:`, error);
    return false;
  }
}

/**
 * Apply throttling to charge points in a group
 */
async function applyThrottling(
  groupId: string,
  exceededPhases: { phase: string; current: number; limit: number; type: string }[]
): Promise<void> {
  try {
    // Get all charge points in the group
    const chargePoints = await prisma.chargePoint.findMany({
      where: { groupId },
    });

    // If no charge points found, nothing to do
    if (!chargePoints || chargePoints.length === 0) {
      return;
    }

    // Get the most restrictive limit
    const mostRestrictiveLimit = exceededPhases.reduce(
      (prev, current) => {
        // Convert to same unit for comparison
        const prevValue = prev.type === 'KW' ? prev.limit : (prev.limit * 230) / 1000;
        const currentValue = current.type === 'KW' ? current.limit : (current.limit * 230) / 1000;

        return prevValue < currentValue ? prev : current;
      },
      exceededPhases[0]
    );

    // Calculate throttling factor
    const currentValue = mostRestrictiveLimit.current;
    const limitValue = mostRestrictiveLimit.limit;
    const throttlingFactor = limitValue / currentValue;

    logger.info(`Applying throttling to group ${groupId} with factor ${throttlingFactor.toFixed(2)}`);

    // Apply throttling to each charge point
    for (const chargePoint of chargePoints) {
      await applyThrottlingToChargePoint(chargePoint.id, throttlingFactor, mostRestrictiveLimit.type);
    }
  } catch (error) {
    logger.error(`Error applying throttling to group ${groupId}:`, error);
  }
}

/**
 * Apply throttling to a specific charge point
 */
async function applyThrottlingToChargePoint(
  chargePointId: string,
  throttlingFactor: number,
  limitType: string
): Promise<void> {
  try {
    // Get the OCPP adapter
    const ocppAdapter = getOCPPAdapter();

    // Check if charge point is connected
    if (!ocppAdapter.isChargePointConnected(chargePointId)) {
      logger.warn(`Cannot apply throttling: ChargePoint ${chargePointId} not connected`);
      return;
    }

    // Get charge point details
    const chargePoint = await prisma.chargePoint.findUnique({
      where: { id: chargePointId },
    });

    if (!chargePoint) {
      logger.warn(`Cannot apply throttling: ChargePoint ${chargePointId} not found in database`);
      return;
    }

    // Generate charging profile
    const chargingProfile = generateChargingProfile(chargePoint, throttlingFactor, limitType);

    // Send SetChargingProfile request to charge point
    const messageId = `cp_${Date.now()}`;
    const message = [
      2, // CALL
      messageId,
      'SetChargingProfile',
      {
        connectorId: 0, // Apply to all connectors
        csChargingProfiles: chargingProfile,
      },
    ];

    // Send the message
    const success = ocppAdapter.sendMessage(chargePointId, message);

    if (success) {
      logger.info(`Sent SetChargingProfile to ${chargePointId}`);

      // Save charging profile state to database
      await prisma.chargingProfileState.upsert({
        where: {
          chargePointId_connectorId: {
            chargePointId,
            connectorId: 0,
          },
        },
        update: {
          profileJson: chargingProfile,
          validUntil: new Date(Date.now() + 24 * 60 * 60 * 1000), // Valid for 24 hours
          updatedAt: new Date(),
        },
        create: {
          chargePointId,
          connectorId: 0,
          profileJson: chargingProfile,
          validUntil: new Date(Date.now() + 24 * 60 * 60 * 1000), // Valid for 24 hours
        },
      });
    } else {
      logger.error(`Failed to send SetChargingProfile to ${chargePointId}`);
    }
  } catch (error) {
    logger.error(`Error applying throttling to charge point ${chargePointId}:`, error);
  }
}
