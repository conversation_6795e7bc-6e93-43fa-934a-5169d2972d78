'use client';

import { useState, useEffect, useRef, useCallback } from 'react';

interface WebSocketOptions {
  reconnectInterval?: number;
  reconnectAttempts?: number;
  onOpen?: (event: Event) => void;
  onClose?: (event: CloseEvent) => void;
  onError?: (event: Event) => void;
}

interface WebSocketMessage {
  topic: string;
  data: any;
  timestamp: string;
}

/**
 * React hook for WebSocket connection
 */
export function useWebSocket(
  url: string,
  topics: string[] = [],
  options: WebSocketOptions = {}
) {
  const [isConnected, setIsConnected] = useState(false);
  const [messages, setMessages] = useState<WebSocketMessage[]>([]);
  const [lastMessage, setLastMessage] = useState<WebSocketMessage | null>(null);
  const [error, setError] = useState<Event | null>(null);
  
  const ws = useRef<WebSocket | null>(null);
  const reconnectCount = useRef(0);
  const reconnectTimerId = useRef<NodeJS.Timeout | null>(null);
  
  const {
    reconnectInterval = 5000,
    reconnectAttempts = 10,
    onOpen,
    onClose,
    onError,
  } = options;
  
  // Connect to WebSocket
  const connect = useCallback(() => {
    if (ws.current?.readyState === WebSocket.OPEN) {
      return;
    }
    
    ws.current = new WebSocket(url);
    
    ws.current.onopen = (event) => {
      setIsConnected(true);
      setError(null);
      reconnectCount.current = 0;
      
      // Subscribe to topics
      topics.forEach((topic) => {
        if (ws.current?.readyState === WebSocket.OPEN) {
          ws.current.send(JSON.stringify({ type: 'subscribe', topic }));
        }
      });
      
      if (onOpen) {
        onOpen(event);
      }
    };
    
    ws.current.onclose = (event) => {
      setIsConnected(false);
      
      if (onClose) {
        onClose(event);
      }
      
      // Attempt to reconnect
      if (reconnectCount.current < reconnectAttempts) {
        reconnectCount.current += 1;
        
        if (reconnectTimerId.current) {
          clearTimeout(reconnectTimerId.current);
        }
        
        reconnectTimerId.current = setTimeout(() => {
          connect();
        }, reconnectInterval);
      }
    };
    
    ws.current.onerror = (event) => {
      setError(event);
      
      if (onError) {
        onError(event);
      }
    };
    
    ws.current.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data) as WebSocketMessage;
        
        setMessages((prev) => [...prev, message]);
        setLastMessage(message);
      } catch (error) {
        console.error('Error parsing WebSocket message:', error);
      }
    };
  }, [url, topics, reconnectInterval, reconnectAttempts, onOpen, onClose, onError]);
  
  // Subscribe to a topic
  const subscribe = useCallback((topic: string) => {
    if (ws.current?.readyState === WebSocket.OPEN) {
      ws.current.send(JSON.stringify({ type: 'subscribe', topic }));
    }
  }, []);
  
  // Unsubscribe from a topic
  const unsubscribe = useCallback((topic: string) => {
    if (ws.current?.readyState === WebSocket.OPEN) {
      ws.current.send(JSON.stringify({ type: 'unsubscribe', topic }));
    }
  }, []);
  
  // Send a message
  const send = useCallback((data: any) => {
    if (ws.current?.readyState === WebSocket.OPEN) {
      ws.current.send(JSON.stringify(data));
      return true;
    }
    return false;
  }, []);
  
  // Connect on mount, disconnect on unmount
  useEffect(() => {
    connect();
    
    return () => {
      if (reconnectTimerId.current) {
        clearTimeout(reconnectTimerId.current);
      }
      
      if (ws.current) {
        ws.current.close();
      }
    };
  }, [connect]);
  
  // Subscribe to topics when they change
  useEffect(() => {
    if (isConnected && ws.current) {
      topics.forEach((topic) => {
        ws.current?.send(JSON.stringify({ type: 'subscribe', topic }));
      });
    }
  }, [isConnected, topics]);
  
  return {
    isConnected,
    messages,
    lastMessage,
    error,
    subscribe,
    unsubscribe,
    send,
  };
}
