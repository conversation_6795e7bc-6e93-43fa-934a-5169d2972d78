import { connect, NatsConnection, StringCodec, Subscription } from 'nats';
import { logger } from '../utils/logger';

// NATS connection
let natsConnection: NatsConnection | null = null;
const sc = StringCodec();

// Active subscriptions
const subscriptions: Map<string, Subscription> = new Map();

/**
 * Initialize NATS connection for subscriber
 */
export async function initSubscriber(): Promise<void> {
  try {
    if (!natsConnection) {
      natsConnection = await connect({
        servers: process.env.NATS_URL || 'nats://localhost:4222',
      });
      logger.info('Subscriber connected to NATS server');
    }
  } catch (error) {
    logger.error('Subscriber failed to connect to NATS server:', error);
  }
}

/**
 * Close NATS connection and all subscriptions
 */
export async function closeSubscriber(): Promise<void> {
  // Unsubscribe from all topics
  for (const [topic, subscription] of subscriptions.entries()) {
    subscription.unsubscribe();
    subscriptions.delete(topic);
    logger.debug(`Unsubscribed from ${topic}`);
  }

  // Close connection
  if (natsConnection) {
    await natsConnection.close();
    natsConnection = null;
    logger.info('Subscriber disconnected from NATS server');
  }
}

/**
 * Subscribe to a topic
 */
export async function subscribe(
  topic: string,
  handler: (data: any) => void
): Promise<void> {
  try {
    // Ensure we have a connection
    if (!natsConnection) {
      await initSubscriber();
    }

    // If we still don't have a connection, can't subscribe
    if (!natsConnection) {
      logger.error(`Cannot subscribe to ${topic}: No NATS connection`);
      return;
    }

    // Check if we're already subscribed
    if (subscriptions.has(topic)) {
      logger.warn(`Already subscribed to ${topic}`);
      return;
    }

    // Subscribe to the topic
    const subscription = natsConnection.subscribe(topic);
    subscriptions.set(topic, subscription);
    logger.info(`Subscribed to ${topic}`);

    // Handle messages
    (async () => {
      for await (const message of subscription) {
        try {
          const data = JSON.parse(sc.decode(message.data));
          handler(data);
        } catch (error) {
          logger.error(`Error handling message from ${topic}:`, error);
        }
      }
    })();
  } catch (error) {
    logger.error(`Error subscribing to ${topic}:`, error);
  }
}

/**
 * Unsubscribe from a topic
 */
export function unsubscribe(topic: string): void {
  const subscription = subscriptions.get(topic);
  if (subscription) {
    subscription.unsubscribe();
    subscriptions.delete(topic);
    logger.info(`Unsubscribed from ${topic}`);
  }
}
