import { connect, NatsConnection, StringCodec } from 'nats';
import { logger } from '../utils/logger';

// NATS connection
let natsConnection: NatsConnection | null = null;
const sc = StringCodec();

/**
 * Initialize NATS connection
 */
export async function initNats(): Promise<void> {
  try {
    if (!natsConnection) {
      natsConnection = await connect({
        servers: process.env.NATS_URL || 'nats://localhost:4222',
      });
      logger.info('Connected to NATS server');
    }
  } catch (error) {
    logger.error('Failed to connect to NATS server:', error);
    // Fall back to local event handling if NATS is not available
  }
}

/**
 * Close NATS connection
 */
export async function closeNats(): Promise<void> {
  if (natsConnection) {
    await natsConnection.close();
    natsConnection = null;
    logger.info('Disconnected from NATS server');
  }
}

/**
 * Publish an event to the message bus
 */
export async function publishEvent(topic: string, data: any): Promise<void> {
  try {
    // Ensure we have a connection
    if (!natsConnection) {
      await initNats();
    }

    // If we still don't have a connection, use local event handling
    if (!natsConnection) {
      handleLocalEvent(topic, data);
      return;
    }

    // Publish to NATS
    const payload = JSON.stringify(data);
    natsConnection.publish(topic, sc.encode(payload));
    logger.debug(`Published event to ${topic}`);
  } catch (error) {
    logger.error(`Error publishing event to ${topic}:`, error);
    // Fall back to local event handling
    handleLocalEvent(topic, data);
  }
}

/**
 * Local event handler for when NATS is not available
 */
function handleLocalEvent(topic: string, data: any): void {
  logger.debug(`Local event handling for ${topic}`);
  
  // Add local event handling logic here
  // This could be a simple event emitter or direct function calls
  
  // For now, just log the event
  logger.info(`Local event: ${topic}`, data);
}
