import { prisma } from '../db/prisma';
import { logger } from '../utils/logger';
import { publishEvent } from '../messageBus/publisher';
import { processMeterValues } from '../core/aggregator';

// OCPP Message Types
const CALL = 2;
const CALLRESULT = 3;
const CALLERROR = 4;

// Message handler map
const messageHandlers: Record<string, (chargePointId: string, payload: any) => Promise<any>> = {
  'BootNotification': handleBootNotification,
  'Heartbeat': handleHeartbeat,
  'StatusNotification': handleStatusNotification,
  'MeterValues': handleMeterValues,
  'StartTransaction': handleStartTransaction,
  'StopTransaction': handleStopTransaction,
};

/**
 * Main entry point for handling OCPP messages
 */
export async function handleOCPPMessage(chargePointId: string, message: any): Promise<any> {
  // OCPP message format: [MessageTypeId, UniqueId, Action, Payload]
  const [messageTypeId, messageId, action, payload] = message;

  // Handle different message types
  if (messageTypeId === CALL) {
    // This is a request from the charge point
    const handler = messageHandlers[action];

    if (!handler) {
      logger.warn(`No handler for action: ${action}`);
      return [CALLERROR, messageId, 'NotImplemented', `Action ${action} not supported`, {}];
    }

    try {
      const result = await handler(chargePointId, payload);
      return [CALLRESULT, messageId, result];
    } catch (error) {
      logger.error(`Error handling ${action}:`, error);
      const errorMessage = error instanceof Error ? error.message : 'Internal error';
      return [CALLERROR, messageId, 'InternalError', errorMessage, {}];
    }
  } else if (messageTypeId === CALLRESULT) {
    // This is a response to our request
    logger.debug(`Received response for message ${messageId}`);
    // Process response (could be handled by a callback system)
    return null;
  } else if (messageTypeId === CALLERROR) {
    // This is an error response to our request
    logger.error(`Received error for message ${messageId}: ${action}, ${JSON.stringify(payload)}`);
    return null;
  } else {
    logger.error(`Unknown message type: ${messageTypeId}`);
    return null;
  }
}

/**
 * Handle BootNotification request
 */
async function handleBootNotification(chargePointId: string, payload: any): Promise<any> {
  logger.info(`BootNotification from ${chargePointId}:`, payload);

  try {
    // Update or create ChargePoint in database
    await prisma.chargePoint.upsert({
      where: { id: chargePointId },
      update: {
        vendor: payload.chargePointVendor,
        model: payload.chargePointModel,
        firmwareVersion: payload.firmwareVersion,
        lastHeartbeat: new Date(),
      },
      create: {
        id: chargePointId,
        name: `Charger ${chargePointId}`, // Default name
        vendor: payload.chargePointVendor,
        model: payload.chargePointModel,
        firmwareVersion: payload.firmwareVersion,
        lastHeartbeat: new Date(),
        connectorIds: JSON.parse(JSON.stringify([1])), // Default connector
      },
    });

    // Publish event
    publishEvent('chargepoint.boot', {
      chargePointId,
      timestamp: new Date().toISOString(),
      ...payload,
    });

    // Return response
    return {
      status: 'Accepted',
      currentTime: new Date().toISOString(),
      interval: 300, // Heartbeat interval in seconds
    };
  } catch (error) {
    logger.error(`Error handling BootNotification for ${chargePointId}:`, error);
    throw error;
  }
}

/**
 * Handle Heartbeat request
 */
async function handleHeartbeat(chargePointId: string, payload: any): Promise<any> {
  logger.debug(`Heartbeat from ${chargePointId}`);

  try {
    // Update last heartbeat time
    await prisma.chargePoint.update({
      where: { id: chargePointId },
      data: { lastHeartbeat: new Date() },
    });

    // Return response
    return {
      currentTime: new Date().toISOString(),
    };
  } catch (error) {
    logger.error(`Error handling Heartbeat for ${chargePointId}:`, error);
    throw error;
  }
}

/**
 * Handle StatusNotification request
 */
async function handleStatusNotification(chargePointId: string, payload: any): Promise<any> {
  logger.info(`StatusNotification from ${chargePointId}:`, payload);

  try {
    // Get the current charge point
    const chargePoint = await prisma.chargePoint.findUnique({
      where: { id: chargePointId },
    });

    if (chargePoint) {
      // Parse connectorIds from JSON
      let connectorIds = typeof chargePoint.connectorIds === 'string'
        ? JSON.parse(chargePoint.connectorIds)
        : Array.isArray(chargePoint.connectorIds)
          ? chargePoint.connectorIds
          : Object.values(chargePoint.connectorIds || {});

      // Check if the connector from the payload is already in the list
      const connectorId = payload.connectorId;
      if (connectorId !== undefined && !connectorIds.includes(connectorId)) {
        // Add the new connector to the list
        connectorIds.push(connectorId);

        // Update the charge point with the new connector list
        await prisma.chargePoint.update({
          where: { id: chargePointId },
          data: {
            connectorIds: JSON.parse(JSON.stringify(connectorIds)),
          },
        });

        logger.info(`Added connector ${connectorId} to charge point ${chargePointId}`);
      }
    }
  } catch (error) {
    logger.error(`Error updating connectors for ${chargePointId}:`, error);
  }

  // Publish event
  publishEvent('chargepoint.status', {
    chargePointId,
    timestamp: new Date().toISOString(),
    ...payload,
  });

  // Return empty response (required by OCPP)
  return {};
}

/**
 * Handle MeterValues request
 */
async function handleMeterValues(chargePointId: string, payload: any): Promise<any> {
  logger.debug(`MeterValues from ${chargePointId}:`, payload);

  try {
    // Process meter values
    await processMeterValues(chargePointId, payload);

    // Publish event
    publishEvent('chargepoint.metervalues', {
      chargePointId,
      timestamp: new Date().toISOString(),
      ...payload,
    });

    // Return empty response (required by OCPP)
    return {};
  } catch (error) {
    logger.error(`Error handling MeterValues for ${chargePointId}:`, error);
    throw error;
  }
}

/**
 * Handle StartTransaction request
 */
async function handleStartTransaction(chargePointId: string, payload: any): Promise<any> {
  logger.info(`StartTransaction from ${chargePointId}:`, payload);

  // Publish event
  publishEvent('chargepoint.transaction.start', {
    chargePointId,
    timestamp: new Date().toISOString(),
    ...payload,
  });

  // Return response
  return {
    transactionId: payload.transactionId || Math.floor(Math.random() * 1000000),
    idTagInfo: {
      status: 'Accepted',
    },
  };
}

/**
 * Handle StopTransaction request
 */
async function handleStopTransaction(chargePointId: string, payload: any): Promise<any> {
  logger.info(`StopTransaction from ${chargePointId}:`, payload);

  // Publish event
  publishEvent('chargepoint.transaction.stop', {
    chargePointId,
    timestamp: new Date().toISOString(),
    ...payload,
  });

  // Return response
  return {
    idTagInfo: {
      status: 'Accepted',
    },
  };
}
