import { prisma } from '../db/prisma';
import { logger } from '../utils/logger';
import { checkGroupLimits } from '../core/throttlingStrategy';

// Interval for checking schedules (in milliseconds)
const SCHEDULE_CHECK_INTERVAL = 60000; // 1 minute

// Interval ID for the scheduler
let schedulerIntervalId: NodeJS.Timeout | null = null;

/**
 * Start the scheduler
 */
export function startScheduler(): void {
  if (schedulerIntervalId) {
    logger.warn('Scheduler is already running');
    return;
  }

  logger.info('Starting scheduler');

  // Run immediately
  checkSchedules();

  // Then run at regular intervals
  schedulerIntervalId = setInterval(checkSchedules, SCHEDULE_CHECK_INTERVAL);
}

/**
 * Stop the scheduler
 */
export function stopScheduler(): void {
  if (schedulerIntervalId) {
    clearInterval(schedulerIntervalId);
    schedulerIntervalId = null;
    logger.info('Scheduler stopped');
  }
}

/**
 * Check all schedules and apply limits as needed
 */
async function checkSchedules(): Promise<void> {
  try {
    logger.debug('Checking schedules');

    // Get all groups
    const groups = await prisma.group.findMany();

    // Check limits for each group
    for (const group of groups) {
      await checkGroupLimits(group.id);
    }
  } catch (error) {
    logger.error('Error checking schedules:', error);
  }
}

/**
 * Create a new time schedule
 */
export async function createTimeSchedule(scheduleData: {
  name: string;
  description?: string;
  timezone?: string;
  startTime: string;
  endTime: string;
  daysOfWeek: number[];
}): Promise<any> {
  try {
    // Validate schedule data
    validateScheduleData(scheduleData);

    // Prepare data with JSON for daysOfWeek
    const data = {
      ...scheduleData,
      daysOfWeek: JSON.parse(JSON.stringify(scheduleData.daysOfWeek))
    };

    // Create schedule
    const schedule = await prisma.timeSchedule.create({
      data,
    });

    logger.info(`Created time schedule: ${schedule.name}`);

    return schedule;
  } catch (error) {
    logger.error('Error creating time schedule:', error);
    throw error;
  }
}

/**
 * Update an existing time schedule
 */
export async function updateTimeSchedule(
  scheduleId: string,
  scheduleData: {
    name?: string;
    description?: string;
    timezone?: string;
    startTime?: string;
    endTime?: string;
    daysOfWeek?: number[];
  }
): Promise<any> {
  try {
    // Validate schedule data
    if (scheduleData.startTime || scheduleData.endTime || scheduleData.daysOfWeek) {
      validateScheduleData({
        name: '',
        startTime: scheduleData.startTime || '',
        endTime: scheduleData.endTime || '',
        daysOfWeek: scheduleData.daysOfWeek || [],
      });
    }

    // Update schedule
    const schedule = await prisma.timeSchedule.update({
      where: { id: scheduleId },
      data: scheduleData,
    });

    logger.info(`Updated time schedule: ${schedule.name}`);

    return schedule;
  } catch (error) {
    logger.error(`Error updating time schedule ${scheduleId}:`, error);
    throw error;
  }
}

/**
 * Delete a time schedule
 */
export async function deleteTimeSchedule(scheduleId: string): Promise<void> {
  try {
    // Check if schedule is in use
    const limitProfiles = await prisma.limitProfile.findMany({
      where: { scheduleId },
    });

    if (limitProfiles.length > 0) {
      throw new Error('Cannot delete schedule that is in use by limit profiles');
    }

    // Delete schedule
    await prisma.timeSchedule.delete({
      where: { id: scheduleId },
    });

    logger.info(`Deleted time schedule: ${scheduleId}`);
  } catch (error) {
    logger.error(`Error deleting time schedule ${scheduleId}:`, error);
    throw error;
  }
}

/**
 * Validate schedule data
 */
function validateScheduleData(scheduleData: {
  name: string;
  startTime: string;
  endTime: string;
  daysOfWeek: number[];
}): void {
  // Validate name
  if (!scheduleData.name && scheduleData.name !== '') {
    throw new Error('Schedule name is required');
  }

  // Validate start time
  if (!scheduleData.startTime) {
    throw new Error('Start time is required');
  }

  if (!scheduleData.startTime.match(/^([01]\d|2[0-3]):([0-5]\d)$/)) {
    throw new Error('Start time must be in HH:MM format');
  }

  // Validate end time
  if (!scheduleData.endTime) {
    throw new Error('End time is required');
  }

  if (!scheduleData.endTime.match(/^([01]\d|2[0-3]):([0-5]\d)$/)) {
    throw new Error('End time must be in HH:MM format');
  }

  // Validate days of week
  if (!scheduleData.daysOfWeek) {
    throw new Error('Days of week are required');
  }

  // Convert to array if it's a JSON string or object
  const daysArray = Array.isArray(scheduleData.daysOfWeek)
    ? scheduleData.daysOfWeek
    : (typeof scheduleData.daysOfWeek === 'string'
      ? JSON.parse(scheduleData.daysOfWeek)
      : Object.values(scheduleData.daysOfWeek));

  if (!Array.isArray(daysArray)) {
    throw new Error('Days of week must be an array');
  }

  if (daysArray.length === 0) {
    throw new Error('At least one day of week must be selected');
  }

  for (const day of daysArray) {
    if (typeof day !== 'number' || day < 0 || day > 6) {
      throw new Error('Days of week must be numbers between 0 and 6');
    }
  }
}
