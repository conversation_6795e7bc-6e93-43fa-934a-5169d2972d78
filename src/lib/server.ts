import { startOC<PERSON><PERSON>dapter, stopOCPPAdapter } from './ocpp/adapter';
import { startScheduler, stopScheduler } from './scheduler/timeSchedule';
import { initNats, closeNats } from './messageBus/publisher';
import { initSubscriber, closeSubscriber } from './messageBus/subscriber';
import { startWebSocketServer, stopWebSocketServer } from './websocket/server';
import { logger } from './utils/logger';

/**
 * Start all server components
 */
export async function startServer(): Promise<void> {
  try {
    logger.info('Starting server components...');

    // Initialize message bus
    await initNats();
    await initSubscriber();

    // Start OCPP adapter
    startOCPPAdapter();

    // Start scheduler
    startScheduler();

    // Start WebSocket server
    startWebSocketServer();

    logger.info('Server components started successfully');
  } catch (error) {
    logger.error('Error starting server components:', error);
    throw error;
  }
}

/**
 * Stop all server components
 */
export async function stopServer(): Promise<void> {
  try {
    logger.info('Stopping server components...');

    // Stop scheduler
    stopScheduler();

    // Stop OCPP adapter
    stopOCPPAdapter();

    // Stop WebSocket server
    stopWebSocketServer();

    // Close message bus connections
    await closeSubscriber();
    await closeNats();

    logger.info('Server components stopped successfully');
  } catch (error) {
    logger.error('Error stopping server components:', error);
    throw error;
  }
}
