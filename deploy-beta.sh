#!/bin/bash

# Log-Datei definieren
LOG_FILE="deploy-beta.log"

# Funktion zum Loggen mit Zeitstempel
log() {
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

log "Beta-Deployment gestartet"

# NVM laden
log "Lade NVM..."
export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && . "$NVM_DIR/nvm.sh"

# Verwende eine bestimmte Node-Version
log "Wechsle zu Node 22..."
nvm install 22
nvm use 22 >> "$LOG_FILE" 2>&1 || { log "Fehler beim Wechseln zu Node 22"; exit 1; }

# Umgebungsvariablen kopieren (Beta-spezifische Env-Dateien)
log "Kopiere Beta-Umgebungsvariablen..."
cp ../env/backend-beta.env backend/.env
cp ../env/frontend-beta.env frontend/.env

# Backend: Dependencies installieren, Admin bauen & neustarten
log "Starte Backend-Build..."
cd backend
log "Lösche Strapi Cache und Node-Module..."
rm -rf .cache dist build node_modules >> "../$LOG_FILE" 2>&1 || true
log "Installiere Backend-Dependencies..."
npm ci >> "../$LOG_FILE" 2>&1 || { log "Fehler bei npm ci im Backend"; exit 1; }
log "Baue Backend..."
npm run build >> "../$LOG_FILE" 2>&1 || { log "Fehler beim Backend-Build"; exit 1; }

# Frontend: Dependencies installieren, bauen & neustarten
log "Starte Frontend-Build..."
cd ../frontend
log "Installiere Frontend-Dependencies..."
npm ci >> "../$LOG_FILE" 2>&1 || { log "Fehler bei npm ci im Frontend"; exit 1; }
log "Baue Frontend..."
npm run build >> "../$LOG_FILE" 2>&1 || { log "Fehler beim Frontend-Build"; exit 1; }

# Starte beide Anwendungen neu mit Beta-Umgebung
log "Starte Beta-Anwendungen neu..."
cd ..

# Stoppe nur Beta-Prozesse falls sie laufen
log "Stoppe laufende Beta-Prozesse..."
pm2 delete etv-backend-beta >> "$LOG_FILE" 2>&1 || true
pm2 delete etv-frontend-beta >> "$LOG_FILE" 2>&1 || true

# Starte mit Beta-Konfiguration
log "Starte Anwendungen mit Beta-Konfiguration..."
pm2 start ecosystem.beta.config.js >> "$LOG_FILE" 2>&1 || { log "Fehler beim Starten der Beta-Anwendungen"; exit 1; }

log "Beta-Deployment abgeschlossen"
log "Frontend verfügbar unter: https://terminal.beta.eulektro.de"
log "Backend API verfügbar unter: https://api.terminal.beta.eulektro.de"
