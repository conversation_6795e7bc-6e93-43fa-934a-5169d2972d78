# When adding additional environment variables, the schema in "/src/env.mjs"
# should be updated accordingly.

# Prisma
# https://www.prisma.io/docs/reference/database-reference/connection-urls#env
DATABASE_URL="file:./db.sqlite"

# Next Auth
# You can generate a new secret on the command line with:
# openssl rand -base64 32
# https://next-auth.js.org/configuration/options#secret
NEXTAUTH_SECRET="asdadjfbsdkjrb423bdqwk3jfdbqk3jd"
NEXTAUTH_URL="http://localhost:3000"

# Next Auth Discord Provider
DISCORD_CLIENT_ID=""
DISCORD_CLIENT_SECRET=""

OCPI_DOMAIN="https://jk.adhoc.eulektro.de"
LONGSHIP_DOMAIN="https://beta.ocpi.longship.io"
CPO_TOKEN="55093f77ea08489da051565acd921b11"

NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY="pk_test_51My7zKINwl1ZJuJdWsqmtabNkXzWGcXikDZ8z4BVzOJLltgacza4EQUTTVWHjTH0n9t3gTltWHssYqk0Z0g4MbzN005q5Xxsvg"
STRIPE_SECRET_KEY="sk_test_51My7zKINwl1ZJuJdJlFMIh8lnn2dzl9M3BAnbcpBqpw7LSI9MjNX8aOqI77Opmvg5gNAtIUWuNSj7hAdDbWMrThs00gFLjWk3i"

DATABASE_URL_MONGODB="mongodb://mongo1:30001,mongo2:30002,mongo3:30003/emp?replicaSet=my-replica-set"
DATABASE_URL_MYSQL="mysql://user:password@localhost:3306/emp"