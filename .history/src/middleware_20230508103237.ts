import { NextRequest, NextResponse } from "next/server";
import { env } from "~/env.mjs";
export const config = {
  matcher: ["/ocpi/:path*", "/api/:path*"],
};


const middleware = async (request: NextRequest) => {
  const response = NextResponse.next();


  
  try {
    const body = await request.json();
    console.log(request.method,": ", request.url, "Body: ", body);
  } catch {
    console.log(request.method,": ", request.url);
  }

  
  if(request.nextUrl.pathname.startsWith("/ocpi"))
  {
    if(!isAuthenticatedOCPIRequest(request))
    {
      return new NextResponse(
        JSON.stringify({ success: false, message: 'authentication failed' }),
        { status: 401, headers: { 'content-type': 'application/json' } },
      );  
    }
  }

  if(request.nextUrl.pathname.startsWith("/api"))
  {
    if(!isAuthenticatedOCPIRequest(request))
    {
      return new NextResponse(
        JSON.stringify({ success: false, message: 'authentication failed' }),
        { status: 401, headers: { 'content-type': 'application/json' } },
      );  
    }
  }

  

  return response;
};

const isAuthenticatedOCPIRequest = (request: NextRequest) => 
{
  return env.CPO_TOKEN === request.headers.get("authorization");
  
  // TODO: identify ocpi connection and compare token from db
  return true;
}
const isAuthenticatedGeneralRequest = (request: NextRequest) => 
{
  
  const tokenFromRquest = request.headers.get("authorization");
  const tokenFromDB = "dummy";
  return tokenFromRquest === tokenFromDB;

}


export default middleware;
