import { NextRequest, NextResponse } from "next/server";
import { env } from "~/env.mjs";
export const config = {
  matcher: ["/ocpi/:path*", "/api/:path*"],
};


const middleware = async (request: NextRequest) => {
  const response = NextResponse.next();

  try {
    const body = await request.json();
    console.log(request.method,": ", request.url, "Body: ", body);
  } catch {
    console.log(request.method,": ", request.url);
  }

  return response;
};

const isAuthenticatedOCPIRequest = (request: NextRequest) => 
{
  const cpoTokenFromRequest = request.headers.get("authorization");
  if(!cpoTokenFromRequest || cpoTokenFromRequest !== env.CPO_TOKEN)
  {
    return new NextResponse(
      JSON.stringify({ success: false, message: 'authentication failed' }),
      { status: 401, headers: { 'content-type': 'application/json' } },
    );
  }
  // TODO: identify ocpi connection and compare token from db
  return true;
}
const isAuthenticatedRequest = (request: NextRequest) => 
{
  
  const tokenFromRquest = request.headers.get("authorization");
  
  // TODO: compare secret auth token
  return true;
}


export default middleware;
