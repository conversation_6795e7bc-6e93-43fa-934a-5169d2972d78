import { NextRequest, NextResponse } from "next/server";

export const config = {
  matcher: ["/ocpi/:path*", "/api/:path*"],
};


const middleware = async (request: NextRequest) => {
  const response = NextResponse.next();

  try {
    const body = await request.json();
    console.log(request.method,": ", request.url, "Body: ", body);
  } catch {
    console.log(request.method,": ", request.url);
  }

  return response;
};

const isAuthenticatedOCPIRequest = (request: NextRequest) => 
{
  const cpoTokenFromRequest = request.headers.get("authorization");
  if(!cpoTokenFromRequest || cpoTokenFromRequest !== env.CPO_TOKEN
    )
  // TODO: identify ocpi connection and compare token from db
  return true;
}
const isAuthenticatedRequest = (request: NextRequest) => 
{
  // TODO: compare secret auth token
  return true;
}


export default middleware;
