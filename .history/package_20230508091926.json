{"name": "adhoc", "version": "0.1.0", "private": true, "scripts": {"build": "next build", "dev": "next dev", "postinstall": "prisma generate", "lint": "next lint", "start": "next start", "prisma_generate": "npx prisma format && npx prisma generate"}, "prisma": {"schema": "prisma/prismaMongoDB/schema.prisma"}, "dependencies": {"@next-auth/prisma-adapter": "^1.0.6", "@prisma/client": "^4.13.0", "@stripe/react-stripe-js": "^2.1.0", "@stripe/stripe-js": "^1.52.1", "bson": "^5.2.0", "next": "^13.4.1", "next-auth": "^4.22.1", "next-https": "^1.1.10", "react": "18.2.0", "react-dom": "18.2.0", "stripe": "^12.3.0", "zod": "^3.21.4", "pdfkit": "^0.13.0"}, "devDependencies": {"@types/eslint": "^8.37.0", "@types/node": "^18.16.5", "@types/prettier": "^2.7.2", "@types/react": "^18.0.3", "@types/react-dom": "^18.2.4", "@types/uuid": "^9.0.1", "@typescript-eslint/eslint-plugin": "^5.59.2", "@typescript-eslint/parser": "^5.59.2", "autoprefixer": "^10.4.14", "eslint": "^8.39.0", "eslint-config-next": "^13.4.1", "local-ssl-proxy": "^2.0.5", "postcss": "^8.4.2", "prettier": "^2.8.8", "prettier-plugin-tailwindcss": "^0.2.8", "prisma": "^4.13.0", "tailwindcss": "^3.3.2", "typescript": "^5.0.4", "uuid": "^9.0.0", "winston": "^3.8.2", "winston-daily-rotate-file": "^4.7.1", "zod-prisma-types": "^2.5.11", "@types/pdfkit": "0.12.9"}, "ct3aMetadata": {"initVersion": "7.12.0"}}