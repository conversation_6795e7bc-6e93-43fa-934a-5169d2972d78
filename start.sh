#!/bin/bash

# Start script for OCPP Load Manager

# Check if <PERSON><PERSON> is installed
if command -v docker &> /dev/null && command -v docker-compose &> /dev/null; then
    echo "Starting with Docker..."
    docker-compose up -d
    exit 0
fi

# If Docker is not available, try to start locally
echo "Docker not found, starting locally..."

# Check if PostgreSQL is running
if command -v pg_isready &> /dev/null; then
    pg_status=$(pg_isready)
    if [[ $pg_status == *"accepting connections"* ]]; then
        echo "PostgreSQL is running"
    else
        echo "PostgreSQL is not running. Please start PostgreSQL first."
        exit 1
    fi
else
    echo "Cannot check PostgreSQL status. Make sure PostgreSQL is running."
fi

# Check if .env file exists
if [ ! -f .env ]; then
    echo "Creating .env file from .env.example"
    cp .env.example .env
fi

# Install dependencies if needed
if [ ! -d "node_modules" ]; then
    echo "Installing dependencies..."
    npm install
fi

# Run database migrations if needed
echo "Running database migrations..."
npm run db:migrate

# Seed the database
echo "Seeding the database..."
npm run db:seed

# Start the application
echo "Starting the application..."
npm run dev
