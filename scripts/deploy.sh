#!/bin/bash

# Farben für bessere Lesbarkeit
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Banner
echo -e "${BLUE}╔══════════════════════════════════════════════════════════════╗${NC}"
echo -e "${BLUE}║                    🚀 DEPLOYMENT SCRIPT                      ║${NC}"
echo -e "${BLUE}║              EulektroTerminalVerwaltung                      ║${NC}"
echo -e "${BLUE}╚══════════════════════════════════════════════════════════════╝${NC}"
echo ""

# Aktueller Branch anzeigen
CURRENT_BRANCH=$(git branch --show-current 2>/dev/null || echo "unknown")
echo -e "${YELLOW}📍 Aktueller Branch:${NC} ${CURRENT_BRANCH}"

# Git Status prüfen
if git diff-index --quiet HEAD -- 2>/dev/null; then
    echo -e "${GREEN}✅ Git Status: Sauber (keine uncommitted changes)${NC}"
else
    echo -e "${RED}⚠️  Git Status: Es gibt uncommitted changes!${NC}"
    echo -e "${YELLOW}   Möchten Sie trotzdem fortfahren? (nicht empfohlen)${NC}"
fi

echo ""

# Deployment-Typ bestimmen
DEPLOY_TYPE=""
DEPLOY_COMMAND=""

if [ "$1" = "beta" ]; then
    DEPLOY_TYPE="BETA"
    DEPLOY_COMMAND="pm2 deploy ecosystem.config.js beta"
    echo -e "${YELLOW}🧪 BETA DEPLOYMENT${NC}"
    echo -e "${YELLOW}   Ziel: Beta-Server${NC}"
elif [ "$1" = "production" ] || [ -z "$1" ]; then
    DEPLOY_TYPE="PRODUCTION"
    DEPLOY_COMMAND="pm2 deploy ecosystem.config.js production"
    echo -e "${RED}🔥 PRODUCTION DEPLOYMENT${NC}"
    echo -e "${RED}   Ziel: Production-Server${NC}"
else
    echo -e "${RED}❌ Unbekannter Deployment-Typ: $1${NC}"
    echo -e "${YELLOW}   Verfügbare Optionen: production, beta${NC}"
    exit 1
fi

echo ""
echo -e "${BLUE}📋 Deployment Details:${NC}"
echo -e "   • Branch: ${CURRENT_BRANCH}"
echo -e "   • Typ: ${DEPLOY_TYPE}"
echo -e "   • Command: ${DEPLOY_COMMAND}"
echo ""

# Sicherheitsabfrage
echo -e "${YELLOW}⚠️  ACHTUNG: Sie sind dabei, auf ${DEPLOY_TYPE} zu deployen!${NC}"
echo ""
read -p "🤔 Sind Sie sicher, dass Sie fortfahren möchten? (yes/no): " -r
echo ""

if [[ ! $REPLY =~ ^[Yy][Ee][Ss]$ ]]; then
    echo -e "${RED}❌ Deployment abgebrochen.${NC}"
    exit 1
fi

# Zusätzliche Bestätigung für Production
if [ "$DEPLOY_TYPE" = "PRODUCTION" ]; then
    echo -e "${RED}🚨 LETZTE WARNUNG: PRODUCTION DEPLOYMENT!${NC}"
    read -p "🔥 Tippen Sie 'DEPLOY' um zu bestätigen: " -r
    echo ""
    
    if [[ ! $REPLY = "DEPLOY" ]]; then
        echo -e "${RED}❌ Production Deployment abgebrochen.${NC}"
        echo -e "${YELLOW}   (Sie müssen exakt 'DEPLOY' eingeben)${NC}"
        exit 1
    fi
fi

# Deployment starten
echo -e "${GREEN}🚀 Starte Deployment...${NC}"
echo ""

# PM2 Deployment ausführen
eval $DEPLOY_COMMAND

# Ergebnis prüfen
if [ $? -eq 0 ]; then
    echo ""
    echo -e "${GREEN}✅ Deployment erfolgreich abgeschlossen!${NC}"
    echo -e "${BLUE}🎉 ${DEPLOY_TYPE} ist jetzt live!${NC}"
else
    echo ""
    echo -e "${RED}❌ Deployment fehlgeschlagen!${NC}"
    echo -e "${YELLOW}   Bitte prüfen Sie die Logs oben.${NC}"
    exit 1
fi
