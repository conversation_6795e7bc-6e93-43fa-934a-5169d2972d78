const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testRoamingMatrixFlag() {
  try {
    console.log('Testing visibleInRoamingMatrix flag...');

    // Get first contact
    const firstContact = await prisma.contact.findFirst();
    
    if (!firstContact) {
      console.log('No contacts found in database');
      return;
    }

    console.log(`Found contact: ${firstContact.name || firstContact.companyName || firstContact.id}`);
    console.log(`Current visibleInRoamingMatrix: ${firstContact.visibleInRoamingMatrix}`);

    // Update the contact to set the flag
    const updatedContact = await prisma.contact.update({
      where: { id: firstContact.id },
      data: { visibleInRoamingMatrix: true },
    });

    console.log(`Updated visibleInRoamingMatrix to: ${updatedContact.visibleInRoamingMatrix}`);

    // Test the roaming matrix API query
    const roamingMatrixContacts = await prisma.contact.findMany({
      where: {
        visibleInRoamingMatrix: true,
      },
      include: {
        ou: {
          select: {
            id: true,
            name: true,
            code: true,
          },
        },
        tarifs: {
          include: {
            tarif: {
              include: {
                validOus: {
                  select: {
                    id: true,
                    name: true,
                    code: true,
                  },
                },
              },
            },
          },
        },
        creditTarifs: {
          include: {
            creditTarif: true,
          },
        },
      },
    });

    console.log(`Found ${roamingMatrixContacts.length} contacts with visibleInRoamingMatrix=true`);
    
    roamingMatrixContacts.forEach(contact => {
      console.log(`- ${contact.name || contact.companyName || contact.id} (${contact.tarifs.length} tariffs)`);
    });

  } catch (error) {
    console.error('Error testing roaming matrix flag:', error);
  } finally {
    await prisma.$disconnect();
  }
}

testRoamingMatrixFlag();
