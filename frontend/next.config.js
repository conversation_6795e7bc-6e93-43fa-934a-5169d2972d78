/**
 * Run `build` or `dev` with `SKIP_ENV_VALIDATION` to skip env validation. This is especially useful
 * for Docker builds.
 */
import "./src/env.js";
import { env } from "./src/env.js";

/** @type {import("next").NextConfig} */
const config = {
	images: {
		remotePatterns: [
			// @ts-ignore
			{
				protocol: env.NEXT_PUBLIC_IMAGES_PROTOCOL,
				hostname: env.NEXT_PUBLIC_IMAGES_HOSTNAME,
				port: env.NEXT_PUBLIC_IMAGES_PORT,
				pathname: env.NEXT_PUBLIC_IMAGES_PATHNAME,
			},
		],
	},
};

export default config;
