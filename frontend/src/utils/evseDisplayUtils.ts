/**
 * Utility functions for EVSE display identifiers
 * Implements hierarchical fallback system for charging point identifiers
 */

export interface EVSEIdentifierFields {
  physicalReference?: string | null;
  evseId?: string | null;
  uid?: string | null;
}

/**
 * Gets the best available display identifier for an EVSE using hierarchical fallback
 * 
 * Priority order:
 * 1. physicalReference (if available and not empty)
 * 2. evseId (if physicalReference not available or empty)
 * 3. uid (as final fallback)
 * 
 * @param evse - EVSE object with identifier fields
 * @param fallbackPrefix - Optional prefix for generated fallback (default: "Ladepunkt")
 * @param fallbackIndex - Optional index for generated fallback
 * @returns The best available identifier string
 */
export function getEVSEDisplayIdentifier(
  evse: EVSEIdentifierFields,
  fallbackPrefix: string = "Ladepunkt",
  fallbackIndex?: number
): string {
  // Check physicalReference first
  if (evse.physicalReference && evse.physicalReference.trim() !== '') {
    return evse.physicalReference.trim();
  }
  
  // Check evseId second
  if (evse.evseId && evse.evseId.trim() !== '') {
    return evse.evseId.trim();
  }
  
  // Check uid third
  if (evse.uid && evse.uid.trim() !== '') {
    return evse.uid.trim();
  }
  
  // Final fallback with optional index
  if (fallbackIndex !== undefined) {
    return `${fallbackPrefix} *${fallbackIndex.toString().padStart(2, '0')}`;
  }
  
  return fallbackPrefix;
}

/**
 * Gets a shortened display identifier for terminal screens (max 14 characters)
 * Uses the hierarchical fallback and truncates if necessary
 * 
 * @param evse - EVSE object with identifier fields
 * @param fallbackIndex - Optional index for generated fallback
 * @param maxLength - Maximum length for display (default: 14)
 * @returns Shortened identifier suitable for terminal display
 */
export function getEVSEDisplayIdentifierShort(
  evse: EVSEIdentifierFields,
  fallbackIndex?: number,
  maxLength: number = 14
): string {
  let displayName = getEVSEDisplayIdentifier(evse, "Ladepunkt", fallbackIndex);
  
  // Truncate if too long, showing last characters with "..." prefix
  if (displayName.length > maxLength) {
    const remainingLength = maxLength - 3; // Account for "..."
    displayName = '...' + displayName.slice(-remainingLength);
  }
  
  return displayName;
}

/**
 * Gets display identifier with additional context (e.g., for dropdowns)
 * Shows primary identifier with secondary info in parentheses
 * 
 * @param evse - EVSE object with identifier fields
 * @param showSecondaryInfo - Whether to show secondary identifier info
 * @param fallbackIndex - Optional index for generated fallback
 * @returns Identifier with optional secondary context
 */
export function getEVSEDisplayIdentifierWithContext(
  evse: EVSEIdentifierFields & { labelForTerminal?: string },
  showSecondaryInfo: boolean = true,
  fallbackIndex?: number
): string {
  const primaryId = getEVSEDisplayIdentifier(evse, "Ladepunkt", fallbackIndex);
  
  if (!showSecondaryInfo) {
    return primaryId;
  }
  
  // Collect secondary information
  const secondaryInfo: string[] = [];
  
  // Add labelForTerminal if available and different from primary
  if (evse.labelForTerminal && evse.labelForTerminal.trim() !== '' && 
      evse.labelForTerminal.trim() !== primaryId) {
    secondaryInfo.push(evse.labelForTerminal.trim());
  }
  
  // Add other identifiers that weren't used as primary
  if (evse.physicalReference && evse.physicalReference.trim() !== primaryId) {
    secondaryInfo.push(`Ref: ${evse.physicalReference.trim()}`);
  } else if (evse.evseId && evse.evseId.trim() !== primaryId) {
    secondaryInfo.push(`ID: ${evse.evseId.trim()}`);
  } else if (evse.uid && evse.uid.trim() !== primaryId) {
    secondaryInfo.push(`UID: ${evse.uid.trim()}`);
  }
  
  if (secondaryInfo.length > 0) {
    return `${primaryId} (${secondaryInfo.join(', ')})`;
  }
  
  return primaryId;
}

/**
 * Sorts EVSEs by their display identifier for consistent ordering
 * 
 * @param evses - Array of EVSE objects
 * @returns Sorted array of EVSEs
 */
export function sortEVSEsByDisplayIdentifier<T extends EVSEIdentifierFields>(
  evses: T[]
): T[] {
  return [...evses].sort((a, b) => {
    const aId = getEVSEDisplayIdentifier(a);
    const bId = getEVSEDisplayIdentifier(b);
    return aId.localeCompare(bId, 'de', { numeric: true });
  });
}
