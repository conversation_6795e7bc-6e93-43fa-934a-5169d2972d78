/**
 * Formatierungsfunktionen
 */

/**
 * Formatiert einen Betrag als Währung (EUR)
 * @param amount - Der zu formatierende Betrag
 * @returns Formatierter Betrag als String
 */
export function formatCurrency(amount: number | undefined | null): string {
	if (amount === undefined || amount === null) return "-";

	return new Intl.NumberFormat("de-DE", {
		style: "currency",
		currency: "EUR",
		minimumFractionDigits: 2,
		maximumFractionDigits: 2,
	}).format(amount);
}

/**
 * Formatiert eine Zahl mit Tausendertrennzeichen
 * @param number - Die zu formatierende Zahl
 * @returns Formatierte Zahl als String
 */
export function formatNumber(number: number | undefined | null): string {
	if (number === undefined || number === null) return "-";

	return new Intl.NumberFormat("de-DE").format(number);
}
