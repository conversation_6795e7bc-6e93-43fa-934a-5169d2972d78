/**
 * TypeScript type definitions for EPEX electricity price data
 */

// Strapi EPEX API Response Types
export interface StrapiEPEXPrice {
  id: number;
  documentId: string;
  timestamp: string;
  marketprice: number;
  unit: string;
  source: string;
  year: number;
  month: number;
  day: number;
  hour: number;
  minute: number;
  fetchedAt: string;
  createdAt: string;
  updatedAt: string;
}

// Processed Price Data Types
export interface EPEXPricePoint {
  timestamp: Date;
  hour: number;
  basePrice: number; // EUR/MWh
  priceWithVAT?: number; // EUR/MWh with VAT
  priceWithNetworkFees?: number; // EUR/MWh with network fees
  finalPrice?: number; // EUR/MWh final price for customer
}

export interface EPEXPriceData {
  date: string; // YYYY-MM-DD format
  prices: EPEXPricePoint[];
  currency: string;
  unit: string;
  source: 'AWATTAR' | 'CACHE';
  lastUpdated: Date;
}

// Chart Configuration Types
export interface ChartConfig {
  showVAT: boolean;
  vatRate: number; // Default 19% for Germany
  networkFees: number; // EUR/MWh
  dateRange: {
    start: Date;
    end: Date;
  };
  timeZone: string; // Default 'Europe/Berlin'
}

// API Request Types (for Strapi)
export interface EPEXPriceRequest {
  startDate: string; // YYYY-MM-DD
  endDate: string; // YYYY-MM-DD
}

// Component Props Types
export interface DateRangeSelectorProps {
  startDate: Date;
  endDate: Date;
  onDateRangeChange: (start: Date, end: Date) => void;
  maxDate?: Date;
  minDate?: Date;
}

export interface PriceControlsProps {
  showVAT: boolean;
  vatRate: number;
  networkFees: number;
  onVATToggle: (show: boolean) => void;
  onVATRateChange: (rate: number) => void;
  onNetworkFeesChange: (fees: number) => void;
  // Tariff selection props
  selectedTariffs: string[];
  availableTariffs: TariffOption[];
  onTariffSelectionChange: (tariffIds: string[]) => void;
  tariffPriceData: TariffPriceData[];
}

export interface EPEXPriceChartProps {
  data: EPEXPriceData[];
  config: ChartConfig;
  loading?: boolean;
  error?: string | null;
  height?: number;
  // Tariff pricing data
  tariffPriceData?: TariffPriceData[];
}

// Error Types
export interface EPEXAPIError {
  code: string;
  message: string;
  details?: any;
  timestamp: Date;
}

// Cache Types
export interface EPEXPriceCache {
  [key: string]: {
    data: EPEXPriceData;
    expiry: Date;
  };
}

// Utility Types
export type DateRange = {
  start: Date;
  end: Date;
};

export type PriceUnit = 'EUR/MWh' | 'EUR/kWh' | 'ct/kWh';

export type TimeResolution = 'PT15M' | 'PT60M' | 'P1D';

// Tariff-related Types for EPEX Integration
export interface TariffOption {
  id: string;
  documentId: string;
  name: string;
  hasAC: boolean;
  hasDC: boolean;
}

export interface TariffPricePoint {
  timestamp: Date;
  hour: number;
  acPrice?: number; // EUR/kWh for AC charging (base price without VAT)
  dcPrice?: number; // EUR/kWh for DC charging (base price without VAT)
  acFinalPrice?: number; // EUR/kWh for AC charging (final customer price with VAT)
  dcFinalPrice?: number; // EUR/kWh for DC charging (final customer price with VAT)
  sessionFee?: number; // EUR per session
}

export interface TariffPriceData {
  tariffId: string;
  tariffName: string;
  date: string; // YYYY-MM-DD format
  prices: TariffPricePoint[];
  hasAC: boolean;
  hasDC: boolean;
}

// Constants
export const EPEX_CONSTANTS = {
  DEFAULT_VAT_RATE: 19, // 19% VAT for Germany
  CACHE_DURATION_HOURS: 1, // Cache data for 1 hour
  MAX_DATE_RANGE_DAYS: 31, // Maximum 31 days range
} as const;
