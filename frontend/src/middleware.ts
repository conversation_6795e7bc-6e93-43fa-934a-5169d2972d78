import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";

export async function middleware(req: NextRequest) {
	// Token aus den Cookies holen
	const token = req.cookies.get("strapiToken")?.value;

	// Pfad aus der URL extrahieren
	const path = req.nextUrl.pathname;

	// Geschützte Routen definieren
	const isProtectedRoute =
		path.startsWith("/dashboard") ||
		path.includes("/(protected)/") ||
		path.startsWith("/standorte") ||
		path.startsWith("/terminals") ||
		path.startsWith("/tariff") ||
		path.startsWith("/epex-prices") ||
		path.startsWith("/umsaetze") ||
		path.startsWith("/sessions") ||
		path.startsWith("/rechnungen") ||
		path.startsWith("/anbindung") ||
		path.startsWith("/cron-jobs") ||
		path.startsWith("/einstellungen");

	// Wenn es eine geschützte Route ist und kein Token vorhanden ist, umleiten
	if (isProtectedRoute && !token) {
		const loginUrl = new URL("/login", req.url);
		loginUrl.searchParams.set("callbackUrl", req.nextUrl.pathname);
		return NextResponse.redirect(loginUrl);
	}

	// Wenn der Benutzer bereits eingeloggt ist und versucht, auf Login-Seite zuzugreifen
	if ((path === "/login" || path === "/") && token) {
		return NextResponse.redirect(new URL("/dashboard", req.url));
	}

	// Falls kein Umleitungsfall vorliegt, mit der Anfrage fortfahren
	return NextResponse.next();
}

// Definieren, für welche Pfade die Middleware ausgeführt werden soll
export const config = {
	matcher: [
		// Geschützte Routen
		"/(protected)/:path*",
		"/dashboard/:path*",
		"/standorte/:path*",
		"/terminals/:path*",
		"/tariff/:path*",
		"/epex-prices/:path*",
		"/umsaetze/:path*",
		"/sessions/:path*",
		"/rechnungen/:path*",
		"/anbindung/:path*",
		"/payment-connections/:path*",
		"/cron-jobs/:path*",
		"/einstellungen/:path*",
		"/users/:path*",
		// Authentifizierungsrouten
		"/login",
		"/",
	],
};
