/**
 * Cron Jobs API Service
 *
 * This service provides functions for managing cron jobs through the backend API.
 */

import axios from 'axios';

// Create axios instance for cron jobs API
const cronJobsApiClient = axios.create({
  baseURL: process.env.NEXT_PUBLIC_STRAPI_API_URL || "http://localhost:1337",
  headers: {
    "Content-Type": "application/json",
  },
});

// Add auth token to requests
cronJobsApiClient.interceptors.request.use((config) => {
  const token = typeof window !== 'undefined' ? localStorage.getItem("strapiToken") : null;
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

export interface CronJobExecution {
  id: string;
  documentId: string;
  jobName: string;
  status: 'RUNNING' | 'SUCCESS' | 'FAILED' | 'CANCELLED';
  startTime: string;
  endTime?: string;
  duration?: number;
  result?: any;
  errorMessage?: string;
  summary?: string;
  triggeredBy: 'CRON' | 'MANUAL';
  triggeredByUser?: {
    username: string;
    email: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface CronJobWithStatus {
  jobName: string;
  schedule: string;
  description: string;
  latestExecution?: CronJobExecution;
}

export interface AvailableJob {
  name: string;
  displayName: string;
  description: string;
  schedule: string;
  enabled: boolean;
}

export interface TriggerJobResponse {
  message: string;
  execution: CronJobExecution;
  result: any;
}

/**
 * API service for cron job management
 */
export const cronJobsApi = {
  /**
   * Get all cron jobs with their status
   */
  async getAllJobsWithStatus(): Promise<CronJobWithStatus[]> {
    try {
      const response = await cronJobsApiClient.get('/api/cron-jobs/status');
      return response.data;
    } catch (error) {
      console.error('Error fetching cron jobs status:', error);
      throw error;
    }
  },

  /**
   * Get execution history for a specific job
   */
  async getExecutionHistory(jobName: string, limit: number = 50): Promise<CronJobExecution[]> {
    try {
      const response = await cronJobsApiClient.get(`/api/cron-jobs/${jobName}/history`, {
        params: { limit }
      });
      return response.data;
    } catch (error) {
      console.error(`Error fetching execution history for ${jobName}:`, error);
      throw error;
    }
  },

  /**
   * Manually trigger a cron job
   */
  async triggerJob(jobName: string): Promise<TriggerJobResponse> {
    try {
      const response = await cronJobsApiClient.post(`/api/cron-jobs/${jobName}/trigger`);
      return response.data;
    } catch (error) {
      console.error(`Error triggering job ${jobName}:`, error);
      throw error;
    }
  },

  /**
   * Get available cron jobs
   */
  async getAvailableJobs(): Promise<AvailableJob[]> {
    try {
      const response = await cronJobsApiClient.get('/api/cron-jobs/available');
      return response.data;
    } catch (error) {
      console.error('Error fetching available jobs:', error);
      throw error;
    }
  },

  /**
   * Get all cron job executions (for admin overview)
   */
  async getAllExecutions(limit: number = 100): Promise<CronJobExecution[]> {
    try {
      const response = await cronJobsApiClient.get('/api/cron-job-executions', {
        params: {
          'sort[0]': 'startTime:desc',
          'pagination[limit]': limit,
          'populate[triggeredByUser][fields][0]': 'username',
          'populate[triggeredByUser][fields][1]': 'email',
        }
      });
      return response.data.data;
    } catch (error) {
      console.error('Error fetching all executions:', error);
      throw error;
    }
  },
};
