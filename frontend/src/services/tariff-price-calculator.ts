/**
 * Tariff Price Calculator Service
 * Calculates tariff prices for given date ranges and integrates with EPEX data
 */

import { format, addHours, startOfDay } from 'date-fns';
import { tariffsApi } from './tariffs-api';
import type { TariffOption, TariffPriceData, TariffPricePoint } from '~/types/epex.types';

// Real tariff data structure from Strapi
interface HourlyRateData {
  hourFrom: number;
  hourTo?: number;
  pricePerKwh: number; // EUR/kWh
  sessionFee: number; // EUR
  priceType: 'EPEX' | 'FIXED';
  fallbackPricePerKwh?: number; // EUR/kWh
  minPricePerKwh?: number; // Cent/kWh (stored as integer in Strapi)
  maxPricePerKwh?: number; // Cent/kWh (stored as integer in Strapi)
  marginType?: 'fixed' | 'percentage';
  marginValue?: number; // EUR or percentage
  taxesAndLevies?: number; // Cent/kWh (stored as biginteger in Strapi)
  epexAveragingPeriodHours?: number;
}

interface DailyScheduleData {
  dayOfWeek: 'monday' | 'tuesday' | 'wednesday' | 'thursday' | 'friday' | 'saturday' | 'sunday';
  hourlyRate: HourlyRateData[];
}

interface PriceComponentData {
  validFrom?: string;
  validTo?: string;
  dailySchedules: DailyScheduleData[];
  changeDate?: string;
}

interface TariffData {
  id: string;
  documentId: string;
  name: string;
  priceAC?: PriceComponentData[];
  priceDC?: PriceComponentData[];
}

/**
 * Calculate tariff prices for a given date range
 */
export async function calculateTariffPrices(
  tariffs: TariffOption[],
  startDate: Date,
  endDate: Date,
  epexPrices?: { timestamp: Date; basePrice: number }[],
  vatConfig?: { showVAT: boolean; vatRate: number }
): Promise<TariffPriceData[]> {
  // Debug: Log tariff calculation request
  console.log('🔍 calculateTariffPrices called with:', {
    tariffs: tariffs.map(t => ({ id: t.documentId, name: t.name })),
    startDate: startDate.toISOString(),
    endDate: endDate.toISOString(),
    epexPricesCount: epexPrices?.length || 0
  });

  const results: TariffPriceData[] = [];

  for (const tariff of tariffs) {
    try {
      console.log(`📋 Fetching tariff data for: ${tariff.name} (${tariff.documentId})`);

      // Fetch tariff details from Strapi
      const tariffData = await fetchTariffData(tariff.documentId);

      if (!tariffData) {
        console.warn(`❌ Could not fetch data for tariff ${tariff.name}`);
        continue;
      }

      console.log(`✅ Fetched tariff data for ${tariff.name}:`, {
        id: tariffData.documentId,
        name: tariffData.name,
        hasAC: !!(tariffData.priceAC && tariffData.priceAC.length > 0),
        hasDC: !!(tariffData.priceDC && tariffData.priceDC.length > 0),
        priceACCount: tariffData.priceAC?.length || 0,
        priceDCCount: tariffData.priceDC?.length || 0
      });

      const tariffPriceData = calculateTariffPriceData(
        tariffData,
        startDate,
        endDate,
        epexPrices,
        vatConfig
      );

      console.log(`💰 Calculated price data for ${tariff.name}:`, {
        tariffId: tariffPriceData.tariffId,
        pricesCount: tariffPriceData.prices.length,
        hasAC: tariffPriceData.hasAC,
        hasDC: tariffPriceData.hasDC,
        samplePrices: tariffPriceData.prices.slice(0, 3).map(p => ({
          timestamp: p.timestamp.toISOString(),
          hour: p.hour,
          acPrice: p.acPrice,
          dcPrice: p.dcPrice
        }))
      });

      results.push(tariffPriceData);
    } catch (error) {
      console.error(`❌ Error calculating prices for tariff ${tariff.name}:`, error);
    }
  }

  console.log(`🎯 Final results:`, results.map(r => ({
    tariffId: r.tariffId,
    tariffName: r.tariffName,
    pricesCount: r.prices.length,
    hasAC: r.hasAC,
    hasDC: r.hasDC
  })));

  return results;
}

/**
 * Calculate price data for a single tariff
 */
function calculateTariffPriceData(
  tariff: TariffData,
  startDate: Date,
  endDate: Date,
  epexPrices?: { timestamp: Date; basePrice: number }[],
  vatConfig?: { showVAT: boolean; vatRate: number }
): TariffPriceData {
  const prices: TariffPricePoint[] = [];

  // Generate hourly prices for the date range
  let currentDate = startOfDay(startDate);
  const endDateTime = startOfDay(endDate);

  while (currentDate <= endDateTime) {
    for (let hour = 0; hour < 24; hour++) {
      const timestamp = addHours(currentDate, hour);

      // Find corresponding EPEX price for this hour
      const epexPrice = epexPrices?.find(
        price => price.timestamp.getTime() === timestamp.getTime()
      );

      const pricePoint: TariffPricePoint = {
        timestamp,
        hour,
      };

      // Calculate AC price if available
      if (tariff.priceAC && tariff.priceAC.length > 0) {
        const acHourlyRate = findValidHourlyRate(tariff.priceAC, timestamp);
        if (acHourlyRate) {
          const basePrice = calculatePriceFromHourlyRate(acHourlyRate, epexPrice?.basePrice);
          pricePoint.acPrice = basePrice;

          // Calculate final customer price with VAT if enabled
          if (vatConfig?.showVAT) {
            pricePoint.acFinalPrice = basePrice * (1 + vatConfig.vatRate / 100);
          } else {
            pricePoint.acFinalPrice = basePrice;
          }

          pricePoint.sessionFee = acHourlyRate.sessionFee;
        }
      }

      // Calculate DC price if available
      if (tariff.priceDC && tariff.priceDC.length > 0) {
        const dcHourlyRate = findValidHourlyRate(tariff.priceDC, timestamp);
        if (dcHourlyRate) {
          const basePrice = calculatePriceFromHourlyRate(dcHourlyRate, epexPrice?.basePrice);
          pricePoint.dcPrice = basePrice;

          // Calculate final customer price with VAT if enabled
          if (vatConfig?.showVAT) {
            pricePoint.dcFinalPrice = basePrice * (1 + vatConfig.vatRate / 100);
          } else {
            pricePoint.dcFinalPrice = basePrice;
          }

          if (!pricePoint.sessionFee) {
            pricePoint.sessionFee = dcHourlyRate.sessionFee;
          }
        }
      }

      prices.push(pricePoint);
    }

    currentDate = addHours(currentDate, 24);
  }

  return {
    tariffId: tariff.documentId,
    tariffName: tariff.name,
    date: format(startDate, 'yyyy-MM-dd'),
    prices,
    hasAC: !!(tariff.priceAC && tariff.priceAC.length > 0),
    hasDC: !!(tariff.priceDC && tariff.priceDC.length > 0),
  };
}

/**
 * Find valid hourly rate for a given timestamp
 * Checks validity periods and finds the appropriate hourly rate
 */
function findValidHourlyRate(
  priceComponents: PriceComponentData[],
  timestamp: Date
): HourlyRateData | null {
  if (!priceComponents || priceComponents.length === 0) {
    return null;
  }

  // Find a valid price component based on validity period
  const validComponent = priceComponents.find(component => {
    const validFrom = component.validFrom ? new Date(component.validFrom) : null;
    const validTo = component.validTo ? new Date(component.validTo) : null;

    // Check if the timestamp falls within the validity period
    if (validFrom && timestamp < validFrom) {
      return false;
    }
    if (validTo && timestamp > validTo) {
      return false;
    }

    return true;
  });

  if (!validComponent) {
    return null;
  }

  // Find the appropriate hourly rate for this timestamp
  const dayOfWeek = getDayOfWeekString(timestamp);
  const hour = timestamp.getHours();

  const dailySchedule = validComponent.dailySchedules?.find(
    schedule => schedule.dayOfWeek === dayOfWeek
  );

  if (!dailySchedule || !dailySchedule.hourlyRate) {
    return null;
  }

  // Find the hourly rate that applies to this hour
  const hourlyRate = dailySchedule.hourlyRate.find(rate => {
    const hourFrom = rate.hourFrom;
    const hourTo = rate.hourTo !== undefined ? rate.hourTo : hourFrom;

    // Handle the case where hourTo might be the same as hourFrom (single hour)
    if (hourFrom === hourTo) {
      return hour === hourFrom;
    }

    // Handle normal range (inclusive)
    return hour >= hourFrom && hour <= hourTo;
  });

  return hourlyRate || null;
}

/**
 * Convert day number to day string
 */
function getDayOfWeekString(date: Date): string {
  const days = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
  return days[date.getDay()] || 'sunday';
}

/**
 * Calculate price based on hourly rate configuration
 */
function calculatePriceFromHourlyRate(
  hourlyRate: HourlyRateData,
  epexBasePrice?: number
): number {
  if (hourlyRate.priceType === 'FIXED') {
    return hourlyRate.pricePerKwh;
  }

  // EPEX-based pricing
  if (hourlyRate.priceType === 'EPEX') {
    if (epexBasePrice === undefined) {
      return hourlyRate.fallbackPricePerKwh || hourlyRate.pricePerKwh || 0;
    }

    let price = epexBasePrice; // EUR/kWh

    // Apply margin
    if (hourlyRate.marginType === 'fixed' && hourlyRate.marginValue) {
      price += hourlyRate.marginValue; // EUR/kWh
    } else if (hourlyRate.marginType === 'percentage' && hourlyRate.marginValue) {
      price *= (1 + hourlyRate.marginValue / 100);
    }

    // Add taxes and levies (convert from cent to EUR)
    if (hourlyRate.taxesAndLevies) {
      price += hourlyRate.taxesAndLevies / 100; // Convert cent/kWh to EUR/kWh
    }

    // Apply min/max limits (convert from cent to EUR)
    if (hourlyRate.minPricePerKwh && price < (hourlyRate.minPricePerKwh / 100)) {
      price = hourlyRate.minPricePerKwh / 100;
    }
    if (hourlyRate.maxPricePerKwh && price > (hourlyRate.maxPricePerKwh / 100)) {
      price = hourlyRate.maxPricePerKwh / 100;
    }

    return price;
  }

  // Fallback price if EPEX data is not available
  return hourlyRate.fallbackPricePerKwh || hourlyRate.pricePerKwh || 0;
}

/**
 * Fetch tariff data from Strapi
 */
async function fetchTariffData(documentId: string): Promise<any | null> {
  try {
    const response = await tariffsApi.getByDocumentId(documentId);
    return response.data || null;
  } catch (error) {
    console.error(`❌ Error fetching tariff ${documentId}:`, error);
    return null;
  }
}

/**
 * Get available tariffs for selection based on current mandant
 */
export async function getAvailableTariffs(mandantId?: string): Promise<TariffOption[]> {
  try {
    const filters: any = {
      deleted: { $ne: true }
    };

    // Filter by mandant if provided
    if (mandantId) {
      filters.mandants = {
        documentId: { $eq: mandantId }
      };
    }

    const response = await tariffsApi.getAll({
      filters,
      populate: ['mandants', 'priceAC', 'priceDC']
    });

    if (!response.data) {
      return [];
    }

    return response.data.map((tariff: any) => ({
      id: tariff.id,
      documentId: tariff.documentId,
      name: tariff.name,
      hasAC: !!(tariff.priceAC && tariff.priceAC.length > 0),
      hasDC: !!(tariff.priceDC && tariff.priceDC.length > 0),
    }));
  } catch (error) {
    console.error('Error fetching available tariffs:', error);
    return [];
  }
}
