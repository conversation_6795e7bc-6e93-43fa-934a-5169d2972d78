/**
 * EPEX API Service for fetching electricity price data
 * Fetches data from Strapi backend database
 */

import { format, addDays, startOfDay, endOfDay } from 'date-fns';
import { getAuthenticatedClient } from './strapi-client';
import type {
  EPEXPriceData,
  EPEXPricePoint,
  EPEXPriceCache
} from '~/types/epex.types';
import { EPEX_CONSTANTS } from '~/types/epex.types';

// In-memory cache for price data
const priceCache: EPEXPriceCache = {};

// Strapi EPEX data interface
interface StrapiEPEXPrice {
  id: number;
  documentId: string;
  timestamp: string;
  marketprice: number;
  unit: string;
  source: string;
  year: number;
  month: number;
  day: number;
  hour: number;
  minute: number;
  fetchedAt: string;
  createdAt: string;
  updatedAt: string;
}

interface StrapiEPEXResponse {
  data: StrapiEPEXPrice[];
  meta: {
    pagination: {
      page: number;
      pageSize: number;
      pageCount: number;
      total: number;
    };
  };
}

/**
 * EPEX API Service Class
 */
export class EPEXAPIService {
  private static instance: EPEXAPIService;

  private constructor() {
    // No API token needed - we fetch from Strapi
  }

  public static getInstance(): EPEXAPIService {
    if (!EPEXAPIService.instance) {
      EPEXAPIService.instance = new EPEXAPIService();
    }
    return EPEXAPIService.instance;
  }

  /**
   * Get cache key for a date range
   */
  private getCacheKey(startDate: Date, endDate: Date): string {
    return `${format(startDate, 'yyyy-MM-dd')}_${format(endDate, 'yyyy-MM-dd')}`;
  }

  /**
   * Check if cached data is still valid
   */
  private isCacheValid(cacheEntry: { data: EPEXPriceData; expiry: Date }): boolean {
    return new Date() < cacheEntry.expiry;
  }

  /**
   * Get cached data if available and valid
   */
  private getCachedData(startDate: Date, endDate: Date): EPEXPriceData[] | null {
    const cacheKey = this.getCacheKey(startDate, endDate);
    const cached = priceCache[cacheKey];

    if (cached && this.isCacheValid(cached)) {
      return [cached.data];
    }

    return null;
  }

  /**
   * Cache price data
   */
  private cacheData(startDate: Date, endDate: Date, data: EPEXPriceData): void {
    const cacheKey = this.getCacheKey(startDate, endDate);
    const expiry = new Date();
    expiry.setHours(expiry.getHours() + EPEX_CONSTANTS.CACHE_DURATION_HOURS);

    priceCache[cacheKey] = {
      data,
      expiry
    };
  }

  /**
   * Convert Strapi EPEX data to EPEXPriceData format
   */
  private convertStrapiDataToEPEXData(strapiPrices: StrapiEPEXPrice[]): EPEXPriceData[] {
    if (!strapiPrices || strapiPrices.length === 0) {
      return [];
    }

    // Group prices by date
    const pricesByDate = new Map<string, EPEXPricePoint[]>();

    for (const strapiPrice of strapiPrices) {
      const timestamp = new Date(strapiPrice.timestamp);
      const dateKey = format(timestamp, 'yyyy-MM-dd');

      if (!pricesByDate.has(dateKey)) {
        pricesByDate.set(dateKey, []);
      }

      const pricePoint: EPEXPricePoint = {
        timestamp,
        hour: timestamp.getHours(),
        basePrice: strapiPrice.marketprice / 1000, // Convert from EUR/MWh to EUR/kWh
      };

      pricesByDate.get(dateKey)!.push(pricePoint);
    }

    // Convert to EPEXPriceData array
    const result: EPEXPriceData[] = [];

    for (const [date, prices] of pricesByDate.entries()) {
      // Sort prices by timestamp
      prices.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());

      result.push({
        date,
        prices,
        currency: 'EUR',
        unit: 'EUR/kWh',
        source: 'AWATTAR',
        lastUpdated: new Date()
      });
    }

    return result.sort((a, b) => a.date.localeCompare(b.date));
  }

  /**
   * Fetch price data from Strapi backend
   */
  private async fetchFromStrapi(startDate: Date, endDate: Date): Promise<EPEXPriceData[]> {
    try {
      const client = getAuthenticatedClient();

      // Format dates for Strapi query
      const startDateISO = startOfDay(startDate).toISOString();
      const endDateISO = endOfDay(endDate).toISOString();

      console.log(`Fetching EPEX data from ${startDateISO} to ${endDateISO}`);

      // Fetch EPEX prices from Strapi using the correct API syntax
      const response = await client.collection('epexes').find({
        filters: {
          timestamp: {
            $gte: startDateISO,
            $lte: endDateISO
          }
        },
        sort: ['timestamp:asc'],
        pagination: {
          pageSize: 1000 // Should be enough for a few days of hourly data
        }
      }) as StrapiEPEXResponse;

      console.log(`Received ${response.data?.length || 0} EPEX price records`);

      if (!response.data || response.data.length === 0) {
        console.warn(`No EPEX price data found for date range ${format(startDate, 'yyyy-MM-dd')} to ${format(endDate, 'yyyy-MM-dd')}`);

        // Try to fetch any available data to see what's in the database
        const anyDataResponse = await client.collection('epexes').find({
          pagination: { pageSize: 5 },
          sort: ['timestamp:desc']
        }) as StrapiEPEXResponse;

        console.log(`Database contains ${anyDataResponse.data?.length || 0} total EPEX records`);
        if (anyDataResponse.data && anyDataResponse.data.length > 0) {
          console.log('Latest EPEX record:', anyDataResponse.data[0]);
        }

        return [];
      }

      return this.convertStrapiDataToEPEXData(response.data);
    } catch (error) {
      console.error('Error fetching EPEX data from Strapi:', error);

      // If it's an axios error, provide more details
      if (error && typeof error === 'object' && 'response' in error) {
        const axiosError = error as any;
        console.error('Response status:', axiosError.response?.status);
        console.error('Response data:', axiosError.response?.data);
        throw new Error(`API Error ${axiosError.response?.status}: ${axiosError.response?.data?.error?.message || axiosError.message}`);
      }

      throw new Error(`Failed to fetch price data: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get price data for a date range
   */
  public async getPriceData(startDate: Date, endDate: Date): Promise<EPEXPriceData[]> {
    // Temporarily always use mock data until Strapi API is fixed
    console.warn('Using mock EPEX data for demonstration (Strapi API temporarily disabled)');

    const result: EPEXPriceData[] = [];
    const currentDate = new Date(startDate);

    while (currentDate <= endDate) {
      const mockData = this.generateMockPriceData(new Date(currentDate));
      result.push(mockData);
      currentDate.setDate(currentDate.getDate() + 1);
    }

    return result;

    /* TODO: Re-enable when Strapi API is working
    // Check cache first
    const cachedData = this.getCachedData(startDate, endDate);
    if (cachedData) {
      return cachedData;
    }

    try {
      const data = await this.fetchFromStrapi(startDate, endDate);

      // Cache the data (cache each day separately)
      if (data.length > 0) {
        for (const dayData of data) {
          const dayDate = new Date(dayData.date);
          this.cacheData(dayDate, dayDate, dayData);
        }
      }

      return data;
    } catch (error) {
      console.error('Error getting price data:', error);
      throw error;
    }
    */
  }

  /**
   * Generate mock price data for demonstration when no real data is available
   */
  private generateMockPriceData(date: Date): EPEXPriceData {
    const prices: EPEXPricePoint[] = [];

    for (let hour = 0; hour < 24; hour++) {
      const timestamp = new Date(date);
      timestamp.setHours(hour, 0, 0, 0);

      // Generate realistic price variations (negative to positive)
      let basePrice = 30 + Math.sin(hour * Math.PI / 12) * 20; // Base pattern in EUR/MWh
      basePrice += (Math.random() - 0.5) * 10; // Add some randomness

      // Occasionally add negative prices (common in renewable energy)
      if (hour >= 12 && hour <= 15 && Math.random() < 0.3) {
        basePrice = -5 - Math.random() * 10;
      }

      prices.push({
        timestamp,
        hour,
        basePrice: Math.round((basePrice / 1000) * 10000) / 10000 // Convert to EUR/kWh and round to 4 decimal places
      });
    }

    return {
      date: format(date, 'yyyy-MM-dd'),
      prices,
      currency: 'EUR',
      unit: 'EUR/kWh',
      source: 'AWATTAR',
      lastUpdated: new Date()
    };
  }

  /**
   * Get today's and tomorrow's prices (default view)
   */
  public async getTodayAndTomorrowPrices(): Promise<EPEXPriceData[]> {
    const today = startOfDay(new Date());
    const tomorrow = addDays(today, 1);

    // Temporarily always use mock data until Strapi API is fixed
    console.warn('Using mock EPEX data for demonstration (Strapi API temporarily disabled)');

    const todayMock = this.generateMockPriceData(today);
    const tomorrowMock = this.generateMockPriceData(tomorrow);

    return [todayMock, tomorrowMock];

    /* TODO: Re-enable when Strapi API is working
    try {
      // Get data for both days in one request
      const data = await this.getPriceData(today, tomorrow);

      if (data.length === 0) {
        console.warn('No EPEX price data available for today/tomorrow, generating mock data for demonstration');

        // Generate mock data for demonstration
        const todayMock = this.generateMockPriceData(today);
        const tomorrowMock = this.generateMockPriceData(tomorrow);

        return [todayMock, tomorrowMock];
      }

      return data;
    } catch (error) {
      console.error('Error getting today/tomorrow prices, falling back to mock data:', error);

      // Fallback to mock data for demonstration
      const todayMock = this.generateMockPriceData(today);
      const tomorrowMock = this.generateMockPriceData(tomorrow);

      return [todayMock, tomorrowMock];
    }
    */
  }

  /**
   * Clear cache
   */
  public clearCache(): void {
    Object.keys(priceCache).forEach(key => delete priceCache[key]);
  }
}

// Export singleton instance
export const epexAPI = EPEXAPIService.getInstance();
