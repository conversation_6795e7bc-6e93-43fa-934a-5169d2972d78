/**
 * EVSEs API Service mit Strapi Client
 *
 * Diese Datei implementiert den Zugriff auf EVSE-Daten (Ladepunkte)
 * unter Verwendung des offiziellen Strapi Clients.
 */

import { getAuthenticatedClient } from './strapi-client';
import type { EvseResponse, EvseData } from './api'; // Importiere die vorhandenen Typen

/**
 * API-Service für EVSEs (Ladepunkte) mit Strapi Client
 */
export const evsesApiStrapi = {
  /**
   * Alle EVSEs abrufen
   * @returns Promise mit allen EVSEs
   */
  getAll: async (): Promise<any> => {
    try {
      const client = getAuthenticatedClient();
      const evses = client.collection('ocpi-evses');
      const response = await evses.find({
        populate: '*'
      });
      return response;
    } catch (error) {
      console.error("Error fetching EVSEs:", error);
      throw error;
    }
  },

  /**
   * Ein EVSE anhand der ID abrufen
   * @param id - ID des EVSEs
   * @returns Promise mit dem EVSE
   */
  getById: async (id: number): Promise<any> => {
    try {
      const client = getAuthenticatedClient();
      const evses = client.collection('ocpi-evses');
      const response = await evses.findOne(id.toString(), {
        populate: '*'
      });
      return response.data;
    } catch (error) {
      console.error(`Error fetching EVSE with id ${id}:`, error);
      throw error;
    }
  },

  /**
   * Ein neues EVSE erstellen
   * @param data - EVSE-Daten
   * @returns Promise mit dem erstellten EVSE
   */
  create: async (data: any): Promise<any> => {
    try {
      const client = getAuthenticatedClient();
      const evses = client.collection('ocpi-evses');
      const response = await evses.create(data);
      return response.data;
    } catch (error) {
      console.error("Error creating EVSE:", error);
      throw error;
    }
  },

  /**
   * Ein EVSE aktualisieren
   * @param id - ID des EVSEs
   * @param data - Aktualisierte EVSE-Daten
   * @returns Promise mit dem aktualisierten EVSE
   */
  update: async (id: number, data: any): Promise<any> => {
    try {
      const client = getAuthenticatedClient();
      const evses = client.collection('ocpi-evses');
      const response = await evses.update(id.toString(), data);
      return response.data;
    } catch (error) {
      console.error(`Error updating EVSE with id ${id}:`, error);
      throw error;
    }
  },

  /**
   * Ein EVSE löschen
   * @param id - ID des EVSEs
   * @returns Promise mit dem gelöschten EVSE
   */
  delete: async (id: number): Promise<any> => {
    try {
      const client = getAuthenticatedClient();
      const evses = client.collection('ocpi-evses');
      await evses.delete(id.toString());
      return { success: true };
    } catch (error) {
      console.error(`Error deleting EVSE with id ${id}:`, error);
      throw error;
    }
  }
};
