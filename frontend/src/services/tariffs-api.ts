/**
 * Tariff API Service
 *
 * This service provides methods to interact with the Strapi tariff API
 * following the existing patterns in the codebase.
 */

import { getAuthenticatedClient } from "~/services/strapi-client";

// Interface for tariff data
export interface TariffData {
  id: string;
  documentId: string;
  name: string;
  validFrom?: string;
  validTo?: string;
  chargerType?: "AC" | "DC";
  deleted?: boolean;
  mandants?: any[];
  terminals?: any[];
  ocpi_evses?: any[];
  ocpi_locations?: any[];
  priceAC?: any[];
  priceDC?: any[];
  blockFeeAC?: any[];
  blockFeeDC?: any[];
  createdAt?: string;
  updatedAt?: string;
}

// Interface for API responses
export interface TariffApiResponse {
  data: TariffData[];
  meta?: {
    pagination?: {
      page: number;
      pageSize: number;
      pageCount: number;
      total: number;
    };
  };
}

export interface SingleTariffApiResponse {
  data: TariffData;
}

// Interface for delete response
export interface DeleteTariffResponse {
  data: TariffData;
  message: string;
}

export const tariffsApi = {
  /**
   * Get all tariffs
   * @param options - Query options
   * @returns Promise with tariffs data
   */
  getAll: async (options?: {
    populate?: string[];
    filters?: any;
    sort?: string[];
    pagination?: {
      page?: number;
      pageSize?: number;
    };
  }): Promise<any> => {
    try {
      const client = getAuthenticatedClient();
      const tariffs = client.collection('tariffs');

      const response = await tariffs.find({
        populate: options?.populate || [
          'mandants',
          'terminals',
          'ocpi_evses',
          'ocpi_locations',
          'priceAC.dailySchedules.hourlyRate',
          'blockFeeAC.blockFeeSchedule',
          'priceDC.dailySchedules.hourlyRate',
          'blockFeeDC.blockFeeSchedule'
        ],
        filters: options?.filters,
        sort: options?.sort,
        pagination: options?.pagination
      });

      return response;
    } catch (error) {
      console.error('Error fetching tariffs:', error);
      throw error;
    }
  },

  /**
   * Get a single tariff by documentId
   * @param documentId - Document ID of the tariff
   * @returns Promise with tariff data
   */
  getByDocumentId: async (documentId: string): Promise<any> => {
    try {
      const client = getAuthenticatedClient();
      const tariffs = client.collection('tariffs');

      const response = await tariffs.find({
        filters: {
          documentId: {
            $eq: documentId
          }
        },
        populate: [
          'mandants',
          'terminals',
          'ocpi_evses',
          'ocpi_locations',
          'priceAC.dailySchedules.hourlyRate',
          'blockFeeAC.blockFeeSchedule',
          'priceDC.dailySchedules.hourlyRate',
          'blockFeeDC.blockFeeSchedule'
        ]
      });

      if (!response.data || response.data.length === 0) {
        throw new Error('Tariff not found');
      }

      return { data: response.data[0] };
    } catch (error) {
      console.error(`Error fetching tariff with documentId ${documentId}:`, error);
      throw error;
    }
  },

  /**
   * Create a new tariff
   * @param data - Tariff data to create
   * @returns Promise with created tariff data
   */
  create: async (data: any): Promise<any> => {
    try {
      const client = getAuthenticatedClient();
      const tariffs = client.collection('tariffs');

      const response = await tariffs.create({
        data
      });

      return response;
    } catch (error) {
      console.error('Error creating tariff:', error);
      throw error;
    }
  },

  /**
   * Update a tariff
   * @param documentId - Document ID of the tariff to update
   * @param data - Updated tariff data
   * @returns Promise with updated tariff data
   */
  update: async (documentId: string, data: any): Promise<any> => {
    try {
      const client = getAuthenticatedClient();
      const tariffs = client.collection('tariffs');

      const response = await tariffs.update(documentId, {
        data
      });

      return response;
    } catch (error) {
      console.error(`Error updating tariff with documentId ${documentId}:`, error);
      throw error;
    }
  },

  /**
   * Soft delete a tariff (sets deleted = true)
   * @param documentId - Document ID of the tariff to delete
   * @returns Promise with delete response
   */
  delete: async (documentId: string): Promise<any> => {
    try {
      const client = getAuthenticatedClient();
      const tariffs = client.collection('tariffs');

      const response = await tariffs.delete(documentId);

      return response;
    } catch (error) {
      console.error(`Error deleting tariff with documentId ${documentId}:`, error);
      throw error;
    }
  }
};
