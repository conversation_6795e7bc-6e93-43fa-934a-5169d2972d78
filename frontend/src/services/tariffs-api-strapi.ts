/**
 * Tariffs API Service mit Strapi Client
 *
 * Diese Datei implementiert den Zugriff auf Tarif-Daten
 * unter Verwendung des offiziellen Strapi Clients.
 */

import { getAuthenticatedClient } from './strapi-client';

// Typdefinitionen für Tarife
export interface HourlyRate {
  id: string;
  hourFrom: number;
  hourTo?: number;
  pricePerKwh: number;
  sessionFee: number;
  priceType: string;
  epex_base?: number;
}

export interface DailySchedule {
  id: string;
  dayOfWeek: string;
  HourlyRate?: HourlyRate[];
}

export interface BlockFeeSchedule {
  id: string;
  dayOfWeek: string;
  startHour: number;
  endHour: number;
  perMinute: number;
  maxFee: number;
  gracePeriod?: number;
}

export interface Tariff {
  id: string;
  attributes: {
    name: string;
    validFrom: string;
    validTo: string;
    chargerType?: "AC" | "DC";
    createdAt: string;
    updatedAt: string;
    publishedAt: string;
    dailySchedules: {
      data: Array<{
        id: string;
        attributes: {
          dayOfWeek: string;
          HourlyRate: {
            data: Array<{
              id: string;
              attributes: {
                hourFrom: number;
                hourTo?: number;
                pricePerKwh: number;
                sessionFee: number;
                priceType: string;
                epex_base?: number;
              }
            }>
          }
        }
      }>
    };
    blockFeeSchedules: {
      data: Array<{
        id: string;
        attributes: {
          dayOfWeek: string;
          startHour: number;
          endHour: number;
          perMinute: number;
          maxFee: number;
          gracePeriod?: number;
        }
      }>
    };
    mandants: {
      data: Array<{
        id: string;
        attributes: {
          name: string;
        }
      }>
    };
    terminals?: {
      data: Array<{
        id: string;
        attributes: {
          Name?: string;
          serialNumber?: string;
          documentId?: string;
        }
      }>
    };
    ocpi_evses?: {
      data: Array<{
        id: string;
        attributes: {
          uid?: string;
          evse_id?: string;
          documentId?: string;
          status?: string;
        }
      }>
    };
    ocpi_locations?: {
      data: Array<{
        id: string;
        attributes: {
          name?: string;
          documentId?: string;
          country_code?: string;
          party_id?: string;
        }
      }>
    };
  }
}

export interface TariffResponse {
  data: Tariff[];
  meta: {
    pagination: {
      page: number;
      pageSize: number;
      pageCount: number;
      total: number;
    }
  }
}

export interface TariffDetailResponse {
  data: Tariff;
  meta: any;
}

/**
 * API-Service für Tarife mit Strapi Client
 */
export const tariffsApiStrapi = {
  /**
   * Alle Tarife abrufen
   * @returns Promise mit allen Tarifen
   */
  getAll: async (): Promise<any> => {
    try {
      const client = getAuthenticatedClient();
      const tariffs = client.collection('tariffs');
      return await tariffs.find({
        populate: [
          'mandants',
          'terminals',
          'ocpi_evses',
          'ocpi_locations',
          'priceAC.dailySchedules.hourlyRate',
          'blockFeeAC.blockFeeSchedule',
          'priceDC.dailySchedules.hourlyRate',
          'blockFeeDC.blockFeeSchedule'
        ]
      });
    } catch (error) {
      console.error("Error fetching tariffs:", error);
      throw error;
    }
  },

  /**
   * Einen Tarif anhand der ID abrufen
   * @param id - ID des Tarifs
   * @returns Promise mit dem Tarif
   */
  getById: async (id: string): Promise<any> => {
    try {
      const client = getAuthenticatedClient();
      const tariffs = client.collection('tariffs');
      return await tariffs.findOne(id, {
        populate: [
          'mandants',
          'terminals',
          'ocpi_evses',
          'ocpi_locations',
          'priceAC.dailySchedules.hourlyRate',
          'blockFeeAC.blockFeeSchedule',
          'priceDC.dailySchedules.hourlyRate',
          'blockFeeDC.blockFeeSchedule'
        ]
      });
    } catch (error) {
      console.error(`Error fetching tariff with id ${id}:`, error);
      throw error;
    }
  },

  /**
   * Einen Tarif anhand der DocumentId abrufen
   * @param documentId - DocumentId des Tarifs
   * @returns Promise mit dem Tarif
   */
  getByDocumentId: async (documentId: string): Promise<any> => {
    try {
      const client = getAuthenticatedClient();
      const tariffs = client.collection('tariffs');
      const response = await tariffs.find({
        filters: {
          documentId: {
            $eq: documentId
          }
        },
        populate: [
          'mandants',
          'terminals',
          'ocpi_evses',
          'ocpi_locations',
          'priceAC',
          'blockFeeAC',
          'priceDC',
          'blockFeeDC'
        ]
      });

      if (response && response.data && response.data.length > 0) {
        return {
          data: response.data[0],
          meta: response.meta
        };
      }

      throw new Error(`No tariff found with documentId ${documentId}`);
    } catch (error) {
      console.error(`Error fetching tariff with documentId ${documentId}:`, error);
      throw error;
    }
  },

  /**
   * Einen neuen Tarif erstellen
   * @param data - Tarif-Daten
   * @returns Promise mit dem erstellten Tarif
   */
  create: async (data: any): Promise<any> => {
    try {
      const client = getAuthenticatedClient();
      const tariffs = client.collection('tariffs');
      return await tariffs.create(data);
    } catch (error) {
      console.error("Error creating tariff:", error);
      throw error;
    }
  },

  /**
   * Einen Tarif aktualisieren
   * @param id - ID des Tarifs
   * @param data - Aktualisierte Tarif-Daten
   * @returns Promise mit dem aktualisierten Tarif
   */
  update: async (id: string, data: any): Promise<any> => {
    try {
      const client = getAuthenticatedClient();
      const tariffs = client.collection('tariffs');
      return await tariffs.update(id, data);
    } catch (error) {
      console.error(`Error updating tariff with id ${id}:`, error);
      throw error;
    }
  },

  /**
   * Einen Tarif löschen
   * @param id - ID des Tarifs
   * @returns Promise mit dem gelöschten Tarif
   */
  delete: async (id: string): Promise<any> => {
    try {
      const client = getAuthenticatedClient();
      const tariffs = client.collection('tariffs');
      await tariffs.delete(id);
      return { success: true };
    } catch (error) {
      console.error(`Error deleting tariff with id ${id}:`, error);
      throw error;
    }
  }
};
