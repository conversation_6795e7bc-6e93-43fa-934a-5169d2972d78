/**
 * Settings API Service mit Strapi Client
 *
 * Diese Datei implementiert den Zugriff auf globale Einstellungen
 * unter Verwendung des offiziellen Strapi Clients für Single-Types.
 */

import { getAuthenticatedClient } from './strapi-client';

// Typdefinitionen für die Einstellungen
export interface GlobalSettings {
  id: number;
  attributes: {
    siteName: string;
    contactEmail: string;
    defaultCurrency: string;
    maintenanceMode: boolean;
    createdAt: string;
    updatedAt: string;
    publishedAt: string;
  };
}

export interface GlobalSettingsResponse {
  data: GlobalSettings;
  meta: any;
}

/**
 * API-Service für globale Einstellungen mit Strapi Client
 */
export const settingsApiStrapi = {
  /**
   * Globale Einstellungen abrufen
   * @returns Promise mit den globalen Einstellungen
   */
  getSettings: async (): Promise<any> => {
    try {
      const client = getAuthenticatedClient();
      const settings = client.single('global-setting');
      return await settings.find();
    } catch (error) {
      console.error("Error fetching global settings:", error);
      throw error;
    }
  },

  /**
   * Globale Einstellungen aktualisieren
   * @param data - Aktualisierte Einstellungsdaten
   * @returns Promise mit den aktualisierten Einstellungen
   */
  updateSettings: async (data: any): Promise<any> => {
    try {
      const client = getAuthenticatedClient();
      const settings = client.single('global-setting');
      return await settings.update(data);
    } catch (error) {
      console.error("Error updating global settings:", error);
      throw error;
    }
  }
};
