/**
 * Strapi Client Konfiguration
 *
 * Diese Datei stellt zentrale Funktionen für den Zugriff auf die Strapi API bereit.
 * Sie verwendet den offiziellen @strapi/client für eine vereinfachte API-Interaktion.
 */

import { strapi } from '@strapi/client';

/**
 * Erstellt einen Strapi-Client mit optionalem Auth-Token
 *
 * @param token - Optional: JWT-Token für authentifizierte Anfragen
 * @returns Konfigurierter Strapi-Client
 */
export const createStrapiClient = (token?: string) => {
  return strapi({
    baseURL: `${process.env.NEXT_PUBLIC_STRAPI_API_URL}/api` || "http://localhost:1337/api",
    auth: token || undefined,
  });
};

/**
 * Gibt einen authentifizierten Client zurück
 * Wenn ein Token übergeben wird, wird dieser verwendet
 * Andernfalls wird das Token aus localStorage geholt (nur client-seitig)
 *
 * @param token - Optional: JWT-Token für authentifizierte Anfragen
 * @returns Authentifizierter Strapi-Client
 */
export const getAuthenticatedClient = (token?: string) => {
  if (token) {
    return createStrapiClient(token);
  }
  const localToken = typeof window !== 'undefined' ? localStorage.getItem("strapiToken") : null;
  return createStrapiClient(localToken || undefined);
};

/**
 * Gibt einen authentifizierten Client mit einem bestimmten Token zurück
 *
 * @param token - JWT-Token für authentifizierte Anfragen
 * @returns Authentifizierter Strapi-Client
 */
export const getClientWithToken = (token: string) => {
  return createStrapiClient(token);
};

/**
 * Standard-Client für nicht-authentifizierte Anfragen
 */
export const publicClient = createStrapiClient();
