"use client";

import { Card, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "~/components/ui/card";
import { Skeleton } from "~/components/ui/skeleton";

interface TerminalStatusData {
	ACTIVE: number;
	MAINTENANCE: number;
	OUT_OF_ORDER: number;
	IDLE: number;
}

interface TerminalStatusOverviewProps {
	data: TerminalStatusData;
	loading?: boolean;
	error?: string;
}

const statusConfig = {
	ACTIVE: {
		label: "Aktiv",
		color: "bg-green-500",
		textColor: "text-green-700",
		bgColor: "bg-green-50",
	},
	MAINTENANCE: {
		label: "Wartung",
		color: "bg-yellow-500",
		textColor: "text-yellow-700",
		bgColor: "bg-yellow-50",
	},
	OUT_OF_ORDER: {
		label: "Außer Betrieb",
		color: "bg-red-500",
		textColor: "text-red-700",
		bgColor: "bg-red-50",
	},
	IDLE: {
		label: "Bereit",
		color: "bg-blue-500",
		textColor: "text-blue-700",
		bgColor: "bg-blue-50",
	},
};

export function TerminalStatusOverview({ data, loading = false, error }: TerminalStatusOverviewProps) {
	if (loading) {
		return (
			<Card>
				<CardHeader>
					<CardTitle>
						<Skeleton className="h-5 w-40" />
					</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="space-y-3">
						{Array.from({ length: 4 }).map((_, i) => (
							<div key={i} className="flex items-center justify-between">
								<Skeleton className="h-4 w-20" />
								<Skeleton className="h-4 w-8" />
							</div>
						))}
					</div>
				</CardContent>
			</Card>
		);
	}

	if (error) {
		return (
			<Card className="border-red-200">
				<CardHeader>
					<CardTitle className="text-red-600">Terminal Status</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="text-red-500 text-sm">Fehler beim Laden der Daten</div>
				</CardContent>
			</Card>
		);
	}

	const total = Object.values(data).reduce((sum, count) => sum + count, 0);

	return (
		<Card className="hover:shadow-lg transition-shadow duration-200">
			<CardHeader>
				<CardTitle className="text-gray-900 flex items-center">
					<span className="mr-2">🔌</span>
					Terminal Status
				</CardTitle>
			</CardHeader>
			<CardContent>
				<div className="space-y-4">
					{Object.entries(statusConfig).map(([status, config]) => {
						const count = data[status as keyof TerminalStatusData];
						const percentage = total > 0 ? (count / total) * 100 : 0;

						return (
							<div key={status} className="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors">
								<div className="flex items-center space-x-3">
									<div className={`w-4 h-4 rounded-full ${config.color} shadow-sm`} />
									<span className="text-gray-700 font-medium">{config.label}</span>
								</div>
								<div className="flex items-center space-x-3">
									<div className="text-right">
										<div className="font-bold text-gray-900 text-lg">{count}</div>
										<div className="text-gray-500 text-xs">
											{percentage.toFixed(0)}%
										</div>
									</div>
									<div className="w-16 h-2 bg-gray-200 rounded-full overflow-hidden">
										<div
											className={`h-full ${config.color} transition-all duration-500`}
											style={{ width: `${percentage}%` }}
										/>
									</div>
								</div>
							</div>
						);
					})}
				</div>
				{total === 0 && (
					<div className="text-center text-gray-500 py-8">
						<div className="text-4xl mb-2">🔌</div>
						<div>Keine Terminals verfügbar</div>
					</div>
				)}
			</CardContent>
		</Card>
	);
}
