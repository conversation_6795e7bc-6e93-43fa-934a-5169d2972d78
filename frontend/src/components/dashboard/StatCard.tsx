"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "~/components/ui/card";
import { Skeleton } from "~/components/ui/skeleton";

interface StatCardProps {
	title: string;
	value: string | number;
	subtitle?: string;
	icon?: React.ReactNode;
	trend?: {
		value: number;
		isPositive: boolean;
	};
	loading?: boolean;
	error?: string;
	className?: string;
}

export function StatCard({
	title,
	value,
	subtitle,
	icon,
	trend,
	loading = false,
	error,
	className = "",
}: StatCardProps) {
	if (loading) {
		return (
			<Card className={`${className}`}>
				<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
					<CardTitle className="font-medium text-sm">
						<Skeleton className="h-4 w-24" />
					</CardTitle>
					{icon && <Skeleton className="h-4 w-4" />}
				</CardHeader>
				<CardContent>
					<Skeleton className="mb-1 h-8 w-20" />
					<Skeleton className="h-3 w-16" />
				</CardContent>
			</Card>
		);
	}

	if (error) {
		return (
			<Card className={`border-red-200 ${className}`}>
				<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
					<CardTitle className="font-medium text-sm text-red-600">{title}</CardTitle>
					{icon && <div className="text-red-400">{icon}</div>}
				</CardHeader>
				<CardContent>
					<div className="text-red-500 text-sm">Fehler beim Laden</div>
				</CardContent>
			</Card>
		);
	}

	return (
		<Card className={`hover:shadow-lg transition-shadow duration-200 ${className}`}>
			<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
				<CardTitle className="font-medium text-gray-600 text-sm">{title}</CardTitle>
				{icon && <div className="text-2xl">{icon}</div>}
			</CardHeader>
			<CardContent>
				<div className="font-bold text-3xl text-gray-900 mb-1">{value}</div>
				{subtitle && (
					<p className="text-gray-600 text-sm mb-2">{subtitle}</p>
				)}
				{trend && (
					<div className="flex items-center text-sm">
						<span
							className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
								trend.isPositive
									? "bg-green-100 text-green-800"
									: "bg-red-100 text-red-800"
							}`}
						>
							{trend.isPositive ? "↗" : "↘"} {Math.abs(trend.value)}%
						</span>
						<span className="ml-2 text-gray-500 text-xs">vs. Vorperiode</span>
					</div>
				)}
			</CardContent>
		</Card>
	);
}
