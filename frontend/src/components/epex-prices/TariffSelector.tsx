/**
 * Tariff Selector Component for EPEX Price Page
 * Allows users to select tariffs to compare with EPEX prices
 */

"use client";

import React, { useState } from 'react';
import { Button } from '~/components/ui/button';
import type { TariffOption } from '~/types/epex.types';

interface TariffSelectorProps {
  availableTariffs: TariffOption[];
  selectedTariffs: string[];
  onSelectionChange: (tariffIds: string[]) => void;
  loading?: boolean;
}

export function TariffSelector({
  availableTariffs,
  selectedTariffs,
  onSelectionChange,
  loading = false
}: TariffSelectorProps) {
  const [isOpen, setIsOpen] = useState(false);

  const handleTariffToggle = (tariffId: string) => {
    const isSelected = selectedTariffs.includes(tariffId);
    let newSelection: string[];

    if (isSelected) {
      newSelection = selectedTariffs.filter(id => id !== tariffId);
    } else {
      // Limit to maximum 3 tariffs for chart readability
      if (selectedTariffs.length >= 3) {
        return;
      }
      newSelection = [...selectedTariffs, tariffId];
    }

    onSelectionChange(newSelection);
  };

  const clearSelection = () => {
    onSelectionChange([]);
  };

  const selectedTariffNames = availableTariffs
    .filter(tariff => selectedTariffs.includes(tariff.documentId))
    .map(tariff => tariff.name);

  if (loading) {
    return (
      <div>
        <h3 className="text-sm font-semibold text-gray-900 mb-2">
          🏷️ Tarif-Vergleich
        </h3>
        <div className="flex items-center justify-center py-4">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
          <span className="ml-2 text-gray-600 text-sm">Lade Tarife...</span>
        </div>
      </div>
    );
  }

  return (
    <div>
      <h3 className="text-base font-semibold text-gray-900 mb-3">
        🏷️ Tarif-Vergleich
      </h3>

      {/* No tariffs available message */}
      {availableTariffs.length === 0 && !loading && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
          <div className="flex items-center">
            <div className="text-blue-500 text-lg mr-3">ℹ️</div>
            <div>
              <h4 className="text-sm font-semibold text-blue-800">Keine Tarife verfügbar</h4>
              <p className="text-sm text-blue-700 mt-1">
                Für den aktuellen Mandanten sind keine Tarife konfiguriert.
                Erstellen Sie zunächst Tarife im Tarif-Management, um diese hier zu vergleichen.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Selection Summary and Controls - Horizontal Layout */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-4">
        {/* Left side: Selected tariffs display */}
        <div className="flex-1">
          <div className="flex items-center gap-3 mb-2">
            <label className="text-sm font-medium text-gray-700">
              Ausgewählt ({selectedTariffs.length}/3):
            </label>
            {selectedTariffs.length > 0 && (
              <Button
                variant="outline"
                size="sm"
                onClick={clearSelection}
                className="text-xs h-7 px-3"
              >
                Alle entfernen
              </Button>
            )}
          </div>

          {selectedTariffs.length === 0 ? (
            <p className="text-sm text-gray-500 italic">
              Keine Tarife ausgewählt
            </p>
          ) : (
            <div className="flex flex-wrap gap-2">
              {selectedTariffNames.map((name, index) => (
                <span
                  key={index}
                  className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800"
                >
                  {name}
                </span>
              ))}
            </div>
          )}
        </div>

        {/* Right side: Tariff selection dropdown */}
        <div className="lg:w-80">
          <div className="relative">
            <button
              onClick={() => setIsOpen(!isOpen)}
              className="w-full flex items-center justify-between px-3 py-2 border border-gray-300 rounded-md bg-white text-sm text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
              disabled={availableTariffs.length === 0}
            >
              <span>
                {availableTariffs.length === 0
                  ? 'Keine Tarife verfügbar'
                  : 'Tarif auswählen...'
                }
              </span>
              <svg
                className={`h-4 w-4 transition-transform ${isOpen ? 'rotate-180' : ''}`}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </button>

        {isOpen && availableTariffs.length > 0 && (
          <div className="absolute z-10 mt-1 w-full bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
            <div className="py-1">
              {availableTariffs.map((tariff) => {
                const isSelected = selectedTariffs.includes(tariff.documentId);
                const isDisabled = !isSelected && selectedTariffs.length >= 3;

                return (
                  <button
                    key={tariff.documentId}
                    onClick={() => !isDisabled && handleTariffToggle(tariff.documentId)}
                    className={`w-full text-left px-3 py-2 text-sm hover:bg-gray-100 flex items-center justify-between ${
                      isSelected ? 'bg-blue-50 text-blue-700' : 'text-gray-700'
                    } ${isDisabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
                    disabled={isDisabled}
                  >
                    <div className="flex items-center">
                      <div className={`w-4 h-4 mr-2 border rounded ${
                        isSelected ? 'bg-blue-600 border-blue-600' : 'border-gray-300'
                      }`}>
                        {isSelected && (
                          <svg className="w-3 h-3 text-white ml-0.5" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                        )}
                      </div>
                      <span className="font-medium">{tariff.name}</span>
                    </div>
                    <div className="flex items-center space-x-1 text-xs">
                      {tariff.hasAC && (
                        <span className="px-1 py-0.5 bg-green-100 text-green-700 rounded">AC</span>
                      )}
                      {tariff.hasDC && (
                        <span className="px-1 py-0.5 bg-purple-100 text-purple-700 rounded">DC</span>
                      )}
                    </div>
                  </button>
                );
              })}
            </div>
          </div>
            )}
          </div>
        </div>
      </div>

      {/* Info Text - Compact */}
      <div className="mt-3 p-3 bg-blue-50 rounded text-sm text-blue-700">
        <strong>💡</strong> Bis zu 3 Tarife auswählbar. AC/DC-Preise werden als zusätzliche Linien angezeigt.
      </div>
    </div>
  );
}
