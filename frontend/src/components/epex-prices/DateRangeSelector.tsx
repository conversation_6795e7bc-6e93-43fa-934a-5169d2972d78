/**
 * Date Range Selector Component for EPEX Price Page
 * Allows users to select custom date ranges for viewing price data
 */

"use client";

import React from 'react';
import { format, addDays, subDays, startOfDay } from 'date-fns';
import { Button } from '~/components/ui/button';
import { Input } from '~/components/ui/input';
import type { DateRangeSelectorProps } from '~/types/epex.types';

export function DateRangeSelector({
  startDate,
  endDate,
  onDateRangeChange,
  maxDate = addDays(new Date(), 1), // Default: tomorrow
  minDate = subDays(new Date(), 30) // Default: 30 days ago
}: DateRangeSelectorProps) {

  // Quick selection buttons
  const quickSelections = [
    {
      label: 'Heute',
      onClick: () => {
        const today = startOfDay(new Date());
        onDateRangeChange(today, today);
      }
    },
    {
      label: 'Heute + Morgen',
      onClick: () => {
        const today = startOfDay(new Date());
        const tomorrow = addDays(today, 1);
        onDateRangeChange(today, tomorrow);
      }
    },
    {
      label: 'Diese Woche',
      onClick: () => {
        const today = startOfDay(new Date());
        const weekEnd = addDays(today, 6);
        onDateRangeChange(today, weekEnd);
      }
    },
    {
      label: 'Letzten 7 Tage',
      onClick: () => {
        const today = startOfDay(new Date());
        const weekAgo = subDays(today, 6);
        onDateRangeChange(weekAgo, today);
      }
    }
  ];

  const handleStartDateChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newStartDate = new Date(event.target.value);
    if (newStartDate <= endDate && newStartDate >= minDate && newStartDate <= maxDate) {
      onDateRangeChange(newStartDate, endDate);
    }
  };

  const handleEndDateChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newEndDate = new Date(event.target.value);
    if (newEndDate >= startDate && newEndDate >= minDate && newEndDate <= maxDate) {
      onDateRangeChange(startDate, newEndDate);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow p-4">
      <h3 className="text-base font-semibold text-gray-900 mb-3">
        📅 Zeitraum auswählen
      </h3>

      {/* Quick Selection Buttons */}
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Schnellauswahl:
        </label>
        <div className="flex flex-wrap gap-2">
          {quickSelections.map((selection, index) => (
            <Button
              key={index}
              variant="outline"
              size="sm"
              onClick={selection.onClick}
              className="text-xs"
            >
              {selection.label}
            </Button>
          ))}
        </div>
      </div>

      {/* Custom Date Range */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
        <div>
          <label htmlFor="start-date" className="block text-sm font-medium text-gray-700 mb-1">
            Von:
          </label>
          <Input
            id="start-date"
            type="date"
            value={format(startDate, 'yyyy-MM-dd')}
            onChange={handleStartDateChange}
            min={format(minDate, 'yyyy-MM-dd')}
            max={format(maxDate, 'yyyy-MM-dd')}
            className="w-full"
          />
        </div>

        <div>
          <label htmlFor="end-date" className="block text-sm font-medium text-gray-700 mb-1">
            Bis:
          </label>
          <Input
            id="end-date"
            type="date"
            value={format(endDate, 'yyyy-MM-dd')}
            onChange={handleEndDateChange}
            min={format(startDate, 'yyyy-MM-dd')}
            max={format(maxDate, 'yyyy-MM-dd')}
            className="w-full"
          />
        </div>
      </div>

      {/* Date Range Info */}
      <div className="mt-3 p-2 bg-blue-50 rounded-md">
        <p className="text-xs text-blue-800">
          <strong>Zeitraum:</strong> {format(startDate, 'dd.MM.yyyy')} bis {format(endDate, 'dd.MM.yyyy')}
          {startDate.getTime() === endDate.getTime() && (
            <span className="ml-1 text-blue-600">(Ein Tag)</span>
          )}
        </p>
        {endDate > maxDate && (
          <p className="text-xs text-amber-600 mt-1">
            ⚠️ Preise für zukünftige Tage sind möglicherweise noch nicht verfügbar.
          </p>
        )}
      </div>
    </div>
  );
}
