// frontend/src/app/[mandant]/terminal/[terminalId]/page.tsx
"use client";

import Image from "next/image";
import Link from "next/link";
import { useParams, useRouter } from "next/navigation";
import React, { useState, useEffect } from "react";
import { publicApiService } from "~/services/public-api";
import { getEVSEDisplayIdentifier } from "~/utils/evseDisplayUtils";

// Typdefinitionen
type OCPIStatus =
	| "ACTIVE"
	| "AVAILABLE"
	| "BLOCKED"
	| "CHARGING"
	| "INOPERATIVE"
	| "OUTOFORDER"
	| "PLANNED"
	| "REMOVED"
	| "RESERVED"
	| "UNKNOWN";

interface Coordinates {
	id: number;
	latitude: string;
	longitude: string;
}

interface Logo {
	id: number;
	documentId: string;
	url: string;
	width: number;
	height: number;
}

interface Location {
	id: number;
	name: string;
	address: string;
	city: string;
	postalCode: string;
	country: string;
	coordinates: Coordinates;
}

interface OCPISession {
	id: number;
	startTime: string;
	endTime: string | null;
	kwh: number;
	authorizationReference: string;
	currency: string;
	totalCost: number;
	ocpiStatus: string;
}

interface PaymentSession {
	documentId: string;
	state: string;
	authorizedAt: string;
	blockedAmount: number;
	ocpi_session: OCPISession | null;
}

interface EVSE {
	id: number;
	documentId: string;
	uid: string;
	evseId: string;
	physicalReference?: string;
	status: string;
	labelForTerminal: string;
	coordinates: Coordinates;
	location: Location | null;
	currentPaymentSession?: PaymentSession | null;
}

interface Mandant {
	id: number;
	name: string;
	logo: Logo;
}

interface ChargingPoint {
	documentId: string;
	evseId: string;
	physicalReference?: string;
	uid: string;
	ocpiStatus: OCPIStatus;
	connectorId: string;
	labelForTerminal?: string;
	currentPaymentSession?: PaymentSession | null;
}

interface Terminal {
	id: number;
	documentId: string;
	createdAt: string;
	updatedAt: string;
	publishedAt: string;
	locale: string | null;
	serialNumber: string;
	terminalName: string;
	online: boolean;
	state: string;
	lastUpdate: string;
	currentLanguage: string;
	mandant: Mandant | null;
	evses: EVSE[];
	location: Location | null;
}

export default function TerminalChargingPointsPage() {
	const params = useParams();
	const router = useRouter();
	const terminalId = params.terminalId as string;

	const [terminal, setTerminal] = useState<Terminal | null>(null);
	const [chargingPoints, setChargingPoints] = useState<ChargingPoint[]>([]);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

	useEffect(() => {
		const fetchTerminalData = async () => {
			try {
				const data = await publicApiService.getTerminalQrData(terminalId);
				console.log("Terminal data:", data);

				setTerminal(data);
				setLastUpdated(new Date());

				// Setze die EVSEs als ChargingPoints
				if (data.evses && data.evses.length > 0) {
					// Konvertiere die EVSE-Daten in das ChargingPoint-Format
					const chargingPointsData = data.evses.map((evse: any) => ({
						documentId: evse.documentId,
						evseId: evse.evseId,
						physicalReference: evse.physicalReference,
						uid: evse.uid,
						ocpiStatus: evse.ocpiStatus as OCPIStatus,
						connectorId: "1", // Standard-Connector-ID, falls nicht vorhanden
						labelForTerminal: evse.labelForTerminal,
						// Aktuelle Payment-Session hinzufügen, falls vorhanden
						currentPaymentSession: evse.currentPaymentSession,
					}));

					setChargingPoints(chargingPointsData);
				} else {
					setChargingPoints([]);
				}

				setError(null);
			} catch (err) {
				console.error("Error fetching terminal data:", err);
				setError(
					"Fehler beim Laden der Terminal-Daten. Bitte versuchen Sie es später erneut.",
				);
			} finally {
				setLoading(false);
			}
		};

		if (terminalId) {
			fetchTerminalData();
		}

		// Polling für EVSE-Status-Updates alle 10 Sekunden
		const intervalId = setInterval(() => {
			if (terminalId) {
				fetchTerminalData();
			}
		}, 10000);

		return () => clearInterval(intervalId);
	}, [terminalId]);

	// Funktion zum Bestimmen des Status-Badges
	const getStatusBadge = (status: OCPIStatus) => {
		switch (status) {
			case "ACTIVE":
				return (
					<span className="inline-flex items-center rounded-full bg-blue-100 px-2.5 py-0.5 font-medium text-blue-800 text-xs">
						Aktiv
					</span>
				);
			case "AVAILABLE":
				return (
					<span className="inline-flex items-center rounded-full bg-green-100 px-2.5 py-0.5 font-medium text-green-800 text-xs">
						Verfügbar
					</span>
				);
			case "BLOCKED":
				return (
					<span className="inline-flex items-center rounded-full bg-yellow-100 px-2.5 py-0.5 font-medium text-xs text-yellow-800">
						Blockiert
					</span>
				);
			case "CHARGING":
				return (
					<span className="inline-flex items-center rounded-full bg-blue-100 px-2.5 py-0.5 font-medium text-blue-800 text-xs">
						Lädt
					</span>
				);
			case "INOPERATIVE":
				return (
					<span className="inline-flex items-center rounded-full bg-red-100 px-2.5 py-0.5 font-medium text-red-800 text-xs">
						Außer Betrieb
					</span>
				);
			case "OUTOFORDER":
				return (
					<span className="inline-flex items-center rounded-full bg-red-100 px-2.5 py-0.5 font-medium text-red-800 text-xs">
						Defekt
					</span>
				);
			case "PLANNED":
				return (
					<span className="inline-flex items-center rounded-full bg-gray-100 px-2.5 py-0.5 font-medium text-gray-800 text-xs">
						Geplant
					</span>
				);
			case "REMOVED":
				return (
					<span className="inline-flex items-center rounded-full bg-gray-100 px-2.5 py-0.5 font-medium text-gray-800 text-xs">
						Entfernt
					</span>
				);
			case "RESERVED":
				return (
					<span className="inline-flex items-center rounded-full bg-purple-100 px-2.5 py-0.5 font-medium text-purple-800 text-xs">
						Reserviert
					</span>
				);
			default:
				return (
					<span className="inline-flex items-center rounded-full bg-gray-100 px-2.5 py-0.5 font-medium text-gray-800 text-xs">
						Unbekannt
					</span>
				);
		}
	};

	if (loading) {
		return (
			<div className="flex min-h-screen items-center justify-center">
				<div className="h-12 w-12 animate-spin rounded-full border-blue-500 border-t-2 border-b-2" />
			</div>
		);
	}

	if (error) {
		return (
			<div className="m-4 border-red-500 border-l-4 bg-red-50 p-4">
				<div className="flex">
					<div className="flex-shrink-0">
						<svg
							className="h-5 w-5 text-red-500"
							viewBox="0 0 20 20"
							fill="currentColor"
						>
							<path
								fillRule="evenodd"
								d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
								clipRule="evenodd"
							/>
						</svg>
					</div>
					<div className="ml-3">
						<p className="text-red-700 text-sm">{error}</p>
					</div>
				</div>
			</div>
		);
	}

	return (
		<div className="container mx-auto px-4 py-8">
			{/* Header */}
			<div className="mb-8 flex items-center justify-between">
				<h1 className="font-bold text-2xl text-gray-900">
					Standort - {terminal?.location?.name}
				</h1>
				<div className="flex items-center space-x-4">
					{lastUpdated && (
						<span className="text-gray-500 text-sm">
							Aktualisiert: {lastUpdated.toLocaleTimeString("de-DE")}
						</span>
					)}
					<button
						onClick={() => {
							setLoading(true);
							const fetchData = async () => {
								try {
									const data =
										await publicApiService.getTerminalQrData(terminalId);
									setTerminal(data);
									setLastUpdated(new Date());

									// Setze die EVSEs als ChargingPoints
									if (data.evses && data.evses.length > 0) {
										// Konvertiere die EVSE-Daten in das ChargingPoint-Format
										const chargingPointsData = data.evses.map((evse: any) => ({
											documentId: evse.documentId,
											evseId: evse.evseId,
											physicalReference: evse.physicalReference,
											uid: evse.uid,
											ocpiStatus: evse.ocpiStatus as OCPIStatus,
											connectorId: "1", // Standard-Connector-ID, falls nicht vorhanden
											labelForTerminal: evse.labelForTerminal,
											// Aktuelle Payment-Session hinzufügen, falls vorhanden
											currentPaymentSession: evse.currentPaymentSession,
										}));

										setChargingPoints(chargingPointsData);
									} else {
										setChargingPoints([]);
									}
								} catch (error) {
									console.error("Error refreshing data:", error);
									setError(
										"Fehler beim Aktualisieren der Daten. Bitte versuchen Sie es später erneut.",
									);
								} finally {
									setLoading(false);
								}
							};
							fetchData();
						}}
						className="inline-flex items-center rounded-md border border-transparent bg-blue-600 px-4 py-2 font-medium text-sm text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
						disabled={loading}
					>
						{loading ? (
							<>
								<svg
									className="-ml-1 mr-2 h-4 w-4 animate-spin text-white"
									xmlns="http://www.w3.org/2000/svg"
									fill="none"
									viewBox="0 0 24 24"
								>
									<circle
										className="opacity-25"
										cx="12"
										cy="12"
										r="10"
										stroke="currentColor"
										strokeWidth="4"
									/>
									<path
										className="opacity-75"
										fill="currentColor"
										d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
									/>
								</svg>
								Aktualisiere...
							</>
						) : (
							<>Aktualisieren</>
						)}
					</button>
				</div>
			</div>

			{/* Terminal-Informationen */}
			<div className="mb-8 overflow-hidden bg-white shadow sm:rounded-lg">
				<div className="flex items-center justify-between px-4 py-5 sm:px-6">
					<div>
						<h3 className="font-medium text-gray-900 text-lg leading-6">
							Terminal-Details
						</h3>
						<p className="mt-1 max-w-2xl text-gray-500 text-sm">
							Informationen zum Bezahlterminal
						</p>
					</div>
					<div className="flex items-center space-x-3">
						{terminal?.online ? (
							<span className="inline-flex items-center rounded-full bg-green-100 px-3 py-1 font-medium text-green-800 text-sm">
								<span className="mr-1 h-2 w-2 rounded-full bg-green-500" />
								Online
							</span>
						) : (
							<span className="inline-flex items-center rounded-full bg-red-100 px-3 py-1 font-medium text-red-800 text-sm">
								<span className="mr-1 h-2 w-2 rounded-full bg-red-500" />
								Offline
							</span>
						)}
						{terminal?.mandant?.logo && (
							<div className="relative h-10 w-20">
								<Image
									src={`${process.env.NEXT_PUBLIC_API_URL}${terminal.mandant.logo.url}`}
									alt={terminal.mandant.name}
									width={terminal.mandant.logo.width}
									height={terminal.mandant.logo.height}
								/>
							</div>
						)}
					</div>
				</div>
				<div className="border-gray-200 border-t">
					<dl>
						<div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
							<dt className="font-medium text-gray-500 text-sm">
								Terminal-Name
							</dt>
							<dd className="mt-1 text-gray-900 text-sm sm:col-span-2 sm:mt-0">
								{terminal?.terminalName}
							</dd>
						</div>
						<div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
							<dt className="font-medium text-gray-500 text-sm">
								Seriennummer
							</dt>
							<dd className="mt-1 text-gray-900 text-sm sm:col-span-2 sm:mt-0">
								{terminal?.serialNumber}
							</dd>
						</div>
						<div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
							<dt className="font-medium text-gray-500 text-sm">Status</dt>
							<dd className="mt-1 text-gray-900 text-sm sm:col-span-2 sm:mt-0">
								{terminal?.state}
							</dd>
						</div>
						<div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
							<dt className="font-medium text-gray-500 text-sm">
								Letzte Aktualisierung
							</dt>
							<dd className="mt-1 text-gray-900 text-sm sm:col-span-2 sm:mt-0">
								{terminal?.lastUpdate
									? new Date(terminal.lastUpdate).toLocaleString("de-DE")
									: "Unbekannt"}
							</dd>
						</div>
						<div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
							<dt className="font-medium text-gray-500 text-sm">
								Aktuelle Sprache
							</dt>
							<dd className="mt-1 text-gray-900 text-sm sm:col-span-2 sm:mt-0">
								{terminal?.currentLanguage
									? terminal.currentLanguage.toUpperCase()
									: "DE"}
							</dd>
						</div>
						{terminal?.location && (
							<>
								<div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
									<dt className="font-medium text-gray-500 text-sm">
										Standort
									</dt>
									<dd className="mt-1 text-gray-900 text-sm sm:col-span-2 sm:mt-0">
										{terminal.location.name}
									</dd>
								</div>
								<div className="bg-white px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
									<dt className="font-medium text-gray-500 text-sm">Adresse</dt>
									<dd className="mt-1 text-gray-900 text-sm sm:col-span-2 sm:mt-0">
										{terminal.location.address}, {terminal.location.postalCode}{" "}
										{terminal.location.city}
									</dd>
								</div>
								<div className="bg-gray-50 px-4 py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
									<dt className="font-medium text-gray-500 text-sm">
										Koordinaten
									</dt>
									<dd className="mt-1 text-gray-900 text-sm sm:col-span-2 sm:mt-0">
										{terminal.location.coordinates.latitude},{" "}
										{terminal.location.coordinates.longitude}
										<a
											href={`https://www.google.com/maps?q=${terminal.location.coordinates.latitude},${terminal.location.coordinates.longitude}`}
											target="_blank"
											rel="noopener noreferrer"
											className="ml-2 text-blue-600 hover:text-blue-800"
										>
											In Google Maps öffnen
										</a>
									</dd>
								</div>
							</>
						)}
					</dl>
				</div>
			</div>

			{/* Ladepunkte-Liste */}
			<div className="mb-8 overflow-hidden bg-white shadow sm:rounded-lg">
				<div className="px-4 py-5 sm:px-6">
					<h2 className="font-semibold text-gray-900 text-xl">
						Verbundene Ladepunkte
					</h2>
					<p className="mt-1 text-gray-500 text-sm">
						Übersicht aller mit diesem Terminal verbundenen Ladepunkte und deren
						Status
					</p>
				</div>

				<div className="border-gray-200 border-t">
					{chargingPoints.length === 0 ? (
						<div className="px-6 py-4 text-center text-gray-500">
							Keine Ladepunkte mit diesem Terminal verbunden
						</div>
					) : (
						<div className="overflow-hidden">
							<table className="min-w-full divide-y divide-gray-200">
								<thead className="bg-gray-50">
									<tr>
										<th
											scope="col"
											className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider"
										>
											Ladepunkt
										</th>
										<th
											scope="col"
											className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider"
										>
											EVSE ID
										</th>
										<th
											scope="col"
											className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider"
										>
											Status
										</th>
										<th
											scope="col"
											className="px-6 py-3 text-right font-medium text-gray-500 text-xs uppercase tracking-wider"
										>
											Aktionen
										</th>
									</tr>
								</thead>
								<tbody className="divide-y divide-gray-200 bg-white">
									{chargingPoints.map((point) => (
										<tr key={point.documentId}>
											<td className="whitespace-nowrap px-6 py-4">
												<div className="font-medium text-gray-900 text-sm">
													{getEVSEDisplayIdentifier({
														physicalReference: point.physicalReference,
														evseId: point.evseId,
														uid: point.uid
													})}
												</div>
												<div className="text-gray-500 text-sm">
													Connector: {point.connectorId}
												</div>
											</td>
											<td className="whitespace-nowrap px-6 py-4">
												<div className="text-gray-900 text-sm">
													{point.labelForTerminal || "-"}
												</div>
											</td>
											<td className="whitespace-nowrap px-6 py-4">
												{getStatusBadge(point.ocpiStatus)}
											</td>
											<td className="whitespace-nowrap px-6 py-4 text-right font-medium text-sm">
												{point.currentPaymentSession &&
													(point.ocpiStatus === "CHARGING" ||
														point.currentPaymentSession.ocpi_session
															?.ocpiStatus === "ACTIVE") && (
														<Link
															href={`/Eulektro/session/${point.currentPaymentSession.documentId}`}
															className="inline-flex items-center rounded-md border border-transparent bg-blue-600 px-4 py-2 font-medium text-sm text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
														>
															Details
														</Link>
													)}
											</td>
										</tr>
									))}
								</tbody>
							</table>
						</div>
					)}
				</div>
			</div>

			{/* Zurück-Button */}
			<div className="mt-6 flex justify-start">
				<button
					onClick={() => router.back()}
					className="inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 font-medium text-gray-700 text-sm shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
				>
					Zurück
				</button>
			</div>
		</div>
	);
}
