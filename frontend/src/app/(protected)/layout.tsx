"use client";

import Image from "next/image";
import Link from "next/link";
import { usePathname } from "next/navigation";
import type React from "react";
import { useState } from "react";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { useAuth } from "~/components/AuthContext";
import { MandantProvider } from "~/components/MandantContext";
import MandantSelector from "~/components/MandantSelector";

// Layout-Komponente für den geschützten Bereich
export default function ProtectedLayout({
	children,
}: {
	children: React.ReactNode;
}) {
	const { login, user, loading, error: authError } = useAuth();
	const pathname = usePathname();
	const [isSidebarOpen, setIsSidebarOpen] = useState(true);

	// Hauptmenü-Items
	const menuItems = [
		{ name: "Dashboard", href: "/dashboard", icon: "📊" },
		{ name: "Terminals", href: "/terminals", icon: "🖥️" },
		{ name: "Standorte", href: "/standorte", icon: "📍" },
		{ name: "Tarife", href: "/tarife", icon: "💰" },
		{ name: "Umsätze", href: "/umsaetze", icon: "📈" },
		{ name: "Sessions", href: "/sessions", icon: "⚡" },
		{ name: "Nutzer", href: "/users", icon: "👥" },
		{ name: "Logs", href: "/logs", icon: "📝" },
		{ name: "Einstellungen", href: "/einstellungen", icon: "⚙️" },
	];

	// Admin-Menü-Items
	const adminMenuItems = [
		{ name: "Payment Connections", href: "/payment-connections", icon: "💳" },
		{ name: "OCPI Connection", href: "/anbindung", icon: "🔗" },
	];

	// Funktion zum Prüfen, ob der Link aktiv ist
	const isActive = (href: string) => {
		const basePathname = pathname.split("/").slice(0, 3).join("/");
		const baseHref = href.split("/").slice(0, 3).join("/");
		return basePathname === baseHref;
	};

	if (!user?.username) {
		return null;
	}

	return (
		<MandantProvider>
			<div className="flex h-screen bg-gray-100">
				{/* Sidebar / Linkes Menü */}
				<div
					className={`bg-gradient-to-b from-[rgb(23,85,104)] via-[rgb(33,105,124)] to-[rgb(43,135,154)] text-white ${
						isSidebarOpen ? "w-64" : "w-20"
					} flex-shrink-0 overflow-y-auto shadow-lg transition-all duration-300`}
				>
					{/* Admin-Info und Toggles */}
					<div className="border-[rgba(255,255,255,0.1)] border-b p-4">
						<div className="flex items-center justify-between">
							{isSidebarOpen ? (
								<div className="flex items-center space-x-2">
									<span className="font-medium text-white/90">
										{user?.username?.charAt(0).toUpperCase() +
											user?.username?.slice(1)}
									</span>
								</div>
							) : (
								<div className="text-center">
									<span className="font-medium text-white/90">
										{user?.username?.[0]?.toUpperCase()}
									</span>
								</div>
							)}
							<button
								onClick={() => setIsSidebarOpen(!isSidebarOpen)}
								className="rounded p-1 text-white hover:bg-[rgba(255,255,255,0.1)]"
							>
								{isSidebarOpen ? "←" : "→"}
							</button>
						</div>
						{isSidebarOpen && (
							<div className="mt-2 text-sm text-white/70">
								Payment Terminal Hub
							</div>
						)}
						<MandantSelector />
					</div>

					{/* Hauptmenü */}
					<nav className="p-4">
						<div className="space-y-1">
							{menuItems.map((item) => (
								<Link
									key={item.name}
									href={item.href}
									className={`flex items-center rounded-lg p-2 transition-colors ${
										isActive(item.href)
											? "bg-white/20 text-white"
											: "text-white/80 hover:bg-white/10 hover:text-white"
									} ${isSidebarOpen ? "" : "justify-center"}`}
								>
									<span className="mr-3 text-xl">{item.icon}</span>
									{isSidebarOpen && <span>{item.name}</span>}
								</Link>
							))}
						</div>

						{/* Admin-Menü-Bereich */}
						{adminMenuItems.length > 0 && (
							<div className="my-4 border-[rgba(255,255,255,0.1)] border-t pt-4">
								{isSidebarOpen && (
									<h3 className="px-2 font-semibold text-white/50 text-xs uppercase tracking-wider">
										Administration
									</h3>
								)}
								<div className="mt-3 space-y-1">
									{adminMenuItems.map((item) => (
										<Link
											key={item.name}
											href={item.href}
											className={`flex items-center rounded-lg p-2 transition-colors ${
												isActive(item.href)
													? "bg-white/20 text-white"
													: "text-white/80 hover:bg-white/10 hover:text-white"
											} ${isSidebarOpen ? "" : "justify-center"}`}
										>
											<span className="mr-3 text-xl">{item.icon}</span>
											{isSidebarOpen && <span>{item.name}</span>}
										</Link>
									))}
								</div>
							</div>
						)}
					</nav>

					{/* Footer */}
					<div className="mt-auto border-[rgba(255,255,255,0.1)] border-t p-4 text-white/60 text-xs">
						{isSidebarOpen ? (
							<>
								<Link
									href="/logout"
									className="flex items-center rounded-lg p-2 text-white/80 hover:bg-white/10 hover:text-white"
								>
									<span className="mr-3 text-xl">🚪</span>
									<span>Abmelden</span>
								</Link>
								<div className="mt-4 text-center">
									© {new Date().getFullYear()} Eulektro
								</div>
							</>
						) : (
							<Link
								href="/logout"
								className="flex justify-center rounded-lg p-2 text-white/80 hover:bg-white/10 hover:text-white"
							>
								<span className="text-xl">🚪</span>
							</Link>
						)}
					</div>
				</div>

				{/* Hauptinhalt */}
				<div className="flex flex-1 flex-col overflow-hidden">
					{/* Header */}
					<header className="bg-white shadow">
						<div className="flex items-center justify-between px-6 py-4">
							<h1 className="font-bold text-[rgb(33,105,124)] text-xl">
								{menuItems.find((item) => isActive(item.href))?.name ||
									"Dashboard"}
							</h1>
							<div className="flex items-center space-x-4">
								{/* Logo in der Header-Leiste rechts */}
								<Link href="/dashboard">
									<Image
										src="/logo/Eulektro-Logo.png"
										alt="Eulektro Logo"
										width={120}
										height={34}
										className="object-contain"
										priority
									/>
								</Link>
							</div>
						</div>
					</header>

					{/* Hauptinhalt */}
					<main className="flex-1 overflow-y-auto bg-[rgba(236,236,231,0.3)] p-6">
						{children}
						<ToastContainer position="bottom-right" />
					</main>
				</div>
			</div>
		</MandantProvider>
	);
}
