"use client";

import Link from "next/link";
import { useAuth } from "~/components/AuthContext";

export default function DashboardPage() {
	const { user } = useAuth();

	return (
		<div>
			<div className="mb-6 rounded-lg bg-white p-6 shadow">
				<h1 className="mb-4 font-bold text-2xl text-gray-900">Dashboard</h1>
				<p className="mb-2 text-gray-600">
					Willkommen {user?.username || "Benutzer"} im
					EulektroTerminalVerwaltung System
				</p>
				<p className="text-gray-600">
					Hier finden Sie eine Übersicht über Ihre Ladestationen und können alle
					wichtigen Funktionen der Anwendung direkt erreichen.
				</p>
			</div>

			<div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
				<NavigationCard
					title="Standorte"
					description="Standorte anzeigen und verwalten"
					href="/standorte"
					icon="🏢"
				/>
				<NavigationCard
					title="Terminals"
					description="Ladestationen verwalten"
					href="/terminals"
					icon="🔌"
				/>
				<NavigationCard
					title="Terminal Simulator"
					description="Terminal-Interaktionen simulieren und testen"
					href="/terminal-simulator"
					icon="🎮"
				/>
				<NavigationCard
					title="Tarife"
					description="Tarife konfigurieren"
					href="/tarife"
					icon="💰"
				/>
				<NavigationCard
					title="Umsätze"
					description="Finanzielle Übersicht"
					href="/umsaetze"
					icon="📊"
				/>
				<NavigationCard
					title="Ladesessions"
					description="Aktuelle und vergangene Ladevorgänge"
					href="/sessions"
					icon="⚡"
				/>
				<NavigationCard
					title="Einstellungen"
					description="Systemeinstellungen anpassen"
					href="/einstellungen"
					icon="⚙️"
				/>
			</div>
		</div>
	);
}

function NavigationCard({
	title,
	description,
	href,
	icon,
}: {
	title: string;
	description: string;
	href: string;
	icon: string;
}) {
	return (
		<Link href={href} className="block">
			<div className="rounded-lg bg-white p-5 shadow-lg transition-colors hover:border-blue-300 hover:bg-blue-50">
				<div className="mb-2 flex items-center">
					<span className="mr-2 text-2xl">{icon}</span>
					<h3 className="font-medium text-lg">{title}</h3>
				</div>
				<p className="text-gray-600 text-sm">{description}</p>
			</div>
		</Link>
	);
}
