"use client";

import { useEffect, useState } from "react";
import { FiPower } from "react-icons/fi";
import { toast } from "react-toastify";
import { useMandant } from "~/components/MandantContext";
import ApiKeyModal, {
	type ApiKeyFormData,
} from "~/components/modals/ApiKeyModal";
import TerminalEditModal, {
	type TerminalEditFormData,
} from "~/components/modals/TerminalEditModal";
import {
	type LocationData,
	type TerminalData,
	evsesApi,
	locationsApi,
	payterApi,
	payterConnectionsApi,
	terminalsApi,
} from "~/services/api";
import { type PayterTerminal, payterApiService } from "~/services/payter-api";

// Typ für einen API-Key
interface ApiKey {
	id: string;
	key: string;
	environment: "test" | "prod";
	description: string;
	active: boolean;
}

// Kombinierter Terminal-Typ für die Anzeige
interface DisplayTerminal extends TerminalData {
	locationName?: string;
	evseCount?: number;
}

export default function TerminalsPage() {
	// Mandanten-Kontext für Zugriffskontrolle und automatische Verknüpfung
	const { activeMandant, getAllowedMandantIds } = useMandant();

	const [strapiTerminals, setStrapiTerminals] = useState<TerminalData[]>([]);
	const [displayTerminals, setDisplayTerminals] = useState<DisplayTerminal[]>(
		[],
	);
	const [payterTerminals, setPayterTerminals] = useState<PayterTerminal[]>([]);
	const [locations, setLocations] = useState<LocationData[]>([]);
	const [apiKeys, setApiKeys] = useState<ApiKey[]>([]);

	const [isLoading, setIsLoading] = useState(true);
	const [syncStatus, setSyncStatus] = useState<
		"idle" | "syncing" | "success" | "error"
	>("idle");
	const [syncMessage, setSyncMessage] = useState("");
	const [activeTab, setActiveTab] = useState<"terminals" | "apikeys">(
		"terminals",
	);

	// Zustand für Modals
	const [terminalEditModalOpen, setTerminalEditModalOpen] = useState(false);
	const [apiKeyModalOpen, setApiKeyModalOpen] = useState(false);
	const [isEditing, setIsEditing] = useState(false);
	const [currentTerminal, setCurrentTerminal] = useState<TerminalData | null>(
		null,
	);
	const [currentApiKey, setCurrentApiKey] = useState<
		ApiKeyFormData | undefined
	>(undefined);

	// Zustand für Lösch-Bestätigung
	const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
	const [itemToDelete, setItemToDelete] = useState<{
		id: string;
		type: "terminal" | "apikey";
	} | null>(null);

	// Zusätzlicher State für die ausgewählte Umgebung
	const [selectedEnvironment, setSelectedEnvironment] = useState<
		"test" | "prod"
	>("test");

	// Funktion zum Laden der Daten
	const loadData = async (env: "test" | "prod") => {
		try {
			setIsLoading(true);

			// Mandanten-ID für die Filterung verwenden
			const mandantId = activeMandant?.id;

			// Parallele Anfragen für alle benötigten Daten
			const [strapiTerminalsResponse, locationsResponse, payterResponse] =
				await Promise.all([
					// Suche alle Terminals, die eine payter_connection mit dem Typ "env" haben und zum aktuellen Mandanten gehören
					terminalsApi.getAll(env, mandantId),
					locationsApi.getAll(),
					// Payter-Verbindungen für den aktiven Mandanten und die gewählte Umgebung laden
					payterConnectionsApi.getAll({
						"filters[type][$eq]": env.charAt(0).toUpperCase() + env.slice(1),
						"filters[mandants][id][$eq]": mandantId,
					}),
				]);

			// Terminals von der Payter API laden mit aktivem Mandanten
			const fetchedPayterTerminals = await payterApiService.getTerminals(
				env,
				mandantId,
			);
			setPayterTerminals(fetchedPayterTerminals);

			// Strapi-Terminals und Locations speichern
			setStrapiTerminals(strapiTerminalsResponse.data);
			setLocations(locationsResponse.data);

			// Kombinierte Terminals für die Anzeige erstellen
			const enhanced = strapiTerminalsResponse.data.map((terminal) => {
				// Finde die Location, die dieses Terminal enthält
				const location = locationsResponse.data.find((loc) =>
					loc.terminals?.some((t) => t.id === terminal.id),
				);

				return {
					...terminal,
					locationName: location?.name || "Nicht zugeordnet",
					evseCount: terminal.evses?.length || 0,
				};
			});

			setDisplayTerminals(enhanced);

			// API-Keys formatieren und speichern
			if (payterResponse?.data) {
				const formattedApiKeys = payterResponse.data.map((connection) => ({
					id: connection.id.toString(),
					key: connection.apiKey,
					environment: connection.type.toLowerCase() as "test" | "prod",
					description: connection.name,
					active: true, // Annahme, dass alle verfügbaren Schlüssel aktiv sind
				}));
				setApiKeys(formattedApiKeys);
			}
		} catch (error) {
			console.error("Fehler beim Laden der Daten:", error);
		} finally {
			setIsLoading(false);
		}
	};

	// Daten bei Änderung der ausgewählten Umgebung oder des aktiven Mandanten laden
	useEffect(() => {
		if (activeMandant) {
			loadData(selectedEnvironment);
		}
	}, [selectedEnvironment, activeMandant]);

	// Funktion zum Synchronisieren der Terminals zwischen Payter und Strapi
	const syncTerminals = async () => {
		setSyncStatus("syncing");
		setSyncMessage("Synchronisiere Terminals...");

		try {
			let createdCount = 0;
			let updatedCount = 0;

			// Für jeden Payter-Terminal prüfen, ob er bereits in Strapi existiert
			for (const payterTerminal of payterTerminals) {
				try {
					const strapiTerminal = await terminalsApi.findBySerialNumber(
						payterTerminal.serialNumber,
					);

					if (strapiTerminal) {
						// Terminal existiert bereits, aktualisieren
						// Finde die passende Payter-Connection für die aktuelle Umgebung
						const payterConnection = apiKeys.find(
							(key) => key.environment === selectedEnvironment,
						);

						await terminalsApi.update(strapiTerminal.documentId, {
							data: {
								terminalName: payterTerminal.terminalName,
								online: payterTerminal.online,
								state: payterTerminal.state,
								lastUpdate: new Date().toISOString(),
								// Payter-Connection setzen, wenn verfügbar
								payter_connection: payterConnection
									? {
											connect: [Number.parseInt(payterConnection.id)],
										}
									: undefined,
							},
						});
						updatedCount++;
					} else {
						// Terminal existiert noch nicht, neu anlegen mit Mandanten-Verknüpfung
						// Finde die passende Payter-Connection für die aktuelle Umgebung
						const payterConnection = apiKeys.find(
							(key) => key.environment === selectedEnvironment,
						);

						const createData: any = {
							serialNumber: payterTerminal.serialNumber,
							terminalName: payterTerminal.terminalName,
							online: payterTerminal.online,
							state: payterTerminal.state,
							lastUpdate: new Date().toISOString(),
						};

						// Wenn ein aktiver Mandant vorhanden ist, verknüpfen wir das Terminal damit
						if (activeMandant) {
							createData.mandant = activeMandant.id;
						}

						// Payter-Connection setzen, wenn verfügbar
						if (payterConnection) {
							createData.payter_connection = Number.parseInt(
								payterConnection.id,
							);
						}

						await terminalsApi.create(createData);
						createdCount++;
					}
				} catch (terminalError) {
					console.error(
						`Fehler bei Terminal ${payterTerminal.serialNumber}:`,
						terminalError,
					);
				}
			}

			// Lade Daten neu, um die aktualisierten Terminals anzuzeigen
			await loadData(selectedEnvironment);

			setSyncStatus("success");
			setSyncMessage(
				`Synchronisierung abgeschlossen: ${createdCount} Terminals erstellt, ${updatedCount} Terminals aktualisiert.`,
			);

			// Nach 5 Sekunden den Statustext zurücksetzen
			setTimeout(() => {
				setSyncStatus("idle");
				setSyncMessage("");
			}, 5000);
		} catch (error) {
			console.error("Fehler bei der Synchronisierung:", error);
			setSyncStatus("error");
			setSyncMessage(
				"Fehler bei der Synchronisierung der Terminals. Bitte überprüfen Sie die API-Verbindung.",
			);

			// Nach 5 Sekunden den Fehlertext zurücksetzen
			setTimeout(() => {
				setSyncStatus("idle");
				setSyncMessage("");
			}, 5000);
		}
	};

	// Funktion zum Bearbeiten eines Terminals
	const handleEditTerminal = (terminal: TerminalData) => {
		setCurrentTerminal(terminal);
		setTerminalEditModalOpen(true);
	};

	// Funktion zum Speichern der Terminal-Änderungen
	const handleSaveTerminal = async (data: TerminalEditFormData) => {
		try {
			// Terminal-Daten aktualisieren
			const updateData = {
				data: {
					terminalName: data.terminalName,
					serialNumber: data.serialNumber,
				},
			};

			// Wenn ein Standort ausgewählt wurde, füge ihn dem Terminal hinzu
			if (data.locationDocumentId) {
				// In Strapi v5 muss für Relationen connect verwendet werden
				(updateData.data as any).ocpi_location = {
					connect: [data.locationDocumentId],
				};
			} else {
				// Wenn kein Standort ausgewählt wurde, Verbindung trennen
				(updateData.data as any).ocpi_location = {
					disconnect: true,
				};
			}

			// Terminal mit allen Änderungen aktualisieren
			await terminalsApi.update(data.documentId, updateData);

			// EVSEs zuordnen, wenn ausgewählt
			if (data.evseDocumentIds && data.evseDocumentIds.length > 0) {
				// Für jede EVSE die Terminal-Zuordnung aktualisieren
				for (const evseDocumentId of data.evseDocumentIds) {
					await evsesApi.update(evseDocumentId, {
						data: {
							terminal: {
								connect: [data.documentId],
							},
						},
					});
				}
			}

			// EVSEs entfernen, die nicht mehr ausgewählt sind
			const terminal = strapiTerminals.find(
				(t) => t.documentId === data.documentId,
			);
			if (terminal?.evses) {
				const currentEvseDocumentIds = terminal.evses.map(
					(evse) => evse.documentId,
				);
				const evseDocumentIdsToRemove = currentEvseDocumentIds.filter(
					(evseDocumentId) => !data.evseDocumentIds?.includes(evseDocumentId),
				);

				for (const evseDocumentId of evseDocumentIdsToRemove) {
					await evsesApi.update(evseDocumentId, {
						data: {
							terminal: {
								disconnect: true,
							},
						},
					});
				}
			}

			// Lade Daten neu, um die Änderungen anzuzeigen
			await loadData(selectedEnvironment);
		} catch (error) {
			console.error("Fehler beim Speichern des Terminals:", error);
			throw error;
		}
	};

	// CRUD-Funktionen für API-Keys
	const handleAddApiKey = () => {
		setCurrentApiKey(undefined);
		setIsEditing(false);
		setApiKeyModalOpen(true);
	};

	const handleEditApiKey = (apiKey: ApiKey) => {
		setCurrentApiKey({
			id: apiKey.id,
			name: apiKey.description,
			apiKey: apiKey.key,
			apiUrl: "", // Hier könnten wir die URL aus der Datenbank laden
			environment: apiKey.environment,
		});
		setIsEditing(true);
		setApiKeyModalOpen(true);
	};

	const handleDeleteApiKey = (id: string) => {
		setItemToDelete({ id, type: "apikey" });
		setDeleteConfirmOpen(true);
	};

	const handleSaveApiKey = async (data: ApiKeyFormData) => {
		try {
			if (isEditing && data.id) {
				// API Key aktualisieren
				const updatedApiKey = await payterConnectionsApi.update(
					Number.parseInt(data.id),
					{
						Name: data.name,
						ApiKey: data.apiKey,
						ApiUrl: data.apiUrl,
						Type:
							data.environment.charAt(0).toUpperCase() +
							data.environment.slice(1),
					},
				);

				// API Keys-Liste aktualisieren
				setApiKeys(
					apiKeys.map((k) =>
						k.id === data.id
							? {
									...k,
									description: data.name,
									key: data.apiKey,
									environment: data.environment,
								}
							: k,
					),
				);
			} else {
				// Neuen API Key erstellen
				const newApiKey = await payterConnectionsApi.create({
					name: data.name,
					apiKey: data.apiKey,
					apiUrl: data.apiUrl,
					type:
						data.environment.charAt(0).toUpperCase() +
						data.environment.slice(1),
				});

				// Neuen API Key zur Liste hinzufügen
				if (newApiKey) {
					setApiKeys([
						...apiKeys,
						{
							id: newApiKey.id.toString(),
							description: data.name,
							key: data.apiKey,
							environment: data.environment,
							active: true,
						},
					]);
				}
			}

			// Modal schließen
			setApiKeyModalOpen(false);
		} catch (error) {
			console.error("Fehler beim Speichern des API-Schlüssels:", error);
			alert("Fehler beim Speichern des API-Schlüssels.");
		}
	};

	// Funktion zum Initialisieren eines Terminals
	const handleInitTerminal = async (terminalId: string) => {
		try {
			// Zeige Ladeindikator oder Benachrichtigung
			setIsLoading(true);

			// Rufe den Payter-Init-Endpunkt auf
			const response = await payterApi.initTerminal(terminalId);

			// Zeige Erfolgsmeldung als Toast
			toast.success(`Terminal ${terminalId} erfolgreich initialisiert!`);

			// Lade Daten neu, um die neuesten Einträge anzuzeigen
			await loadData(selectedEnvironment);
		} catch (error: unknown) {
			console.error(
				`Fehler beim Initialisieren des Terminals ${terminalId}:`,
				error,
			);

			const errorMessage =
				error instanceof Error ? error.message : "Unbekannter Fehler";

			toast.error(
				`Fehler beim Initialisieren des Terminals ${terminalId}: ${errorMessage}`,
			);
		} finally {
			// Setze Ladeindikator zurück
			setIsLoading(false);
		}
	};

	// Funktion zum Außer-Betrieb-Setzen eines Terminals
	const handleOutOfOrderTerminal = async (terminalId: string) => {
		try {
			// Zeige Ladeindikator oder Benachrichtigung
			setIsLoading(true);

			// Rufe den Payter-Out-of-Order-Endpunkt auf
			const response = await payterApi.setOutOfOrder(terminalId);

			// Zeige Erfolgsmeldung als Toast
			toast.success(
				`Terminal ${terminalId} erfolgreich außer Betrieb gesetzt!`,
			);

			// Lade Daten neu, um die neuesten Einträge anzuzeigen
			await loadData(selectedEnvironment);
		} catch (error: unknown) {
			console.error(
				`Fehler beim Außer-Betrieb-Setzen des Terminals ${terminalId}:`,
				error,
			);

			const errorMessage =
				error instanceof Error ? error.message : "Unbekannter Fehler";

			toast.error(
				`Fehler beim Außer-Betrieb-Setzen des Terminals ${terminalId}: ${errorMessage}`,
			);
		} finally {
			// Setze Ladeindikator zurück
			setIsLoading(false);
		}
	};

	// Formatierungsfunktion für den Status
	const renderStatus = (status: string, online: boolean) => {
		let statusClass = "bg-gray-100 text-gray-800";
		if (online) {
			if (status === "IDLE") {
				statusClass = "bg-green-100 text-green-800";
			} else if (status === "BUSY") {
				statusClass = "bg-blue-100 text-blue-800";
			} else if (status === "ERROR") {
				statusClass = "bg-red-100 text-red-800";
			}
		} else {
			statusClass = "bg-red-100 text-red-800";
			status = "Offline";
		}

		return (
			<span
				className={`inline-flex rounded-full px-2 font-semibold text-xs leading-5 ${statusClass}`}
			>
				{status}
			</span>
		);
	};

	return (
		<div>
			<div className="mb-6 rounded-lg bg-white p-6 shadow">
				<h1 className="mb-4 font-bold text-2xl text-gray-900">Terminale</h1>
				<p className="mb-4 text-gray-600">
					Hier können Sie alle Ladestationen verwalten und API-Schlüssel für
					Payter konfigurieren.
				</p>

				{/* Tabs */}
				<div className="mb-6 border-gray-200 border-b">
					<nav className="-mb-px flex space-x-8">
						<button
							onClick={() => setActiveTab("terminals")}
							className={`border-b-2 px-1 py-4 font-medium text-sm ${
								activeTab === "terminals"
									? "border-blue-500 text-blue-600"
									: "border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700"
							}`}
						>
							Terminale
						</button>
						<button
							onClick={() => setActiveTab("apikeys")}
							className={`border-b-2 px-1 py-4 font-medium text-sm ${
								activeTab === "apikeys"
									? "border-blue-500 text-blue-600"
									: "border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700"
							}`}
						>
							API-Schlüssel
						</button>
					</nav>
				</div>
			</div>

			{/* Terminals Tab */}
			{activeTab === "terminals" && (
				<div className="rounded-lg bg-white p-6 shadow">
					<div className="mb-6 flex items-center justify-between">
						<div className="flex items-center">
							<h2 className="mr-4 font-semibold text-xl">Terminalliste</h2>
							<div className="ml-4 flex items-center">
								<label
									htmlFor="environment"
									className="mr-2 block font-medium text-gray-700 text-sm"
								>
									Umgebung:
								</label>
								<select
									id="environment"
									value={selectedEnvironment}
									onChange={(e) =>
										setSelectedEnvironment(e.target.value as "test" | "prod")
									}
									className="mt-1 block rounded-md border border-gray-300 py-1 pr-10 pl-3 text-base focus:border-blue-500 focus:outline-none focus:ring-blue-500 sm:text-sm"
								>
									<option value="test">Test</option>
									<option value="prod">Produktion</option>
								</select>
							</div>
						</div>
						<button
							onClick={syncTerminals}
							disabled={syncStatus === "syncing" || isLoading}
							className="rounded-md bg-blue-600 px-4 py-2 font-medium text-sm text-white hover:bg-blue-700 disabled:opacity-50"
						>
							{syncStatus === "syncing"
								? "Synchronisiere..."
								: "Mit Payter synchronisieren"}
						</button>
					</div>

					{/* Synchronisierungsstatus */}
					{syncStatus !== "idle" && (
						<div
							className={`mb-4 rounded p-3 ${
								syncStatus === "success"
									? "bg-green-100 text-green-700"
									: syncStatus === "error"
										? "bg-red-100 text-red-700"
										: "bg-blue-100 text-blue-700"
							}`}
						>
							{syncMessage}
						</div>
					)}

					{isLoading ? (
						<div className="flex justify-center py-8">
							<div className="text-gray-600">Lade Daten...</div>
						</div>
					) : (
						<div className="overflow-x-auto">
							<table className="min-w-full divide-y divide-gray-200">
								<thead className="bg-gray-50">
									<tr>
										<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
											Name
										</th>
										<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
											Seriennummer
										</th>
										<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
											Standort
										</th>
										<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
											EVSEs
										</th>
										<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
											Status
										</th>
										<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
											Letzte Aktualisierung
										</th>
										<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
											Aktionen
										</th>
									</tr>
								</thead>
								<tbody className="divide-y divide-gray-200 bg-white">
									{displayTerminals.length === 0 ? (
										<tr>
											<td
												colSpan={7}
												className="px-6 py-4 text-center text-gray-500"
											>
												Keine Terminals gefunden. Klicken Sie auf "Mit Payter
												synchronisieren", um die Terminals zu laden.
											</td>
										</tr>
									) : (
										displayTerminals.map((terminal) => (
											<tr key={terminal.id}>
												<td className="whitespace-nowrap px-6 py-4">
													<div className="font-medium text-gray-900">
														{terminal.terminalName}
													</div>
												</td>
												<td className="whitespace-nowrap px-6 py-4 text-gray-500 text-sm">
													{terminal.serialNumber}
												</td>
												<td className="whitespace-nowrap px-6 py-4 text-gray-500 text-sm">
													{terminal.locationName}
												</td>
												<td className="whitespace-nowrap px-6 py-4 text-gray-500 text-sm">
													{terminal.evseCount}
												</td>
												<td className="whitespace-nowrap px-6 py-4">
													{renderStatus(terminal.state, terminal.online)}
												</td>
												<td className="whitespace-nowrap px-6 py-4 text-gray-500 text-sm">
													{terminal.lastUpdate
														? new Date(terminal.lastUpdate).toLocaleString()
														: "-"}
												</td>
												<td className="whitespace-nowrap px-6 py-4 font-medium text-sm">
													<div className="flex items-center space-x-3">
														<button
															onClick={() => handleEditTerminal(terminal)}
															className="cursor-pointer text-blue-600 hover:text-blue-900"
														>
															Bearbeiten
														</button>
														<button
															onClick={() =>
																handleInitTerminal(terminal.serialNumber)
															}
															className="flex cursor-pointer items-center text-blue-600 hover:text-blue-900"
															title="Terminal initialisieren"
														>
															<FiPower className="mr-1" size={14} />
															Init
														</button>
														<button
															onClick={() =>
																handleOutOfOrderTerminal(terminal.serialNumber)
															}
															className="flex cursor-pointer items-center text-red-600 hover:text-red-900"
															title="Terminal außer Betrieb setzen"
														>
															<FiPower className="mr-1" size={14} />
															Außer Betrieb
														</button>
													</div>
												</td>
											</tr>
										))
									)}
								</tbody>
							</table>
						</div>
					)}
				</div>
			)}

			{/* API Keys Tab */}
			{activeTab === "apikeys" && (
				<div className="rounded-lg bg-white p-6 shadow">
					<div className="mb-6 flex items-center justify-between">
						<h2 className="font-semibold text-xl">Payter API-Schlüssel</h2>
						<button
							onClick={handleAddApiKey}
							className="rounded-md bg-blue-600 px-4 py-2 font-medium text-sm text-white hover:bg-blue-700"
						>
							+ API-Schlüssel hinzufügen
						</button>
					</div>

					{isLoading ? (
						<div className="flex justify-center py-8">
							<div className="text-gray-600">Lade Daten...</div>
						</div>
					) : (
						<div className="overflow-x-auto">
							<table className="min-w-full divide-y divide-gray-200">
								<thead className="bg-gray-50">
									<tr>
										<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
											Beschreibung
										</th>
										<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
											Umgebung
										</th>
										<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
											API-Schlüssel
										</th>
										<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
											Status
										</th>
										<th className="px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider">
											Aktionen
										</th>
									</tr>
								</thead>
								<tbody className="divide-y divide-gray-200 bg-white">
									{apiKeys.length === 0 ? (
										<tr>
											<td
												colSpan={5}
												className="px-6 py-4 text-center text-gray-500"
											>
												Keine API-Schlüssel gefunden.
											</td>
										</tr>
									) : (
										apiKeys.map((apiKey) => (
											<tr key={apiKey.id}>
												<td className="whitespace-nowrap px-6 py-4">
													<div className="font-medium text-gray-900">
														{apiKey.description}
													</div>
												</td>
												<td className="whitespace-nowrap px-6 py-4">
													<span
														className={`inline-flex rounded-full px-2 font-semibold text-xs leading-5 ${
															apiKey.environment === "prod"
																? "bg-green-100 text-green-800"
																: apiKey.environment === "test"
																	? "bg-yellow-100 text-yellow-800"
																	: "bg-blue-100 text-blue-800"
														}`}
													>
														{apiKey.environment}
													</span>
												</td>
												<td className="whitespace-nowrap px-6 py-4 text-gray-500 text-sm">
													<div className="flex items-center">
														<span className="rounded bg-gray-100 p-1 font-mono">
															{apiKey.key}
														</span>
														<button
															className="ml-2 text-gray-500 hover:text-gray-700"
															onClick={() =>
																navigator.clipboard.writeText(apiKey.key)
															}
															title="In Zwischenablage kopieren"
														>
															📋
														</button>
													</div>
												</td>
												<td className="whitespace-nowrap px-6 py-4">
													<span
														className={`inline-flex rounded-full px-2 font-semibold text-xs leading-5 ${
															apiKey.active
																? "bg-green-100 text-green-800"
																: "bg-red-100 text-red-800"
														}`}
													>
														{apiKey.active ? "Aktiv" : "Inaktiv"}
													</span>
												</td>
												<td className="whitespace-nowrap px-6 py-4 font-medium text-sm">
													<button
														onClick={() => handleEditApiKey(apiKey)}
														className="mr-3 text-blue-600 hover:text-blue-900"
													>
														Bearbeiten
													</button>
													<button
														onClick={() => handleDeleteApiKey(apiKey.id)}
														className="text-red-600 hover:text-red-900"
													>
														Löschen
													</button>
												</td>
											</tr>
										))
									)}
								</tbody>
							</table>
						</div>
					)}
				</div>
			)}

			{/* Modals */}
			<TerminalEditModal
				isOpen={terminalEditModalOpen}
				onClose={() => setTerminalEditModalOpen(false)}
				onSave={handleSaveTerminal}
				terminal={currentTerminal}
			/>

			<ApiKeyModal
				isOpen={apiKeyModalOpen}
				onClose={() => setApiKeyModalOpen(false)}
				onSave={handleSaveApiKey}
				initialData={currentApiKey}
				isEditing={isEditing}
			/>

			{/* Löschbestätigung */}
			{deleteConfirmOpen && (
				<div className="fixed inset-0 z-50 overflow-y-auto">
					<div className="flex min-h-screen items-center justify-center px-4 pt-4 pb-20 text-center sm:block sm:p-0">
						<div
							className="fixed inset-0 transition-opacity"
							aria-hidden="true"
						>
							<div className="absolute inset-0 bg-gray-500 opacity-75" />
						</div>

						<span
							className="hidden sm:inline-block sm:h-screen sm:align-middle"
							aria-hidden="true"
						>
							&#8203;
						</span>

						<div className="inline-block transform overflow-hidden rounded-lg bg-white px-4 pt-5 pb-4 text-left align-bottom shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6 sm:align-middle">
							<div className="sm:flex sm:items-start">
								<div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
									<h3 className="font-medium text-gray-900 text-lg leading-6">
										Element löschen
									</h3>
									<div className="mt-2">
										<p className="text-gray-500 text-sm">
											Sind Sie sicher, dass Sie dieses Element löschen möchten?
											Diese Aktion kann nicht rückgängig gemacht werden.
										</p>
									</div>
								</div>
							</div>
							<div className="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
								<button
									type="button"
									onClick={async () => {
										if (itemToDelete) {
											try {
												if (itemToDelete.type === "apikey") {
													await payterConnectionsApi.delete(
														Number.parseInt(itemToDelete.id),
													);
													// Update API keys list after deletion
													setApiKeys(
														apiKeys.filter((key) => key.id !== itemToDelete.id),
													);
												}
												// Handle other delete types here if needed in the future
											} catch (error) {
												console.error("Fehler beim Löschen:", error);
												alert("Fehler beim Löschen des Elements.");
											}
										}
										setDeleteConfirmOpen(false);
										setItemToDelete(null);
									}}
									className="inline-flex w-full justify-center rounded-md border border-transparent bg-red-600 px-4 py-2 font-medium text-base text-white shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 sm:ml-3 sm:w-auto sm:text-sm"
								>
									Löschen
								</button>
								<button
									type="button"
									onClick={() => {
										setDeleteConfirmOpen(false);
										setItemToDelete(null);
									}}
									className="mt-3 inline-flex w-full justify-center rounded-md border border-gray-300 bg-white px-4 py-2 font-medium text-base text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 sm:mt-0 sm:w-auto sm:text-sm"
								>
									Abbrechen
								</button>
							</div>
						</div>
					</div>
				</div>
			)}
		</div>
	);
}
