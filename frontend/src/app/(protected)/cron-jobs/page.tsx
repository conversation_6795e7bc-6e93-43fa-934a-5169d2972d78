"use client";

import React, { useState, useEffect } from "react";
import { useAuth } from "~/components/AuthContext";
import { Button } from "~/components/ui/button";
import { Spinner } from "~/components/ui/spinner";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "~/components/ui/tabs";
import CronJobTable from "~/components/cron-jobs/CronJobTable";
import CronJobExecutionHistory from "~/components/cron-jobs/CronJobExecutionHistory";
import { cronJobsApi, type CronJobWithStatus, type CronJobExecution } from "~/services/cron-jobs-api";

export default function CronJobsPage() {
  const { user } = useAuth();
  const [jobsWithStatus, setJobsWithStatus] = useState<CronJobWithStatus[]>([]);
  const [allExecutions, setAllExecutions] = useState<CronJobExecution[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshKey, setRefreshKey] = useState(0);

  // Check if user has admin role
  const isAdmin = user?.role?.type === 'administrator';

  useEffect(() => {
    if (!isAdmin) {
      setError('Access denied. Administrator role required.');
      setIsLoading(false);
      return;
    }

    fetchData();
  }, [isAdmin, refreshKey]);

  const fetchData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const [jobsData, executionsData] = await Promise.all([
        cronJobsApi.getAllJobsWithStatus(),
        cronJobsApi.getAllExecutions(100),
      ]);

      setJobsWithStatus(jobsData);
      setAllExecutions(executionsData);
    } catch (error) {
      console.error('Error fetching cron jobs data:', error);
      setError('Failed to load cron jobs data');
    } finally {
      setIsLoading(false);
    }
  };

  const handleRefresh = () => {
    setRefreshKey(prev => prev + 1);
  };

  const handleJobTriggered = () => {
    // Refresh data after a job is triggered
    setTimeout(() => {
      handleRefresh();
    }, 1000);
  };

  if (!isAdmin) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Access Denied</h2>
          <p className="text-gray-600">Administrator role required to access cron job management.</p>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Spinner size="lg" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="rounded-lg bg-red-50 p-6">
        <h2 className="text-lg font-semibold text-red-800 mb-2">Error</h2>
        <p className="text-red-600 mb-4">{error}</p>
        <Button onClick={handleRefresh} variant="outline">
          Try Again
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Cron Job Management</h1>
          <p className="text-gray-600 mt-1">
            Monitor and manage automated background jobs
          </p>
        </div>
        <Button onClick={handleRefresh} variant="outline">
          Refresh
        </Button>
      </div>

      {/* Tabs */}
      <Tabs defaultValue="jobs" className="w-full">
        <TabsList>
          <TabsTrigger value="jobs">Active Jobs</TabsTrigger>
          <TabsTrigger value="history">Execution History</TabsTrigger>
        </TabsList>

        <TabsContent value="jobs" className="mt-6">
          <div className="rounded-lg bg-white shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">Cron Jobs Status</h2>
              <p className="text-sm text-gray-600 mt-1">
                Overview of all configured cron jobs and their current status
              </p>
            </div>
            <div className="p-6">
              <CronJobTable 
                jobs={jobsWithStatus} 
                onJobTriggered={handleJobTriggered}
                onRefresh={handleRefresh}
              />
            </div>
          </div>
        </TabsContent>

        <TabsContent value="history" className="mt-6">
          <div className="rounded-lg bg-white shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">Execution History</h2>
              <p className="text-sm text-gray-600 mt-1">
                Recent executions across all cron jobs
              </p>
            </div>
            <div className="p-6">
              <CronJobExecutionHistory executions={allExecutions} />
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
