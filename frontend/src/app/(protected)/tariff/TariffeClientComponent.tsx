"use client";

import * as React from "react";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { tariffsApi } from "~/services/tariffs-api";

// Lokaler Tarif-Typ für die Komponente
interface LocalTariff {
  id: string;
  documentId: string;
  name: string;
  validFrom: string;
  validTo: string;
  chargerType?: "AC" | "DC";
  dailySchedules: any[];
  blockFeeSchedules: any[];
  mandants: any[];
  terminals?: any[];
  ocpi_evses?: any[];
  ocpi_locations?: any[];
  // Raw pricing data for analysis
  priceAC?: any[];
  priceDC?: any[];
  blockFeeAC?: any[];
  blockFeeDC?: any[];
}

// Interface for pricing component analysis
interface PricingAnalysis {
  acPricesCount: number;
  dcPricesCount: number;
  acBlockingFeesCount: number;
  dcBlockingFeesCount: number;
  acPricesActive: boolean;
  dcPricesActive: boolean;
  acBlockingFeesActive: boolean;
  dcBlockingFeesActive: boolean;
}

interface TariffeClientComponentProps {
  initialTariffs: LocalTariff[];
}

// Helper function to check if a component is currently active based on its validity period
function isComponentActive(validFrom: string, validTo: string | null | undefined): boolean {
  const now = new Date();
  const from = new Date(validFrom);

  // Handle null or undefined validTo (unlimited validity)
  if (!validTo || validTo === null || validTo === undefined) {
    return now >= from; // Active if current time is after validFrom
  }

  const to = new Date(validTo);
  // Check if it's a valid date and not Unix epoch
  if (isNaN(to.getTime()) || to.getTime() === 0) {
    return now >= from; // Treat as unlimited validity
  }

  return now >= from && now <= to;
}

// Helper function to check if any component in an array is currently active
function hasActiveComponents(components: any[]): boolean {
  if (!components || components.length === 0) return false;

  return components.some(component =>
    component.validFrom && isComponentActive(component.validFrom, component.validTo)
  );
}

// Helper function to check if a tariff is currently active
function isTariffActive(tariff: LocalTariff): boolean {
  // A tariff is active if ANY of its pricing components are currently active
  return (
    hasActiveComponents(tariff.priceAC || []) ||
    hasActiveComponents(tariff.priceDC || []) ||
    hasActiveComponents(tariff.blockFeeAC || []) ||
    hasActiveComponents(tariff.blockFeeDC || [])
  );
}

// Helper function to analyze pricing components
function analyzePricingComponents(tariff: LocalTariff): PricingAnalysis {
  return {
    acPricesCount: tariff.priceAC?.length || 0,
    dcPricesCount: tariff.priceDC?.length || 0,
    acBlockingFeesCount: tariff.blockFeeAC?.length || 0,
    dcBlockingFeesCount: tariff.blockFeeDC?.length || 0,
    acPricesActive: hasActiveComponents(tariff.priceAC || []),
    dcPricesActive: hasActiveComponents(tariff.priceDC || []),
    acBlockingFeesActive: hasActiveComponents(tariff.blockFeeAC || []),
    dcBlockingFeesActive: hasActiveComponents(tariff.blockFeeDC || []),
  };
}

// Component for status badges
function StatusBadge({ active, count, label }: { active: boolean; count: number; label: string }) {
  if (count === 0) {
    return (
      <div className="flex items-center gap-1 px-2 py-1 rounded-full bg-gray-100 text-gray-500 text-xs">
        <div className="w-2 h-2 rounded-full bg-gray-300"></div>
        <span>{label}: 0</span>
      </div>
    );
  }

  return (
    <div className={`flex items-center gap-1 px-2 py-1 rounded-full text-xs ${
      active
        ? 'bg-green-100 text-green-700'
        : 'bg-yellow-100 text-yellow-700'
    }`}>
      <div className={`w-2 h-2 rounded-full ${
        active ? 'bg-green-500' : 'bg-yellow-500'
      }`}></div>
      <span>{label}: {count}</span>
      {active && <span className="text-xs">✓</span>}
    </div>
  );
}

export default function TariffeClientComponent({ initialTariffs }: TariffeClientComponentProps) {
  const router = useRouter();
  const [tariffs, setTariffs] = useState<LocalTariff[]>(initialTariffs);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState<string | null>(null);

  // Funktion zum Anzeigen eines Tarifs
  const handleTariffClick = (tariff: LocalTariff) => {
    console.log('Tarif zum Anzeigen ausgewählt:', tariff);
    // Verwende die documentId für die Navigation zur Detailseite
    router.push(`/tariff/${tariff.documentId}`);
  };

  // Funktion zum Löschen eines Tarifs
  const handleDeleteTariff = async (tariff: LocalTariff) => {
    // Bestätigung vom Benutzer einholen
    if (!window.confirm(`Möchten Sie den Tarif "${tariff.name}" wirklich löschen?`)) {
      return;
    }

    try {
      setIsDeleting(tariff.documentId);

      // Tarif über API löschen (soft delete)
      await tariffsApi.delete(tariff.documentId);

      // Tarif aus der lokalen Liste entfernen
      setTariffs(prevTariffs =>
        prevTariffs.filter(t => t.documentId !== tariff.documentId)
      );

      // Erfolgsmeldung anzeigen
      setSuccessMessage(`Tarif "${tariff.name}" wurde erfolgreich gelöscht.`);

      // Erfolgsmeldung nach 5 Sekunden ausblenden
      setTimeout(() => {
        setSuccessMessage(null);
      }, 5000);

    } catch (error) {
      console.error('Fehler beim Löschen des Tarifs:', error);
      alert('Fehler beim Löschen des Tarifs. Bitte versuchen Sie es später erneut.');
    } finally {
      setIsDeleting(null);
    }
  };

  return (
    <div className="container mx-auto p-6">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Tarife</h1>
        <button
          onClick={() => router.push('/tariff/new')}
          className="btn-primary"
        >
          Neuen Tarif erstellen
        </button>
      </div>

      {successMessage && (
        <div className="mb-6 p-4 bg-green-100 border border-green-400 text-green-700 rounded-lg">
          {successMessage}
        </div>
      )}

      {tariffs.length === 0 ? (
        <div className="text-center py-12">
          <div className="max-w-md mx-auto">
            <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
              <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">Keine Tarife gefunden</h3>
            <p className="text-gray-500">Erstellen Sie Ihren ersten Tarif, um zu beginnen.</p>
          </div>
        </div>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {tariffs.map((tariff) => {
            const analysis = analyzePricingComponents(tariff);
            const isActive = isTariffActive(tariff);

            return (
              <div
                key={tariff.id}
                className="bg-white rounded-xl shadow-sm border border-gray-200 hover:shadow-md transition-all duration-200 overflow-hidden"
              >
                {/* Header */}
                <div className="p-6 pb-4">
                  <div className="flex items-start justify-between mb-3">
                    <h3 className="text-lg font-semibold text-gray-900 truncate pr-2">
                      {tariff.name}
                    </h3>
                    <div className={`px-2 py-1 rounded-full text-xs font-medium ${
                      isActive
                        ? 'bg-green-100 text-green-700'
                        : 'bg-gray-100 text-gray-600'
                    }`}>
                      {isActive ? 'Aktiv' : 'Inaktiv'}
                    </div>
                  </div>

                  {/* Component Status Summary */}
                  <div className="text-sm text-gray-600 mb-4">
                    <div className="flex items-center gap-1">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <span>
                        {analysis.acPricesCount + analysis.dcPricesCount + analysis.acBlockingFeesCount + analysis.dcBlockingFeesCount} Komponenten konfiguriert
                      </span>
                    </div>
                  </div>
                </div>

                {/* Pricing Components */}
                <div className="px-6 pb-4">
                  <h4 className="text-sm font-medium text-gray-700 mb-3">Preiskomponenten</h4>
                  <div className="grid grid-cols-2 gap-2 mb-4">
                    <StatusBadge
                      active={analysis.acPricesActive}
                      count={analysis.acPricesCount}
                      label="AC Preise"
                    />
                    <StatusBadge
                      active={analysis.dcPricesActive}
                      count={analysis.dcPricesCount}
                      label="DC Preise"
                    />
                    <StatusBadge
                      active={analysis.acBlockingFeesActive}
                      count={analysis.acBlockingFeesCount}
                      label="AC Blockgebühren"
                    />
                    <StatusBadge
                      active={analysis.dcBlockingFeesActive}
                      count={analysis.dcBlockingFeesCount}
                      label="DC Blockgebühren"
                    />
                  </div>
                </div>

                {/* Assignments */}
                <div className="px-6 pb-4">
                  <h4 className="text-sm font-medium text-gray-700 mb-3">Zuordnungen</h4>
                  <div className="space-y-2 text-sm text-gray-600">
                    <div className="flex items-center justify-between">
                      <span>Mandanten:</span>
                      <span className="font-medium">
                        {tariff.mandants.length > 0 ? tariff.mandants.length : '0'}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>Terminals:</span>
                      <span className="font-medium">
                        {tariff.terminals?.length || 0}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>EVSEs:</span>
                      <span className="font-medium">
                        {tariff.ocpi_evses?.length || 0}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span>Standorte:</span>
                      <span className="font-medium">
                        {tariff.ocpi_locations?.length || 0}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Actions */}
                <div className="px-6 py-4 bg-gray-50 border-t border-gray-100">
                  <div className="flex gap-2">
                    <button
                      onClick={() => handleTariffClick(tariff)}
                      className="flex-1 px-3 py-2 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 transition-colors"
                    >
                      Details anzeigen
                    </button>
                    <button
                      onClick={() => handleDeleteTariff(tariff)}
                      disabled={isDeleting === tariff.documentId}
                      className="px-3 py-2 bg-red-100 text-red-700 text-sm font-medium rounded-lg hover:bg-red-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                    >
                      {isDeleting === tariff.documentId ? (
                        <div className="flex items-center gap-1">
                          <svg className="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                          </svg>
                          <span>Löschen...</span>
                        </div>
                      ) : (
                        'Löschen'
                      )}
                    </button>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
}
