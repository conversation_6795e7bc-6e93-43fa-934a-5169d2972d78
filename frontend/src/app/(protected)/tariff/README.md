# Tariff Management Module

This module has been refactored to improve maintainability and code organization. The original `neu/page.tsx` file (2210 lines) has been broken down into smaller, reusable components and utilities.

## Structure

```
frontend/src/app/(protected)/tarife/
├── components/
│   ├── HelpText.tsx                 # Help text component with toggle functionality
│   ├── HourCell.tsx                 # Individual hour cell for calendar
│   ├── PriceForm/
│   │   ├── index.tsx               # Main price form component
│   │   ├── FixedPriceForm.tsx      # Fixed price configuration
│   │   └── EPEXPriceForm.tsx       # EPEX price configuration
│   ├── PriceOverview.tsx           # Overview of configured prices
│   ├── BlockingFeeForm.tsx         # Blocking fee configuration
│   ├── WeeklyCalendar.tsx          # Weekly calendar for hour selection
│   ├── EPEXPriceSimulator.tsx      # EPEX price simulator (existing)
│   └── BlockingFeeSimulator.tsx    # Blocking fee simulator (existing)
├── hooks/
│   └── useCalendarSelection.ts     # Calendar selection logic with range support
├── utils/
│   ├── vatCalculations.ts          # VAT calculation utilities
│   ├── priceGrouping.ts            # Price and blocking fee grouping logic
│   └── colorGeneration.ts          # Color generation for price visualization
├── types/
│   └── tariff.types.ts             # TypeScript interfaces and types
└── neu/
    └── page.tsx                    # Main page (significantly reduced)
```

## Components

### Core Components

- **HelpText**: Reusable help text component with toggle functionality
- **HourCell**: Individual calendar cell with price visualization
- **PriceForm**: Main form for price configuration (AC/DC)
  - **FixedPriceForm**: Sub-component for fixed price settings
  - **EPEXPriceForm**: Sub-component for EPEX price settings
- **PriceOverview**: Displays grouped price configurations
- **BlockingFeeForm**: Form for blocking fee configuration
- **WeeklyCalendar**: Calendar component for hour selection

### Hooks

- **useCalendarSelection**: Manages calendar cell selection with Shift-click range support

### Utilities

- **vatCalculations**: VAT calculation functions (19% German VAT)
- **priceGrouping**: Groups consecutive hours with same pricing/blocking fees
- **colorGeneration**: Generates consistent colors for price visualization

### Types

- **tariff.types.ts**: Centralized TypeScript interfaces for all tariff-related data

## Key Features

### Calendar Selection
- Single cell selection
- Range selection with Shift+click
- Select all hours for a day
- Select specific hour for all days
- Separate selection states for pricing and blocking fees

### Price Configuration
- Fixed pricing with VAT calculations
- EPEX dynamic pricing with margins, limits, and fallbacks
- Visual price overview with grouping
- Price removal functionality

### Blocking Fees
- Per-minute fees with grace periods
- Maximum fee limits
- Visual overview with grouping
- Fee removal functionality

## Benefits of Refactoring

1. **Maintainability**: Smaller, focused components are easier to maintain
2. **Reusability**: Components can be reused across different parts of the application
3. **Type Safety**: Centralized TypeScript definitions
4. **Separation of Concerns**: Logic separated from UI components
5. **Testability**: Smaller components are easier to test
6. **Performance**: Better code splitting and loading

## Usage

The main page (`neu/page.tsx`) now imports and uses the refactored components:

```tsx
import { PriceForm } from "../components/PriceForm";
import { PriceOverview } from "../components/PriceOverview";
import { BlockingFeeForm } from "../components/BlockingFeeForm";
import { HourCell } from "../components/HourCell";
import { useCalendarSelection } from "../hooks/useCalendarSelection";
```

The calendar selection hook provides all necessary functions:

```tsx
const {
  selectedHours,
  selectedBlockingHours,
  toggleHourSelection,
  toggleAllHoursForDay,
  toggleHourForAllDays,
  // ... more functions
} = useCalendarSelection();
```

## Future Improvements

- Add unit tests for all components and utilities
- Implement error boundaries for better error handling
- Add loading states for async operations
- Consider adding a state management solution (Redux/Zustand) for complex state
- Add accessibility improvements (ARIA labels, keyboard navigation)
- Implement drag-and-drop for calendar selection
