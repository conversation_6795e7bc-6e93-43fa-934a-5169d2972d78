import { cookies } from "next/headers";
import { redirect } from "next/navigation";
import { notFound } from "next/navigation";
import { getAuthenticatedClient } from "~/services/strapi-client";
import TariffDetailClient from "./TariffDetailClient";

// Force dynamic rendering since we use cookies
export const dynamic = 'force-dynamic';

// Interface for the tariff detail data
interface TariffDetail {
  id: string;
  documentId: string;
  name: string;
  priceAC: any[];
  blockFeeAC: any[];
  priceDC: any[];
  blockFeeDC: any[];
  mandants: any[];
  terminals: any[];
  ocpi_evses: any[];
  ocpi_locations: any[];
  createdAt: string;
  updatedAt: string;
}

// Server function to load tariff details
async function getTariffDetail(documentId: string): Promise<TariffDetail | null> {
  try {
    const cookieStore = await cookies();
    const token = cookieStore.get("strapiToken")?.value;

    if (!token) {
      redirect("/login");
    }

    // Initialize Strapi Client
    const client = getAuthenticatedClient(token);
    const tariffs = client.collection('tariffs');

    // Load tariff with full population (only non-deleted)
    const response = await tariffs.find({
      filters: {
        documentId: {
          $eq: documentId
        },
        deleted: { $ne: true }
      },
      populate: [
        'mandants',
        'terminals',
        'ocpi_evses',
        'ocpi_locations',
        'priceAC.dailySchedules.hourlyRate',
        'blockFeeAC.blockFeeSchedule',
        'priceDC.dailySchedules.hourlyRate',
        'blockFeeDC.blockFeeSchedule'
      ]
    });

    if (!response || !response.data || response.data.length === 0) {
      return null;
    }

    const tariff = response.data[0];

    if (!tariff) {
      return null;
    }

    return {
      id: tariff.id,
      documentId: tariff.documentId,
      name: tariff.name,
      priceAC: tariff.priceAC || [],
      blockFeeAC: tariff.blockFeeAC || [],
      priceDC: tariff.priceDC || [],
      blockFeeDC: tariff.blockFeeDC || [],
      mandants: tariff.mandants || [],
      terminals: tariff.terminals || [],
      ocpi_evses: tariff.ocpi_evses || [],
      ocpi_locations: tariff.ocpi_locations || [],
      createdAt: tariff.createdAt,
      updatedAt: tariff.updatedAt
    };
  } catch (error) {
    console.error('Error loading tariff detail:', error);
    return null;
  }
}

interface PageProps {
  params: Promise<{
    id: string;
  }>;
}

export default async function TariffDetailPage({ params }: PageProps) {
  const { id } = await params;
  const tariff = await getTariffDetail(id);

  if (!tariff) {
    notFound();
  }

  return <TariffDetailClient tariff={tariff} />;
}
