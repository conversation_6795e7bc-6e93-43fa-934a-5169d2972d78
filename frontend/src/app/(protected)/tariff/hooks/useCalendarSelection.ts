"use client";

import { useState, useCallback } from "react";
import type { CellSelection, LastSelectedCell } from "../types/tariff.types";

/**
 * Hook for managing calendar cell selection with range selection support
 */
export function useCalendarSelection() {
  const [selectedHours, setSelectedHours] = useState<CellSelection>({});
  const [selectedBlockingHours, setSelectedBlockingHours] = useState<CellSelection>({});
  const [lastSelectedCell, setLastSelectedCell] = useState<LastSelectedCell | null>(null);
  const [lastSelectedBlockingCell, setLastSelectedBlockingCell] = useState<LastSelectedCell | null>(null);

  // Helper function to select a range of cells
  const selectCellRange = useCallback((
    startCell: LastSelectedCell,
    endCell: LastSelectedCell,
    currentSelection: CellSelection
  ): CellSelection => {
    const newSelection = { ...currentSelection };

    // Calculate total hours between the two cells
    const startTotalHour = startCell.day * 24 + startCell.hour;
    const endTotalHour = endCell.day * 24 + endCell.hour;

    // Determine start and end points (regardless of selection order)
    const minTotalHour = Math.min(startTotalHour, endTotalHour);
    const maxTotalHour = Math.max(startTotalHour, endTotalHour);

    // Mark all cells in the range
    for (let totalHour = minTotalHour; totalHour <= maxTotalHour; totalHour++) {
      const day = Math.floor(totalHour / 24);
      const hour = totalHour % 24;

      // Only consider days 0-4 (Mon-Fri)
      if (day >= 0 && day <= 4) {
        const key = `${day}-${hour}`;
        newSelection[key] = true;
      }
    }

    return newSelection;
  }, []);

  // Toggle selection of an hour cell
  const toggleHourSelection = useCallback((
    hour: number,
    day: number,
    shiftKey: boolean = false,
  ) => {
    try {
      const key = `${day}-${hour}`;
      const currentCell = { day, hour };

      // If Shift is pressed and there's a previous selection
      if (shiftKey && lastSelectedCell) {
        // Select range between last and current cell
        const newSelectedHours = selectCellRange(lastSelectedCell, currentCell, selectedHours);
        setSelectedHours(newSelectedHours);
      } else {
        // Normal toggle selection
        setSelectedHours((prev) => ({
          ...prev,
          [key]: !prev[key],
        }));
      }

      // Update last selected cell
      setLastSelectedCell(currentCell);
    } catch (error) {
      console.error("Error in toggleHourSelection:", error);
    }
  }, [lastSelectedCell, selectedHours, selectCellRange]);

  // Toggle selection of all hours for a day
  const toggleAllHoursForDay = useCallback((day: number) => {
    try {
      // Check if all hours of the day are already selected
      const allSelected = Array.from({ length: 24 }, (_, hour) => {
        const key = `${day}-${hour}`;
        return selectedHours[key] === true;
      }).every(Boolean);

      // Create new object with updated selections
      const newSelectedHours = { ...selectedHours };

      // If all hours are selected, deselect all, otherwise select all
      for (let hour = 0; hour < 24; hour++) {
        const key = `${day}-${hour}`;
        newSelectedHours[key] = !allSelected;
      }

      setSelectedHours(newSelectedHours);
    } catch (error) {
      console.error("Error in toggleAllHoursForDay:", error);
    }
  }, [selectedHours]);

  // Toggle selection of an hour for all days
  const toggleHourForAllDays = useCallback((hour: number) => {
    try {
      // Check if the hour is already selected for all days
      const allSelected = Array.from({ length: 5 }, (_, day) => {
        const key = `${day}-${hour}`;
        return selectedHours[key] === true;
      }).every(Boolean);

      // Create new object with updated selections
      const newSelectedHours = { ...selectedHours };

      // If the hour is selected for all days, deselect it, otherwise select it
      for (let day = 0; day < 5; day++) {
        const key = `${day}-${hour}`;
        newSelectedHours[key] = !allSelected;
      }

      setSelectedHours(newSelectedHours);
    } catch (error) {
      console.error("Error in toggleHourForAllDays:", error);
    }
  }, [selectedHours]);

  // Toggle selection of a blocking hour cell with Shift-click support
  const toggleBlockingHourSelection = useCallback((
    hour: number,
    day: number,
    shiftKey: boolean = false,
  ) => {
    try {
      const key = `${day}-${hour}`;
      const currentCell = { day, hour };

      // If Shift is pressed and there's a previous selection
      if (shiftKey && lastSelectedBlockingCell) {
        // Select range between last and current cell
        const newSelectedBlockingHours = selectCellRange(lastSelectedBlockingCell, currentCell, selectedBlockingHours);
        setSelectedBlockingHours(newSelectedBlockingHours);
      } else {
        // Normal toggle selection
        setSelectedBlockingHours((prev) => ({
          ...prev,
          [key]: !prev[key],
        }));
      }

      // Update last selected cell
      setLastSelectedBlockingCell(currentCell);
    } catch (error) {
      console.error("Error in toggleBlockingHourSelection:", error);
    }
  }, [lastSelectedBlockingCell, selectedBlockingHours, selectCellRange]);

  // Toggle selection of all hours for a day for blocking fees
  const toggleAllHoursForDayBlocking = useCallback((day: number) => {
    try {
      // Check if all hours of the day are already selected
      const allSelected = Array.from({ length: 24 }, (_, hour) => {
        const key = `${day}-${hour}`;
        return selectedBlockingHours[key] === true;
      }).every(Boolean);

      // Create new object with updated selections
      const newSelectedBlockingHours = { ...selectedBlockingHours };

      // If all hours are selected, deselect all, otherwise select all
      for (let hour = 0; hour < 24; hour++) {
        const key = `${day}-${hour}`;
        newSelectedBlockingHours[key] = !allSelected;
      }

      setSelectedBlockingHours(newSelectedBlockingHours);
    } catch (error) {
      console.error("Error in toggleAllHoursForDayBlocking:", error);
    }
  }, [selectedBlockingHours]);

  // Toggle selection of an hour for all days for blocking fees
  const toggleHourForAllDaysBlocking = useCallback((hour: number) => {
    try {
      // Check if the hour is already selected for all days
      const allSelected = Array.from({ length: 5 }, (_, day) => {
        const key = `${day}-${hour}`;
        return selectedBlockingHours[key] === true;
      }).every(Boolean);

      // Create new object with updated selections
      const newSelectedBlockingHours = { ...selectedBlockingHours };

      // If the hour is selected for all days, deselect it, otherwise select it
      for (let day = 0; day < 5; day++) {
        const key = `${day}-${hour}`;
        newSelectedBlockingHours[key] = !allSelected;
      }

      setSelectedBlockingHours(newSelectedBlockingHours);
    } catch (error) {
      console.error("Error in toggleHourForAllDaysBlocking:", error);
    }
  }, [selectedBlockingHours]);

  // Clear all selections
  const clearSelections = useCallback(() => {
    setSelectedHours({});
    setSelectedBlockingHours({});
    setLastSelectedCell(null);
    setLastSelectedBlockingCell(null);
  }, []);

  return {
    selectedHours,
    selectedBlockingHours,
    setSelectedHours,
    setSelectedBlockingHours,
    toggleHourSelection,
    toggleAllHoursForDay,
    toggleHourForAllDays,
    toggleBlockingHourSelection,
    toggleAllHoursForDayBlocking,
    toggleHourForAllDaysBlocking,
    clearSelections,
  };
}
