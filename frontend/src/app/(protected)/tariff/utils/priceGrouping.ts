import type { HourlyRate, PriceGroup, BlockingFeeGroup, BlockingFeeSchedule } from "../types/tariff.types";

const weekdays = ["Montag", "<PERSON>nstag", "Mi<PERSON><PERSON>ch", "Donnerstag", "Freitag"];

/**
 * Group hourly rates by consecutive hours with same pricing
 * @param rates - Hourly rates object
 * @returns Array of grouped price ranges
 */
export function groupHourlyRates(rates: { [key: string]: HourlyRate }): PriceGroup[] {
  // Convert to array with sorting
  const ratesArray = Object.entries(rates).map(([key, rate]) => {
    const [day, hour] = key.split('-').map(Number);
    return {
      day,
      hour,
      weekday: weekdays[day ?? 0] || "Unbekannt",
      ...rate,
      key
    };
  }).sort((a, b) => (a.day ?? 0) * 24 + (a.hour ?? 0) - ((b.day ?? 0) * 24 + (b.hour ?? 0)));

  // Group consecutive hours with same prices
  const groups: PriceGroup[] = [];
  let currentGroup: any = null;

  for (const rate of ratesArray) {
    const isSameSettings = currentGroup &&
      currentGroup.pricePerKwh === rate.pricePerKwh &&
      currentGroup.sessionFee === rate.sessionFee &&
      currentGroup.priceType === rate.priceType &&
      // EPEX-specific comparisons
      currentGroup.fallbackPricePerKwh === rate.fallbackPricePerKwh &&
      currentGroup.minPricePerKwh === rate.minPricePerKwh &&
      currentGroup.maxPricePerKwh === rate.maxPricePerKwh &&
      currentGroup.marginType === rate.marginType &&
      currentGroup.marginValue === rate.marginValue &&
      currentGroup.taxesAndLevies === rate.taxesAndLevies &&
      currentGroup.epexAveragingPeriodHours === rate.epexAveragingPeriodHours;

    const isConsecutive = currentGroup &&
      ((currentGroup.endDay === rate.day && currentGroup.endHour + 1 === rate.hour) ||
       (currentGroup.endDay === (rate.day ?? 0) - 1 && currentGroup.endHour === 23 && rate.hour === 0));

    if (isSameSettings && isConsecutive) {
      // Extend current group
      currentGroup.endDay = rate.day;
      currentGroup.endHour = rate.hour;
      currentGroup.endWeekday = rate.weekday;
      currentGroup.count++;
    } else {
      // Start new group
      if (currentGroup) {
        groups.push(currentGroup);
      }
      currentGroup = {
        startDay: rate.day,
        startHour: rate.hour,
        endDay: rate.day,
        endHour: rate.hour,
        startWeekday: rate.weekday,
        endWeekday: rate.weekday,
        pricePerKwh: rate.pricePerKwh,
        sessionFee: rate.sessionFee,
        priceType: rate.priceType,
        count: 1,
        // EPEX-specific fields
        fallbackPricePerKwh: rate.fallbackPricePerKwh,
        minPricePerKwh: rate.minPricePerKwh,
        maxPricePerKwh: rate.maxPricePerKwh,
        marginType: rate.marginType,
        marginValue: rate.marginValue,
        taxesAndLevies: rate.taxesAndLevies,
        epexAveragingPeriodHours: rate.epexAveragingPeriodHours
      };
    }
  }

  if (currentGroup) {
    groups.push(currentGroup);
  }

  return groups;
}

/**
 * Group blocking fee schedules by consecutive hours with same fees
 * @param schedules - Blocking fee schedules object
 * @returns Array of grouped blocking fee ranges
 */
export function groupBlockingFeeSchedules(schedules: { [key: string]: BlockingFeeSchedule }): BlockingFeeGroup[] {
  // Convert to array with sorting
  const schedulesArray = Object.entries(schedules).map(([key, schedule]) => {
    const [day, hour] = key.split('-').map(Number);
    return {
      day,
      hour,
      weekday: weekdays[day ?? 0] || "Unbekannt",
      ...schedule,
      key
    };
  }).sort((a, b) => (a.day ?? 0) * 24 + (a.hour ?? 0) - ((b.day ?? 0) * 24 + (b.hour ?? 0)));

  // Group consecutive hours with same fees
  const groups: BlockingFeeGroup[] = [];
  let currentGroup: any = null;

  for (const schedule of schedulesArray) {
    const isSameSettings = currentGroup &&
      currentGroup.perMinute === schedule.perMinute &&
      currentGroup.maxFee === schedule.maxFee &&
      currentGroup.gracePeriod === schedule.gracePeriod;

    const isConsecutive = currentGroup &&
      ((currentGroup.endDay === schedule.day && currentGroup.endHour + 1 === schedule.hour) ||
       (currentGroup.endDay === (schedule.day ?? 0) - 1 && currentGroup.endHour === 23 && schedule.hour === 0));

    if (isSameSettings && isConsecutive) {
      // Extend current group
      currentGroup.endDay = schedule.day;
      currentGroup.endHour = schedule.hour;
      currentGroup.endWeekday = schedule.weekday;
      currentGroup.count++;
    } else {
      // Start new group
      if (currentGroup) {
        groups.push(currentGroup);
      }
      currentGroup = {
        startDay: schedule.day,
        startHour: schedule.hour,
        endDay: schedule.day,
        endHour: schedule.hour,
        startWeekday: schedule.weekday,
        endWeekday: schedule.weekday,
        perMinute: schedule.perMinute,
        maxFee: schedule.maxFee,
        gracePeriod: schedule.gracePeriod,
        count: 1
      };
    }
  }

  if (currentGroup) {
    groups.push(currentGroup);
  }

  return groups;
}

/**
 * Format time range for display
 * @param group - Price or blocking fee group
 * @returns Formatted time range string
 */
export function formatTimeRange(group: { startDay: number; startHour: number; endDay: number; endHour: number; startWeekday: string; endWeekday: string }): string {
  if (group.startDay === group.endDay && group.startHour === group.endHour) {
    // Single hour
    return `${group.startWeekday} ${group.startHour.toString().padStart(2, '0')}:00`;
  } else if (group.startDay === group.endDay) {
    // Multiple hours on same day
    return `${group.startWeekday} ${group.startHour.toString().padStart(2, '0')}:00 - ${group.endHour.toString().padStart(2, '0')}:00`;
  } else {
    // Across multiple days
    return `${group.startWeekday} ${group.startHour.toString().padStart(2, '0')}:00 - ${group.endWeekday} ${group.endHour.toString().padStart(2, '0')}:00`;
  }
}

/**
 * Generate keys for a time range
 * @param group - Price or blocking fee group
 * @returns Array of keys for the time range
 */
export function generateKeysForTimeRange(group: { startDay: number; startHour: number; endDay: number; endHour: number }): string[] {
  const keys: string[] = [];
  for (let day = group.startDay; day <= group.endDay; day++) {
    const startHour = day === group.startDay ? group.startHour : 0;
    const endHour = day === group.endDay ? group.endHour : 23;
    for (let hour = startHour; hour <= endHour; hour++) {
      keys.push(`${day}-${hour}`);
    }
  }
  return keys;
}
