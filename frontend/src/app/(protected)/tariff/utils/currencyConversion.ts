// Currency conversion utilities for Euro <-> Cent conversion

/**
 * Convert Euro (decimal) to Cent (integer)
 * @param euro - Amount in Euro (e.g., 0.29)
 * @returns Amount in Cent (e.g., 29)
 */
export function euroToCent(euro: number | string): number {
  const euroValue = typeof euro === 'string' ? parseFloat(euro) : euro;
  if (isNaN(euroValue)) return 0;
  return Math.round(euroValue * 100);
}

/**
 * Convert Cent (integer) to Euro (decimal)
 * @param cent - Amount in Cent (e.g., 29)
 * @returns Amount in Euro (e.g., 0.29)
 */
export function centToEuro(cent: number): number {
  if (isNaN(cent)) return 0;
  return cent / 100;
}

/**
 * Convert Euro string to Cent integer safely
 * @param euroString - String representation of Euro amount
 * @returns Cent amount as integer
 */
export function euroStringToCent(euroString: string | undefined): number {
  if (!euroString || euroString === '') return 0;
  return euroToCent(euroString);
}

/**
 * Convert Euro string to Euro number safely
 * @param euroString - String representation of Euro amount
 * @returns Euro amount as number
 */
export function euroStringToEuro(euroString: string | undefined): number {
  if (!euroString || euroString === '') return 0;
  const value = parseFloat(euroString);
  return isNaN(value) ? 0 : value;
}

/**
 * Format cent amount as Euro string for display
 * @param cent - Amount in Cent
 * @returns Formatted Euro string (e.g., "0.29")
 */
export function formatCentAsEuro(cent: number): string {
  return (cent / 100).toFixed(2);
}

/**
 * Format Euro amount as Cent string for display
 * @param euro - Amount in Euro
 * @returns Formatted Cent string (e.g., "29")
 */
export function formatEuroAsCent(euro: number): string {
  return Math.round(euro * 100).toString();
}
