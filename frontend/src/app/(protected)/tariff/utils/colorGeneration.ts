// Color generation utilities for price visualization

/**
 * Generate a consistent color for price combinations
 * @param pricePerKwh - Price per kWh
 * @param sessionFee - Session fee
 * @returns HSL color string
 */
export function getPriceColor(pricePerKwh: number, sessionFee: number): string {
  // Combine both prices to create a unique value
  const combinedPrice = `${pricePerKwh.toFixed(2)}-${sessionFee.toFixed(2)}`;

  // Predefined colors for specific price levels
  const priceColors: Record<string, string> = {
    // We can add more colors later if we want to highlight specific prices
  };

  // If we have a predefined color, use it
  if (priceColors[combinedPrice]) {
    return priceColors[combinedPrice];
  }

  // Otherwise generate a color based on the price
  // We use a hash function to create a consistent color
  let hash = 0;
  for (let i = 0; i < combinedPrice.length; i++) {
    hash = combinedPrice.charCodeAt(i) + ((hash << 5) - hash);
  }

  // Convert the hash to an HSL color
  // We use HSL because we can control brightness and saturation
  const h = Math.abs(hash % 360); // Hue (0-360)
  const s = 70 + Math.abs((hash >> 8) % 30); // Saturation (70-100%)
  const l = 80 + Math.abs((hash >> 16) % 10); // Lightness (80-90%)

  return `hsl(${h}, ${s}%, ${l}%)`;
}
