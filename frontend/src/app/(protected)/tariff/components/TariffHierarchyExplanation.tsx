"use client";

import * as React from "react";
import { useState } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { ChevronDown, ChevronUp, Info, ArrowDown } from "lucide-react";

export default function TariffHierarchyExplanation() {
  const [isExpanded, setIsExpanded] = useState(false);

  const hierarchyLevels = [
    {
      level: 1,
      title: "Ladepunkt (EVSE)",
      description: "Tarif direkt einem spezifischen Ladepunkt zugeordnet",
      icon: "🔌",
      color: "bg-red-50 border-red-200 text-red-800"
    },
    {
      level: 2,
      title: "Terminal",
      description: "Tarif einem Terminal zugeordnet (gilt für alle Ladepunkte des Terminals)",
      icon: "📱",
      color: "bg-orange-50 border-orange-200 text-orange-800"
    },
    {
      level: 3,
      title: "Standort (Location)",
      description: "Tarif einem Standort zugeordnet (gilt für alle Terminals und Ladepunkte des Standorts)",
      icon: "📍",
      color: "bg-yellow-50 border-yellow-200 text-yellow-800"
    },
    {
      level: 4,
      title: "Mandant (Tenant)",
      description: "Standard-Tarif für den gesamten Mandanten (Fallback wenn keine spezifischeren Zuordnungen vorhanden)",
      icon: "🏢",
      color: "bg-blue-50 border-blue-200 text-blue-800"
    }
  ];

  return (
    <Card className="mb-6">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <Info className="h-5 w-5 mr-2 text-blue-600" />
            <CardTitle>Tarif-Auflösungshierarchie</CardTitle>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
          >
            {isExpanded ? (
              <>
                <ChevronUp className="h-4 w-4 mr-1" />
                Weniger anzeigen
              </>
            ) : (
              <>
                <ChevronDown className="h-4 w-4 mr-1" />
                Mehr anzeigen
              </>
            )}
          </Button>
        </div>
      </CardHeader>
      
      {isExpanded && (
        <CardContent>
          <div className="space-y-4">
            <p className="text-gray-600 mb-6">
              Das System verwendet eine hierarchische Reihenfolge zur Bestimmung des gültigen Tarifs für einen Ladevorgang. 
              Die Suche erfolgt von der spezifischsten zur allgemeinsten Ebene:
            </p>

            <div className="space-y-3">
              {hierarchyLevels.map((level, index) => (
                <div key={level.level}>
                  <div className={`p-4 rounded-lg border-2 ${level.color}`}>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="text-2xl mr-3">{level.icon}</div>
                        <div>
                          <div className="font-semibold">
                            {level.level}. {level.title}
                          </div>
                          <div className="text-sm opacity-90">
                            {level.description}
                          </div>
                        </div>
                      </div>
                      <div className="text-lg font-bold opacity-60">
                        Priorität {level.level}
                      </div>
                    </div>
                  </div>
                  
                  {index < hierarchyLevels.length - 1 && (
                    <div className="flex justify-center py-2">
                      <ArrowDown className="h-4 w-4 text-gray-400" />
                    </div>
                  )}
                </div>
              ))}
            </div>

            <div className="mt-6 p-4 bg-gray-50 rounded-lg">
              <h4 className="font-semibold mb-2">💡 Konfigurationstipp:</h4>
              <ul className="text-sm text-gray-700 space-y-1">
                <li>• <strong>Mandant-Tarif:</strong> Definieren Sie einen Standard-Tarif auf Mandanten-Ebene als Fallback</li>
                <li>• <strong>Standort-spezifisch:</strong> Überschreiben Sie den Standard für bestimmte Standorte</li>
                <li>• <strong>Terminal-spezifisch:</strong> Definieren Sie spezielle Tarife für einzelne Terminals</li>
                <li>• <strong>Ladepunkt-spezifisch:</strong> Höchste Priorität für individuelle Ladepunkt-Konfigurationen</li>
              </ul>
            </div>

            <div className="mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
              <h4 className="font-semibold text-blue-800 mb-2">🔍 Wie funktioniert die Auflösung?</h4>
              <p className="text-sm text-blue-700">
                Wenn ein Kunde einen Ladevorgang startet, prüft das System zunächst, ob dem spezifischen Ladepunkt 
                ein Tarif zugeordnet ist. Falls nicht, wird die Hierarchie nach oben durchlaufen, bis ein gültiger 
                Tarif gefunden wird. Der erste gefundene Tarif wird verwendet.
              </p>
            </div>
          </div>
        </CardContent>
      )}
    </Card>
  );
}
