"use client";

import * as React from "react";
import { formatEuro } from "~/utils/formatters";
import { getPriceColor } from "../utils/colorGeneration";
import type { HourCellProps } from "../types/tariff.types";

/**
 * Component for individual hour cells in the weekly calendar
 */
export function HourCell({
  hour,
  day,
  isSelected,
  hasPrice,
  priceInfo,
  onToggle,
  compact = false,
}: HourCellProps) {
  // Determine background color based on price
  let bgColor = "bg-gray-50";
  let borderColor = "border-gray-200";
  let textColor = "text-gray-600";

  if (isSelected) {
    bgColor = "bg-blue-100";
    borderColor = "border-blue-500";
    textColor = "text-blue-600";
  } else if (hasPrice && priceInfo) {
    // Generate color based on price
    const priceColor = getPriceColor(
      priceInfo.pricePerKwh,
      priceInfo.sessionFee,
    );
    // Use custom background color with CSS variable
    bgColor = "";
    borderColor = "border-gray-300";
    textColor = "text-gray-700";
  }

  // Handler for click events with Shift detection
  const handleClick = (e: React.MouseEvent) => {
    try {
      onToggle(hour, day, e.shiftKey);
    } catch (error) {
      console.error("Error clicking cell:", error);
    }
  };

  return (
    <div
      className={`border cursor-pointer transition h-10 flex items-center justify-center ${compact ? "p-0.5 text-xs" : "p-2"} ${bgColor} ${borderColor} hover:opacity-80`}
      style={
        hasPrice && priceInfo
          ? {
              backgroundColor: getPriceColor(
                priceInfo.pricePerKwh,
                priceInfo.sessionFee,
              ),
            }
          : undefined
      }
      onClick={handleClick}
      title={
        hasPrice && priceInfo
          ? `${formatEuro(priceInfo.pricePerKwh)}€/kWh + ${formatEuro(priceInfo.sessionFee)}€ Sessiongebühr`
          : undefined
      }
    >
      <div className="flex items-center justify-center">
        {!compact && <span>{hour}:00</span>}
        {hasPrice && (
          <span className={`${textColor} text-xs font-bold ml-1`}>✓</span>
        )}
        {!hasPrice && isSelected && (
          <span className="text-blue-600 text-xs">●</span>
        )}
      </div>
    </div>
  );
}
