"use client";

import * as React from "react";
import { useState } from "react";
import { But<PERSON> } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { formatEuro } from "~/utils/formatters";
import { calculateNetPrice } from "../utils/vatCalculations";
import { groupBlockingFeeSchedules, formatTimeRange, generateKeysForTimeRange } from "../utils/priceGrouping";
import { HelpText } from "./HelpText";
import type { BlockingFeeFormProps } from "../types/tariff.types";

/**
 * Form component for blocking fee configuration
 */
export function BlockingFeeForm({
  type,
  onApplyBlockingFee,
  onRemoveBlockingFees,
  currentPricingType,
  acBlockingFeeSchedules,
  dcBlockingFeeSchedules,
  showBlockingFeeSimulator,
  setShowBlockingFeeSimulator
}: BlockingFeeFormProps) {
  const prefix = type.toLowerCase();
  const [perMinute, setPerMinute] = useState("0.10");
  const [maxFee, setMaxFee] = useState("10.00");
  const [gracePeriod, setGracePeriod] = useState("30");

  const handleApplyBlockingFee = () => {
    onApplyBlockingFee(
      parseFloat(perMinute) || 0,
      parseFloat(maxFee) || 0,
      parseInt(gracePeriod) || 0
    );
  };

  const blockingFeeGroups = React.useMemo(() => {
    if (!currentPricingType) return [];
    const schedules = currentPricingType === "AC" ? acBlockingFeeSchedules : dcBlockingFeeSchedules;
    return groupBlockingFeeSchedules(schedules);
  }, [currentPricingType, acBlockingFeeSchedules, dcBlockingFeeSchedules]);

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
        <div>
          <Label htmlFor={`${prefix}-blocking-fee-per-minute`}>
            Gebühr pro Minute (€ brutto)
            <HelpText>
              Gebühr die pro Minute nach der Karenzzeit berechnet wird.
            </HelpText>
          </Label>
          <div className="space-y-1">
            <Input
              id={`${prefix}-blocking-fee-per-minute`}
              type="number"
              step="0.01"
              min="0"
              value={perMinute}
              onChange={(e) => setPerMinute(e.target.value)}
            />
            <div className="text-xs text-gray-500">
              Netto: {formatEuro(calculateNetPrice(parseFloat(perMinute) || 0))}€/min
            </div>
          </div>
        </div>
        <div>
          <Label htmlFor={`${prefix}-max-blocking-fee`}>
            Maximale Gebühr (€ brutto)
            <HelpText>
              Maximale Blockiergebühr die berechnet werden kann.
            </HelpText>
          </Label>
          <div className="space-y-1">
            <Input
              id={`${prefix}-max-blocking-fee`}
              type="number"
              step="0.01"
              min="0"
              value={maxFee}
              onChange={(e) => setMaxFee(e.target.value)}
            />
            <div className="text-xs text-gray-500">
              Netto: {formatEuro(calculateNetPrice(parseFloat(maxFee) || 0))}€
            </div>
          </div>
        </div>
        <div>
          <Label htmlFor={`${prefix}-grace-period`}>
            Karenzzeit (Minuten)
            <HelpText>
              Zeit in Minuten, bevor die Blockiergebühr anfällt.
            </HelpText>
          </Label>
          <Input
            id={`${prefix}-grace-period`}
            type="number"
            min="0"
            value={gracePeriod}
            onChange={(e) => setGracePeriod(e.target.value)}
          />
        </div>
      </div>

      <div className="text-sm text-gray-600">
        <p>Wählen Sie die Stunden aus, für die diese Blockiergebühr gelten soll, und klicken Sie dann auf "Anwenden".</p>
      </div>

      {/* Overview of configured blocking fees */}
      {blockingFeeGroups.length > 0 && (
        <Card className="mt-4">
          <CardHeader>
            <CardTitle className="text-lg">Konfigurierte Blockiergebühren ({type})</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {blockingFeeGroups.map((group, index) => (
                <div key={index} className="flex justify-between items-center p-3 bg-orange-50 rounded-lg border border-orange-200">
                  <div className="flex-1">
                    <div className="font-medium text-gray-900">
                      {formatTimeRange(group)}
                    </div>
                    <div className="text-sm text-gray-600">
                      {group.count} Stunde{group.count !== 1 ? 'n' : ''}
                    </div>
                  </div>
                  <div className="flex-1 text-center">
                    <div className="text-sm">
                      <span className="font-medium">{formatEuro(group.perMinute)}€/min</span>
                    </div>
                    <div className="text-xs text-gray-600">
                      Max: {formatEuro(group.maxFee)}€
                    </div>
                  </div>
                  <div className="flex-1 text-center">
                    <div className="text-sm">
                      <span className="font-medium">{group.gracePeriod} min</span>
                    </div>
                    <div className="text-xs text-gray-600">
                      Karenzzeit
                    </div>
                  </div>
                  <div className="flex-shrink-0">
                    <button
                      type="button"
                      onClick={() => {
                        const keysToRemove = generateKeysForTimeRange(group);
                        onRemoveBlockingFees(keysToRemove);
                      }}
                      className="text-red-500 hover:text-red-700 p-1"
                      title="Blockiergebühr entfernen"
                    >
                      🗑️
                    </button>
                  </div>
                </div>
              ))}
            </div>

            <div className="mt-4 pt-3 border-t border-gray-200">
              <div className="text-sm text-gray-600">
                <strong>Gesamt:</strong> {blockingFeeGroups.reduce((sum, group) => sum + group.count, 0)} Stunden mit Blockiergebühren konfiguriert
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
