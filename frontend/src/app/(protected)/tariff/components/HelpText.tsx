"use client";

import * as React from "react";
import { useState } from "react";
import type { HelpTextProps } from "../types/tariff.types";

/**
 * Help text component with toggle functionality
 */
export function HelpText({ children, className = "" }: HelpTextProps) {
  const [isVisible, setIsVisible] = useState(false);

  return (
    <div className={`relative inline-block ${className}`}>
      <button
        type="button"
        onClick={() => setIsVisible(!isVisible)}
        className="ml-1 text-blue-500 hover:text-blue-700 text-sm"
        title="Hilfe anzeigen"
      >
        ℹ️
      </button>
      {isVisible && (
        <div className="absolute z-10 w-64 p-3 mt-1 text-sm bg-white border border-gray-300 rounded-lg shadow-lg">
          {children}
          <button
            onClick={() => setIsVisible(false)}
            className="absolute top-1 right-1 text-gray-400 hover:text-gray-600"
          >
            ×
          </button>
        </div>
      )}
    </div>
  );
}
