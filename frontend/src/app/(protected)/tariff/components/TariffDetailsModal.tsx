"use client";

import * as React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Trigger } from "~/components/ui/dialog";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "~/components/ui/card";
import { Eye, Calendar, Clock } from "lucide-react";
import { format } from "date-fns";
import { de } from "date-fns/locale";
import { formatValidityPeriod, calculateValidityStatus, getValidityStatusText } from "~/utils/formatters";

interface TariffDetailsModalProps {
  title: string;
  summary: any;
  type: "AC" | "DC";
  mode: "price" | "blockingFee";
  children?: React.ReactNode;
}

export function TariffDetailsModal({ title, summary, type, mode, children }: TariffDetailsModalProps) {
  if (!summary) return null;

  // Helper function to format dates
  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), "dd.MM.yyyy HH:mm", { locale: de });
    } catch {
      return dateString;
    }
  };

  // Helper function to get display price for comparison
  const getDisplayPrice = (rate: any) => {
    if (rate.priceType === 'EPEX') {
      return rate.fallbackPricePerKwh || 0;
    }
    return rate.pricePerKwh || 0;
  };

  // Helper function to get display text for price with all components
  const getDisplayText = (rate: any) => {
    const components: string[] = [];

    if (rate.priceType === 'EPEX') {
      // Main EPEX price display
      const fallbackPrice = rate.fallbackPricePerKwh || 0;
      components.push(`EPEX (${fallbackPrice.toFixed(2)} €/kWh)`);

      // Add margin details if available
      if (rate.marginValue && rate.marginValue > 0) {
        const marginText = rate.marginType === 'percentage'
          ? `+${rate.marginValue}%`
          : `+${rate.marginValue.toFixed(3)} €/kWh`;
        components.push(`Margin: ${marginText}`);
      }

      // Add taxes and levies if available
      if (rate.taxesAndLevies && rate.taxesAndLevies > 0) {
        // Convert from cent to euro if it's stored as integer
        const taxesValue = rate.taxesAndLevies > 10 ? rate.taxesAndLevies / 100 : rate.taxesAndLevies;
        components.push(`Steuern/Abgaben: ${taxesValue.toFixed(3)} €/kWh`);
      }

      // Add price limits if available
      const limits: string[] = [];
      if (rate.minPricePerKwh && rate.minPricePerKwh > 0) {
        // Convert from cent to euro if it's stored as integer
        const minPrice = rate.minPricePerKwh > 10 ? rate.minPricePerKwh / 100 : rate.minPricePerKwh;
        limits.push(`Min: ${minPrice.toFixed(2)} €/kWh`);
      }
      if (rate.maxPricePerKwh && rate.maxPricePerKwh > 0) {
        // Convert from cent to euro if it's stored as integer
        const maxPrice = rate.maxPricePerKwh > 10 ? rate.maxPricePerKwh / 100 : rate.maxPricePerKwh;
        limits.push(`Max: ${maxPrice.toFixed(2)} €/kWh`);
      }
      if (limits.length > 0) {
        components.push(`Limits: ${limits.join(', ')}`);
      }

    } else {
      // FIXED price
      const fixedPrice = rate.pricePerKwh || 0;
      components.push(`${fixedPrice.toFixed(2)} €/kWh (Festpreis)`);
    }

    // Add session fee if available and > 0
    if (rate.sessionFee && rate.sessionFee > 0) {
      components.push(`Session Fee: ${rate.sessionFee.toFixed(2)} €`);
    }

    return components.join(' | ');
  };

  // Helper function to consolidate consecutive hours with same price
  const consolidateHourlyRates = (hourlyRates: any[]) => {
    if (!hourlyRates || hourlyRates.length === 0) return [];

    // Sort by hourFrom to ensure correct order
    const sortedRates = [...hourlyRates].sort((a, b) => a.hourFrom - b.hourFrom);

    const consolidated: any[] = [];
    let currentGroup = { ...sortedRates[0] };

    for (let i = 1; i < sortedRates.length; i++) {
      const current = sortedRates[i];
      const prev = sortedRates[i - 1];

      // Get display prices for comparison
      const currentDisplayPrice = getDisplayPrice(current);
      const prevDisplayPrice = getDisplayPrice(prev);

      // Check if all pricing components are the same for proper consolidation
      const sameDisplayPrice = Math.abs(currentDisplayPrice - prevDisplayPrice) < 0.001; // Handle floating point precision
      const sameType = current.priceType === prev.priceType;
      const sameMargin = (current.marginValue || 0) === (prev.marginValue || 0) &&
                        (current.marginType || '') === (prev.marginType || '');
      const sameTaxes = (current.taxesAndLevies || 0) === (prev.taxesAndLevies || 0);
      const sameLimits = (current.minPricePerKwh || 0) === (prev.minPricePerKwh || 0) &&
                        (current.maxPricePerKwh || 0) === (prev.maxPricePerKwh || 0);
      const sameSessionFee = (current.sessionFee || 0) === (prev.sessionFee || 0);
      const sameFallback = (current.fallbackPricePerKwh || 0) === (prev.fallbackPricePerKwh || 0);
      const sameEpexPeriod = (current.epexAveragingPeriodHours || 0) === (prev.epexAveragingPeriodHours || 0);

      // For consecutive hours: current hour should be previous hour + 1
      const isConsecutive = current.hourFrom === prev.hourFrom + 1;

      // Check if all components are identical
      const allSame = sameDisplayPrice && sameType && sameMargin && sameTaxes &&
                     sameLimits && sameSessionFee && sameFallback && sameEpexPeriod;

      if (allSame && isConsecutive) {
        // Extend current group - update the end hour to include current hour
        currentGroup.hourTo = current.hourTo || current.hourFrom;
      } else {
        // Start new group
        consolidated.push(currentGroup);
        currentGroup = { ...current };
      }
    }

    // Add the last group
    consolidated.push(currentGroup);

    return consolidated;
  };

  // Helper function to consolidate consecutive time slots with same fee
  const consolidateTimeSlots = (timeSlots: any[]) => {
    if (!timeSlots || timeSlots.length === 0) return [];

    // Normalize time slots to have consistent format
    const normalizedSlots = timeSlots.map(slot => ({
      ...slot,
      startHour: slot.startHour !== undefined ? slot.startHour : (slot.startTime ? parseInt(slot.startTime.split(':')[0]) : 0),
      endHour: slot.endHour !== undefined ? slot.endHour : (slot.endTime ? parseInt(slot.endTime.split(':')[0]) : 0),
      startTime: slot.startTime || `${slot.startHour || 0}:00`,
      endTime: slot.endTime || `${slot.endHour || 0}:59`,
      perMinute: slot.perMinute || slot.feePerMinute || 0,
      feePerMinute: slot.feePerMinute || slot.perMinute || 0
    }));

    // Sort by startHour
    const sortedSlots = [...normalizedSlots].sort((a, b) => a.startHour - b.startHour);

    const consolidated: any[] = [];
    let currentGroup = { ...sortedSlots[0] };

    for (let i = 1; i < sortedSlots.length; i++) {
      const current = sortedSlots[i];
      const prev = sortedSlots[i - 1];

      // Check if current slot has same fee and is consecutive
      const sameFee = Math.abs((current.perMinute || 0) - (prev.perMinute || 0)) < 0.001;
      const sameMaxFee = Math.abs((current.maxFee || 0) - (prev.maxFee || 0)) < 0.001;
      const sameGracePeriod = (current.gracePeriod || 0) === (prev.gracePeriod || 0);
      const isConsecutive = current.startHour === prev.endHour + 1;

      if (sameFee && sameMaxFee && sameGracePeriod && isConsecutive) {
        // Extend current group
        currentGroup.endHour = current.endHour;
        currentGroup.endTime = current.endTime;
      } else {
        // Start new group
        consolidated.push(currentGroup);
        currentGroup = { ...current };
      }
    }

    // Add the last group
    consolidated.push(currentGroup);

    return consolidated;
  };

  // Helper function to consolidate consecutive weekdays with same configuration
  const consolidateWeekdays = (dailySchedules: any[], isBlockingFee: boolean = false) => {
    if (!dailySchedules || dailySchedules.length === 0) return [];

    // Define weekday order for proper sorting and grouping
    const weekdayOrder = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
    const weekdayNames = {
      'monday': 'Montag',
      'tuesday': 'Dienstag',
      'wednesday': 'Mittwoch',
      'thursday': 'Donnerstag',
      'friday': 'Freitag',
      'saturday': 'Samstag',
      'sunday': 'Sonntag'
    };

    // Sort schedules by weekday order
    const sortedSchedules = [...dailySchedules].sort((a, b) =>
      weekdayOrder.indexOf(a.dayOfWeek) - weekdayOrder.indexOf(b.dayOfWeek)
    );

    // Helper function to compare if two daily schedules are identical
    const schedulesAreIdentical = (schedule1: any, schedule2: any) => {
      if (isBlockingFee) {
        // For blocking fees, the schedule itself contains the blocking fee data
        return (
          (schedule1.startHour || 0) === (schedule2.startHour || 0) &&
          (schedule1.endHour || 0) === (schedule2.endHour || 0) &&
          Math.abs((schedule1.perMinute || 0) - (schedule2.perMinute || 0)) < 0.001 &&
          Math.abs((schedule1.maxFee || 0) - (schedule2.maxFee || 0)) < 0.001 &&
          (schedule1.gracePeriod || 0) === (schedule2.gracePeriod || 0)
        );
      } else {
        // For pricing, compare hourly rates
        const rates1 = consolidateHourlyRates(schedule1.hourlyRate || []);
        const rates2 = consolidateHourlyRates(schedule2.hourlyRate || []);

        if (rates1.length !== rates2.length) return false;

        return rates1.every((rate1, index) => {
          const rate2 = rates2[index];
          return (
            rate1.hourFrom === rate2.hourFrom &&
            rate1.hourTo === rate2.hourTo &&
            Math.abs(getDisplayPrice(rate1) - getDisplayPrice(rate2)) < 0.001 &&
            rate1.priceType === rate2.priceType &&
            (rate1.marginValue || 0) === (rate2.marginValue || 0) &&
            (rate1.marginType || '') === (rate2.marginType || '') &&
            (rate1.taxesAndLevies || 0) === (rate2.taxesAndLevies || 0) &&
            (rate1.minPricePerKwh || 0) === (rate2.minPricePerKwh || 0) &&
            (rate1.maxPricePerKwh || 0) === (rate2.maxPricePerKwh || 0) &&
            (rate1.sessionFee || 0) === (rate2.sessionFee || 0) &&
            (rate1.fallbackPricePerKwh || 0) === (rate2.fallbackPricePerKwh || 0) &&
            (rate1.epexAveragingPeriodHours || 0) === (rate2.epexAveragingPeriodHours || 0)
          );
        });
      }
    };

    const consolidated: any[] = [];
    let currentGroup = {
      ...sortedSchedules[0],
      weekdays: [sortedSchedules[0].dayOfWeek]
    };

    for (let i = 1; i < sortedSchedules.length; i++) {
      const current = sortedSchedules[i];
      const prev = sortedSchedules[i - 1];

      // Check if current schedule is identical to previous and consecutive
      const isIdentical = schedulesAreIdentical(current, prev);
      const isConsecutive = weekdayOrder.indexOf(current.dayOfWeek) === weekdayOrder.indexOf(prev.dayOfWeek) + 1;

      if (isIdentical && isConsecutive) {
        // Extend current group
        currentGroup.weekdays.push(current.dayOfWeek);
      } else if (isIdentical && !isConsecutive) {
        // Same configuration but not consecutive - check if we can still group
        // For now, start a new group to keep it simple
        consolidated.push(currentGroup);
        currentGroup = {
          ...current,
          weekdays: [current.dayOfWeek]
        };
      } else {
        // Different configuration - start new group
        consolidated.push(currentGroup);
        currentGroup = {
          ...current,
          weekdays: [current.dayOfWeek]
        };
      }
    }

    // Add the last group
    consolidated.push(currentGroup);

    // Generate display names for weekday ranges and ensure data is preserved
    return consolidated.map(group => {
      const displayName = group.weekdays.length === 1
        ? weekdayNames[group.weekdays[0] as keyof typeof weekdayNames]
        : group.weekdays.length === 7
        ? 'Montag bis Sonntag'
        : group.weekdays.length === 5 &&
          group.weekdays.every((day: string) => ['monday', 'tuesday', 'wednesday', 'thursday', 'friday'].includes(day))
        ? 'Montag bis Freitag'
        : group.weekdays.length === 2 &&
          group.weekdays.every((day: string) => ['saturday', 'sunday'].includes(day))
        ? 'Samstag bis Sonntag'
        : `${weekdayNames[group.weekdays[0] as keyof typeof weekdayNames]} bis ${weekdayNames[group.weekdays[group.weekdays.length - 1] as keyof typeof weekdayNames]}`;

      // Ensure we preserve the original data structure
      return {
        ...group,
        displayName,
        // Preserve original fields for compatibility
        hourlyRate: group.hourlyRate || [],
        blockingFeeTimeSlots: group.blockingFeeTimeSlots || []
      };
    });
  };

  const renderPriceDetails = () => {
    const dataArray = mode === "price" ? summary.allPrices : summary.allBlockingFees;

    return dataArray.map((item: any, index: number) => {
      // Use the new utility functions for proper validity period handling
      const status = calculateValidityStatus(item.validFrom, item.validTo);
      const { isCurrent, isFuture, isPast } = status;

      return (
        <Card key={index} className={`${isCurrent ? 'border-green-500 bg-green-50' : isFuture ? 'border-blue-500 bg-blue-50' : 'border-gray-300 bg-gray-50'}`}>
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg">
                {mode === "price" ? `Preiskonfiguration ${index + 1}` : `Blockiergebühr ${index + 1}`}
              </CardTitle>
              <div className={`px-3 py-1 rounded-full text-xs font-medium ${
                isCurrent ? 'bg-green-100 text-green-800' :
                isFuture ? 'bg-blue-100 text-blue-800' :
                'bg-gray-100 text-gray-800'
              }`}>
                {getValidityStatusText(status)}
              </div>
            </div>
            <div className="flex items-center text-sm text-gray-600">
              <Calendar className="h-4 w-4 mr-2" />
              {formatValidityPeriod(item.validFrom, item.validTo)}
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                {mode === "price" ? (
                  <>
                    <h4 className="font-medium mb-2">Tagespläne ({consolidateWeekdays(item.dailySchedules || []).length})</h4>
                    {consolidateWeekdays(item.dailySchedules || []).map((schedule: any, scheduleIndex: number) => (
                      <div key={scheduleIndex} className="mb-3 p-3 bg-white rounded border">
                        <div className="font-medium text-sm mb-2 capitalize">
                          {schedule.displayName}
                        </div>
                        <div className="space-y-2">
                          {consolidateHourlyRates(schedule.hourlyRate || []).map((rate: any, rateIndex: number) => (
                            <div key={rateIndex} className="border rounded-lg p-3 bg-gray-50">
                              <div className="flex items-center justify-between mb-2">
                                <div className="flex items-center font-medium text-sm">
                                  <Clock className="h-4 w-4 mr-2 text-blue-600" />
                                  {rate.hourFrom === rate.hourTo ?
                                    `${rate.hourFrom}:00 - ${rate.hourFrom}:59` :
                                    `${rate.hourFrom}:00 - ${rate.hourTo}:59`
                                  }
                                </div>
                                <div className="text-sm font-bold text-green-600">
                                  {rate.priceType === 'EPEX' ?
                                    `${(rate.fallbackPricePerKwh || 0).toFixed(2)} €/kWh` :
                                    `${(rate.pricePerKwh || 0).toFixed(2)} €/kWh`
                                  }
                                </div>
                              </div>

                              {/* Detailed breakdown */}
                              <div className="space-y-1 text-xs text-gray-600">
                                {rate.priceType === 'EPEX' && (
                                  <>
                                    <div className="flex justify-between">
                                      <span>Preistyp:</span>
                                      <span className="font-medium">EPEX Spot</span>
                                    </div>
                                    <div className="flex justify-between">
                                      <span>Fallback-Preis:</span>
                                      <span>{(rate.fallbackPricePerKwh || 0).toFixed(3)} €/kWh</span>
                                    </div>
                                    {rate.marginValue && rate.marginValue > 0 && (
                                      <div className="flex justify-between">
                                        <span>Margin:</span>
                                        <span>
                                          {rate.marginType === 'percentage'
                                            ? `+${rate.marginValue}%`
                                            : `+${rate.marginValue.toFixed(3)} €/kWh`
                                          }
                                        </span>
                                      </div>
                                    )}
                                    {rate.taxesAndLevies && rate.taxesAndLevies > 0 && (
                                      <div className="flex justify-between">
                                        <span>Steuern & Abgaben:</span>
                                        <span>
                                          {(rate.taxesAndLevies > 10 ? rate.taxesAndLevies / 100 : rate.taxesAndLevies).toFixed(3)} €/kWh
                                        </span>
                                      </div>
                                    )}
                                    {(rate.minPricePerKwh > 0 || rate.maxPricePerKwh > 0) && (
                                      <div className="flex justify-between">
                                        <span>Preislimits:</span>
                                        <span>
                                          {rate.minPricePerKwh > 0 && `Min: ${(rate.minPricePerKwh > 10 ? rate.minPricePerKwh / 100 : rate.minPricePerKwh).toFixed(2)} €/kWh`}
                                          {rate.minPricePerKwh > 0 && rate.maxPricePerKwh > 0 && ' | '}
                                          {rate.maxPricePerKwh > 0 && `Max: ${(rate.maxPricePerKwh > 10 ? rate.maxPricePerKwh / 100 : rate.maxPricePerKwh).toFixed(2)} €/kWh`}
                                        </span>
                                      </div>
                                    )}
                                  </>
                                )}

                                {rate.priceType === 'FIXED' && (
                                  <div className="flex justify-between">
                                    <span>Preistyp:</span>
                                    <span className="font-medium">Festpreis</span>
                                  </div>
                                )}

                                {rate.sessionFee && rate.sessionFee > 0 && (
                                  <div className="flex justify-between border-t pt-1 mt-1">
                                    <span className="font-medium">Session Fee:</span>
                                    <span className="font-medium text-orange-600">{rate.sessionFee.toFixed(2)} €</span>
                                  </div>
                                )}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    ))}
                  </>
                ) : (
                  <>
                    <h4 className="font-medium mb-2">Zeitpläne ({consolidateWeekdays(item.blockFeeSchedule || [], true).length})</h4>
                    {consolidateWeekdays(item.blockFeeSchedule || [], true).map((schedule: any, scheduleIndex: number) => (
                      <div key={scheduleIndex} className="mb-3 p-3 bg-white rounded border">
                        <div className="font-medium text-sm mb-2 capitalize">
                          {schedule.displayName ||
                           (schedule.dayOfWeek === 'monday' ? 'Montag' :
                            schedule.dayOfWeek === 'tuesday' ? 'Dienstag' :
                            schedule.dayOfWeek === 'wednesday' ? 'Mittwoch' :
                            schedule.dayOfWeek === 'thursday' ? 'Donnerstag' :
                            schedule.dayOfWeek === 'friday' ? 'Freitag' :
                            schedule.dayOfWeek === 'saturday' ? 'Samstag' :
                            schedule.dayOfWeek === 'sunday' ? 'Sonntag' : schedule.dayOfWeek)}
                        </div>
                        <div className="space-y-2">
                          {/* The schedule itself IS the time slot */}
                          <div className="border rounded-lg p-3 bg-gray-50">
                            <div className="flex items-center justify-between mb-2">
                              <div className="flex items-center font-medium text-sm">
                                <Clock className="h-4 w-4 mr-2 text-blue-600" />
                                {schedule.startHour !== undefined && schedule.endHour !== undefined ?
                                  `${schedule.startHour}:00 - ${schedule.endHour}:59` :
                                  'Ganztägig'
                                }
                              </div>
                              <div className="text-sm font-bold text-orange-600">
                                {(schedule.perMinute || 0).toFixed(3)} €/min
                              </div>
                            </div>

                            {/* Detailed breakdown */}
                            <div className="space-y-1 text-xs text-gray-600">
                              <div className="flex justify-between">
                                <span>Gebühr pro Minute:</span>
                                <span>{(schedule.perMinute || 0).toFixed(3)} €/min</span>
                              </div>
                              {(schedule.maxFee || 0) > 0 && (
                                <div className="flex justify-between">
                                  <span>Maximale Gebühr:</span>
                                  <span>{(schedule.maxFee || 0).toFixed(2)} €</span>
                                </div>
                              )}
                              {(schedule.gracePeriod || 0) > 0 && (
                                <div className="flex justify-between">
                                  <span>Kulanzzeit:</span>
                                  <span>{schedule.gracePeriod || 0} Minuten</span>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </>
                )}
              </div>
              <div>
                <h4 className="font-medium mb-2">Zusätzliche Informationen</h4>
                <div className="space-y-2 text-sm">
                  <div>
                    <span className="text-gray-600">Erstellt:</span> {formatDate(item.changeDate || item.createdAt)}
                  </div>
                  {item.users_permissions_user && (
                    <div>
                      <span className="text-gray-600">Benutzer:</span> {item.users_permissions_user}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      );
    });
  };

  return (
    <Dialog>
      <DialogTrigger asChild>
        {children || (
          <Button variant="ghost" size="sm" className="mt-2 w-full">
            <Eye className="h-4 w-4 mr-2" />
            Details anzeigen
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{title} - Detailansicht</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          {renderPriceDetails()}
        </div>
      </DialogContent>
    </Dialog>
  );
}
