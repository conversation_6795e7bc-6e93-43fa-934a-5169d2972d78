"use client";

import * as React from "react";
import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "~/components/ui/card";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Tabs, Tabs<PERSON>ist, TabsTrigger, TabsContent } from "~/components/ui/tabs-with-icons";
import { Plus, Trash2, Search, Building, MapPin, Smartphone, Zap } from "lucide-react";
import { useMandant } from "~/components/MandantContext";
import { assignmentApiStrapi } from "~/services/assignment-api-strapi";
import { getEVSEDisplayIdentifier } from "~/utils/evseDisplayUtils";

// Interface for the tariff detail data
interface TariffDetail {
  id: string;
  documentId: string;
  name: string;
  priceAC: any[];
  blockFeeAC: any[];
  priceDC: any[];
  blockFeeDC: any[];
  mandants: any[];
  terminals: any[];
  ocpi_evses: any[];
  ocpi_locations: any[];
  createdAt: string;
  updatedAt: string;
}

interface TariffAssignmentManagerProps {
  tariff: TariffDetail;
  activeAssignmentTab?: string;
  setActiveAssignmentTab?: (tab: string) => void;
  onTariffUpdate?: (tariff: TariffDetail) => void;
}

export default function TariffAssignmentManager({
  tariff,
  activeAssignmentTab: externalActiveTab,
  setActiveAssignmentTab: externalSetActiveTab,
  onTariffUpdate
}: TariffAssignmentManagerProps) {
  const { activeMandant } = useMandant();
  const [searchTerm, setSearchTerm] = useState("");
  const [internalActiveTab, setInternalActiveTab] = useState("mandants");

  // Use external state if provided, otherwise use internal state
  const activeAssignmentTab = externalActiveTab ?? internalActiveTab;
  const setActiveAssignmentTab = externalSetActiveTab ?? setInternalActiveTab;

  // State for current tariff assignments (local state to avoid page reloads)
  const [currentTariff, setCurrentTariff] = useState<TariffDetail>(tariff);

  // State for available entities to assign
  const [availableMandants, setAvailableMandants] = useState<any[]>([]);
  const [availableLocations, setAvailableLocations] = useState<any[]>([]);
  const [availableTerminals, setAvailableTerminals] = useState<any[]>([]);
  const [availableEvses, setAvailableEvses] = useState<any[]>([]);

  // Loading states
  const [loading, setLoading] = useState(false);
  const [assignmentLoading, setAssignmentLoading] = useState(false);

  // Load available entities based on active tab
  useEffect(() => {
    loadAvailableEntities();
  }, [activeAssignmentTab, activeMandant, currentTariff]);

  // Update local tariff state when prop changes
  useEffect(() => {
    setCurrentTariff(tariff);
  }, [tariff]);

  const loadAvailableEntities = async () => {
    setLoading(true);
    try {
      switch (activeAssignmentTab) {
        case "mandants":
          const mandantsResponse = await assignmentApiStrapi.getAvailableMandants(
            currentTariff.documentId
          );
          setAvailableMandants(mandantsResponse.data || []);
          break;
        case "locations":
          const locationsResponse = await assignmentApiStrapi.getAvailableLocations(
            activeMandant?.documentId,
            currentTariff.documentId
          );
          setAvailableLocations(locationsResponse.data || []);
          break;
        case "terminals":
          const terminalsResponse = await assignmentApiStrapi.getAvailableTerminals(
            activeMandant?.documentId,
            currentTariff.documentId
          );
          setAvailableTerminals(terminalsResponse.data || []);
          break;
        case "evses":
          const evsesResponse = await assignmentApiStrapi.getAvailableEvses(
            activeMandant?.documentId,
            currentTariff.documentId
          );
          setAvailableEvses(evsesResponse.data || []);
          break;
      }
    } catch (error) {
      console.error("Error loading available entities:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleAssign = async (entityId: string, entityType: string) => {
    setAssignmentLoading(true);
    try {
      // Find the entity to assign from available entities
      let entityToAssign: any = null;
      switch (entityType) {
        case "mandant":
          entityToAssign = availableMandants.find(m => (m.documentId || m.id) === entityId);
          await assignmentApiStrapi.assignToMandant(currentTariff.documentId, entityId);
          break;
        case "location":
          entityToAssign = availableLocations.find(l => (l.documentId || l.id) === entityId);
          await assignmentApiStrapi.assignToLocation(currentTariff.documentId, entityId);
          break;
        case "terminal":
          entityToAssign = availableTerminals.find(t => (t.documentId || t.id) === entityId);
          await assignmentApiStrapi.assignToTerminal(currentTariff.documentId, entityId);
          break;
        case "evse":
          entityToAssign = availableEvses.find(e => (e.documentId || e.id) === entityId);
          await assignmentApiStrapi.assignToEvse(currentTariff.documentId, entityId);
          break;
      }

      if (entityToAssign) {
        // Optimistically update local state
        const updatedTariff = { ...currentTariff };
        switch (entityType) {
          case "mandant":
            // Check if entity already exists to prevent duplicates
            const mandantExists = updatedTariff.mandants.some(m => (m.documentId || m.id) === entityId);
            if (!mandantExists) {
              updatedTariff.mandants = [...updatedTariff.mandants, entityToAssign];
            }
            setAvailableMandants(prev => prev.filter(m => (m.documentId || m.id) !== entityId));
            break;
          case "location":
            const locationExists = updatedTariff.ocpi_locations.some(l => (l.documentId || l.id) === entityId);
            if (!locationExists) {
              updatedTariff.ocpi_locations = [...updatedTariff.ocpi_locations, entityToAssign];
            }
            setAvailableLocations(prev => prev.filter(l => (l.documentId || l.id) !== entityId));
            break;
          case "terminal":
            const terminalExists = updatedTariff.terminals.some(t => (t.documentId || t.id) === entityId);
            if (!terminalExists) {
              updatedTariff.terminals = [...updatedTariff.terminals, entityToAssign];
            }
            setAvailableTerminals(prev => prev.filter(t => (t.documentId || t.id) !== entityId));
            break;
          case "evse":
            const evseExists = updatedTariff.ocpi_evses.some(e => (e.documentId || e.id) === entityId);
            if (!evseExists) {
              updatedTariff.ocpi_evses = [...updatedTariff.ocpi_evses, entityToAssign];
            }
            setAvailableEvses(prev => prev.filter(e => (e.documentId || e.id) !== entityId));
            break;
        }
        setCurrentTariff(updatedTariff);
        onTariffUpdate?.(updatedTariff);
      }
    } catch (error) {
      console.error("Error assigning entity:", error);
      alert("Fehler beim Zuordnen. Bitte versuchen Sie es erneut.");
      // Reload available entities to ensure consistency
      loadAvailableEntities();
    } finally {
      setAssignmentLoading(false);
    }
  };

  const handleUnassign = async (entityId: string, entityType: string) => {
    setAssignmentLoading(true);
    try {
      // Find the entity to unassign from current assignments
      let entityToUnassign: any = null;
      switch (entityType) {
        case "mandant":
          entityToUnassign = currentTariff.mandants.find(m => (m.documentId || m.id) === entityId);
          await assignmentApiStrapi.unassignFromMandant(currentTariff.documentId, entityId);
          break;
        case "location":
          entityToUnassign = currentTariff.ocpi_locations.find(l => (l.documentId || l.id) === entityId);
          await assignmentApiStrapi.unassignFromLocation(currentTariff.documentId, entityId);
          break;
        case "terminal":
          entityToUnassign = currentTariff.terminals.find(t => (t.documentId || t.id) === entityId);
          await assignmentApiStrapi.unassignFromTerminal(currentTariff.documentId, entityId);
          break;
        case "evse":
          entityToUnassign = currentTariff.ocpi_evses.find(e => (e.documentId || e.id) === entityId);
          await assignmentApiStrapi.unassignFromEvse(currentTariff.documentId, entityId);
          break;
      }

      if (entityToUnassign) {
        // Optimistically update local state
        const updatedTariff = { ...currentTariff };
        switch (entityType) {
          case "mandant":
            updatedTariff.mandants = updatedTariff.mandants.filter(m => (m.documentId || m.id) !== entityId);
            setAvailableMandants(prev => {
              // Check if entity already exists to prevent duplicates
              const exists = prev.some(m => (m.documentId || m.id) === entityId);
              if (exists) return prev;
              return [...prev, entityToUnassign].sort((a, b) => (a.name || '').localeCompare(b.name || ''));
            });
            break;
          case "location":
            updatedTariff.ocpi_locations = updatedTariff.ocpi_locations.filter(l => (l.documentId || l.id) !== entityId);
            setAvailableLocations(prev => {
              const exists = prev.some(l => (l.documentId || l.id) === entityId);
              if (exists) return prev;
              return [...prev, entityToUnassign].sort((a, b) => (a.name || '').localeCompare(b.name || ''));
            });
            break;
          case "terminal":
            updatedTariff.terminals = updatedTariff.terminals.filter(t => (t.documentId || t.id) !== entityId);
            setAvailableTerminals(prev => {
              const exists = prev.some(t => (t.documentId || t.id) === entityId);
              if (exists) return prev;
              return [...prev, entityToUnassign].sort((a, b) => (a.terminalName || '').localeCompare(b.terminalName || ''));
            });
            break;
          case "evse":
            updatedTariff.ocpi_evses = updatedTariff.ocpi_evses.filter(e => (e.documentId || e.id) !== entityId);
            setAvailableEvses(prev => {
              const exists = prev.some(e => (e.documentId || e.id) === entityId);
              if (exists) return prev;
              return [...prev, entityToUnassign].sort((a, b) => (a.uid || '').localeCompare(b.uid || ''));
            });
            break;
        }
        setCurrentTariff(updatedTariff);
        onTariffUpdate?.(updatedTariff);
      }
    } catch (error) {
      console.error("Error unassigning entity:", error);
      alert("Fehler beim Entfernen der Zuordnung. Bitte versuchen Sie es erneut.");
      // Reload available entities to ensure consistency
      loadAvailableEntities();
    } finally {
      setAssignmentLoading(false);
    }
  };

  const filterEntities = (entities: any[], searchTerm: string) => {
    if (!searchTerm) return entities;
    return entities.filter(entity => {
      const searchLower = searchTerm.toLowerCase();

      // For EVSEs, also search in the hierarchical display identifier
      if (entity.evseId || entity.physicalReference) {
        const displayId = getEVSEDisplayIdentifier({
          physicalReference: entity.physicalReference,
          evseId: entity.evseId,
          uid: entity.uid
        });
        if (displayId.toLowerCase().includes(searchLower)) return true;
      }

      return (
        entity.name?.toLowerCase().includes(searchLower) ||
        entity.terminalName?.toLowerCase().includes(searchLower) ||
        entity.serialNumber?.toLowerCase().includes(searchLower) ||
        entity.uid?.toLowerCase().includes(searchLower) ||
        entity.evseId?.toLowerCase().includes(searchLower) ||
        entity.physicalReference?.toLowerCase().includes(searchLower) ||
        entity.labelForTerminal?.toLowerCase().includes(searchLower)
      );
    });
  };

  // Helper function to generate unique keys
  const getEntityKey = (entity: any, entityType: string, prefix: string = "") => {
    const id = entity.documentId || entity.id;
    return `${prefix}${entityType}-${id}`;
  };

  const renderAssignmentSection = (
    title: string,
    icon: React.ReactNode,
    currentAssignments: any[],
    availableEntities: any[],
    entityType: string,
    nameField: string = "name"
  ) => (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          {icon}
          <h3 className="text-lg font-semibold ml-2">{title}</h3>
        </div>
        <div className="text-sm text-gray-600">
          {currentAssignments.length} zugeordnet
        </div>
      </div>

      {/* Current Assignments */}
      {currentAssignments.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-sm">Aktuelle Zuordnungen</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {currentAssignments.map((entity) => (
                <div key={getEntityKey(entity, entityType, "assigned-")} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                  <div>
                    <div className="font-medium">
                      {entityType === "evse" ?
                        getEVSEDisplayIdentifier({
                          physicalReference: entity.physicalReference,
                          evseId: entity.evseId,
                          uid: entity.uid
                        }) :
                        entity[nameField]
                      }
                    </div>
                    {entity.serialNumber && (
                      <div className="text-xs text-gray-600">SN: {entity.serialNumber}</div>
                    )}
                    {entityType === "evse" && entity.labelForTerminal && (
                      <div className="text-xs text-gray-600">Label: {entity.labelForTerminal}</div>
                    )}
                    {entityType !== "evse" && entity.uid && (
                      <div className="text-xs text-gray-600">UID: {entity.uid}</div>
                    )}
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleUnassign(entity.documentId || entity.id, entityType)}
                    disabled={assignmentLoading}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Search and Add New Assignments */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Neue Zuordnung hinzufügen</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                placeholder={`${title} suchen...`}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>

            {loading ? (
              <div className="text-center py-4">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mx-auto"></div>
              </div>
            ) : (
              <div className="max-h-60 overflow-y-auto space-y-2">
                {assignmentLoading && (
                  <div className="text-center py-2">
                    <div className="text-sm text-gray-600">Zuordnung wird verarbeitet...</div>
                  </div>
                )}
                {filterEntities(availableEntities, searchTerm).map((entity) => (
                  <div key={getEntityKey(entity, entityType, "available-")} className="flex items-center justify-between p-2 border rounded">
                    <div>
                      <div className="font-medium">
                        {entityType === "evse" ?
                          getEVSEDisplayIdentifier({
                            physicalReference: entity.physicalReference,
                            evseId: entity.evseId,
                            uid: entity.uid
                          }) :
                          entity[nameField]
                        }
                      </div>
                      {entity.serialNumber && (
                        <div className="text-xs text-gray-600">SN: {entity.serialNumber}</div>
                      )}
                      {entityType === "evse" && entity.labelForTerminal && (
                        <div className="text-xs text-gray-600">Label: {entity.labelForTerminal}</div>
                      )}
                      {entityType !== "evse" && entity.uid && (
                        <div className="text-xs text-gray-600">UID: {entity.uid}</div>
                      )}
                    </div>
                    <Button
                      size="sm"
                      onClick={() => handleAssign(entity.documentId || entity.id, entityType)}
                      className="btn-primary"
                      disabled={assignmentLoading}
                    >
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
                {filterEntities(availableEntities, searchTerm).length === 0 && (
                  <div className="text-center py-4 text-gray-500">
                    Keine verfügbaren {title.toLowerCase()} gefunden
                  </div>
                )}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Tarif-Zuordnungen verwalten</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-600 mb-4">
            Ordnen Sie diesen Tarif verschiedenen Entitäten zu. Beachten Sie die Hierarchie:
            Ladepunkt → Terminal → Standort → Mandant.
          </p>

          <Tabs value={activeAssignmentTab} onValueChange={setActiveAssignmentTab}>
            <TabsList>
              <TabsTrigger value="mandants" icon={<Building />}>Mandanten</TabsTrigger>
              <TabsTrigger value="locations" icon={<MapPin />}>Standorte</TabsTrigger>
              <TabsTrigger value="terminals" icon={<Smartphone />}>Terminals</TabsTrigger>
              <TabsTrigger value="evses" icon={<Zap />}>Ladepunkte</TabsTrigger>
            </TabsList>

            <TabsContent value="mandants" className="mt-6">
              {renderAssignmentSection(
                "Mandanten",
                <Building className="h-5 w-5 text-blue-600" />,
                currentTariff.mandants,
                availableMandants,
                "mandant"
              )}
            </TabsContent>

            <TabsContent value="locations" className="mt-6">
              {renderAssignmentSection(
                "Standorte",
                <MapPin className="h-5 w-5 text-green-600" />,
                currentTariff.ocpi_locations,
                availableLocations,
                "location"
              )}
            </TabsContent>

            <TabsContent value="terminals" className="mt-6">
              {renderAssignmentSection(
                "Terminals",
                <Smartphone className="h-5 w-5 text-yellow-600" />,
                currentTariff.terminals,
                availableTerminals,
                "terminal",
                "terminalName"
              )}
            </TabsContent>

            <TabsContent value="evses" className="mt-6">
              {renderAssignmentSection(
                "Ladepunkte",
                <Zap className="h-5 w-5 text-purple-600" />,
                currentTariff.ocpi_evses,
                availableEvses,
                "evse",
                "uid"
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
