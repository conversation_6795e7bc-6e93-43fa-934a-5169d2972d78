# Migration von Axios zum Strapi Client

Diese Anleitung beschreibt, wie Sie von der aktuellen Axios-basierten API-Implementierung zum offiziellen Strapi Client migrieren können.

## Vorteile des Strapi Clients

- **Vereinfachte API-Interaktion**: Spezialisierte Methoden für Collection-Types, Single-Types und Dateien
- **Bessere Typisierung**: Vollständige TypeScript-Unterstützung
- **Konsistente Fehlerbehandlung**: Einheitliche Fehlerbehandlung für alle API-Anfragen
- **Offiziell unterstützt**: Bleibt mit zukünftigen Strapi-Versionen kompatibel

## Schrittweise Migration

### 1. Installation des Strapi Clients

```bash
npm install @strapi/client
```

### 2. Zentrale Client-Konfiguration

Die Datei `src/services/strapi-client.ts` enthält die zentrale Konfiguration für den Strapi Client:

```typescript
import { strapi } from '@strapi/client';

// Erstellt einen Strapi-Client mit optionalem Auth-Token
export const createStrapiClient = (token?: string) => {
  return strapi({
    baseURL: process.env.NEXT_PUBLIC_STRAPI_API_URL || "http://localhost:1337/api",
    auth: token || undefined,
  });
};

// Gibt einen authentifizierten Client zurück (client-seitig)
export const getAuthenticatedClient = () => {
  const token = typeof window !== 'undefined' ? localStorage.getItem("strapiToken") : null;
  return createStrapiClient(token || undefined);
};

// Gibt einen authentifizierten Client mit einem bestimmten Token zurück
export const getClientWithToken = (token: string) => {
  return createStrapiClient(token);
};

// Standard-Client für nicht-authentifizierte Anfragen
export const publicClient = createStrapiClient();
```

### 3. Migration von API-Services

#### Vorher (mit Axios):

```typescript
export const evsesApi = {
  getAll: async (): Promise<EvseResponse> => {
    try {
      const response = await apiClient.get("/api/evses?populate=*");
      return response.data;
    } catch (error) {
      console.error("Error fetching EVSEs:", error);
      throw error;
    }
  },
  
  getById: async (id: number): Promise<EvseData> => {
    try {
      const response = await apiClient.get(`/api/evses/${id}?populate=*`);
      return response.data.data;
    } catch (error) {
      console.error(`Error fetching EVSE with id ${id}:`, error);
      throw error;
    }
  },
}
```

#### Nachher (mit Strapi Client):

```typescript
import { getAuthenticatedClient } from './strapi-client';

export const evsesApiStrapi = {
  getAll: async (): Promise<EvseResponse> => {
    try {
      const client = getAuthenticatedClient();
      const evses = client.collection('evses');
      return await evses.find({ populate: '*' });
    } catch (error) {
      console.error("Error fetching EVSEs:", error);
      throw error;
    }
  },
  
  getById: async (id: number): Promise<EvseData> => {
    try {
      const client = getAuthenticatedClient();
      const evses = client.collection('evses');
      const response = await evses.findOne(id, { populate: '*' });
      return response.data;
    } catch (error) {
      console.error(`Error fetching EVSE with id ${id}:`, error);
      throw error;
    }
  },
}
```

### 4. Verwendung von Single-Types

```typescript
import { getAuthenticatedClient } from './strapi-client';

export const settingsApiStrapi = {
  getSettings: async (): Promise<GlobalSettingsResponse> => {
    try {
      const client = getAuthenticatedClient();
      const settings = client.single('global-setting');
      return await settings.find();
    } catch (error) {
      console.error("Error fetching global settings:", error);
      throw error;
    }
  },
}
```

### 5. Arbeit mit Dateien

```typescript
import { getAuthenticatedClient } from './strapi-client';

export const mediaApiStrapi = {
  getAllFiles: async (params = {}): Promise<MediaFile[]> => {
    try {
      const client = getAuthenticatedClient();
      return await client.files.find(params);
    } catch (error) {
      console.error("Error fetching media files:", error);
      throw error;
    }
  },
}
```

### 6. Anpassung der Authentifizierung

Für die Authentifizierung (Login) verwenden wir weiterhin `fetch`, da der Strapi Client keine spezielle Methode für die Authentifizierung bietet:

```typescript
const login = async (identifier: string, password: string) => {
  try {
    const API_URL = process.env.NEXT_PUBLIC_STRAPI_API_URL || "http://localhost:1337";
    
    const response = await fetch(`${API_URL}/api/auth/local`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ identifier, password }),
    });
    
    const data = await response.json();
    
    if (data?.jwt) {
      localStorage.setItem("strapiToken", data.jwt);
      // ...
    }
  } catch (error) {
    // Fehlerbehandlung
  }
};
```

Für das Abrufen von Benutzerdaten verwenden wir den Strapi Client:

```typescript
const fetchUser = async (userToken: string) => {
  try {
    const client = getClientWithToken(userToken);
    const userData = await client.fetch('users/me?populate=role');
    
    if (userData) {
      setUser(userData);
      setToken(userToken);
    }
  } catch (error) {
    // Fehlerbehandlung
  }
};
```

## Empfohlene Migrationsstrategie

1. **Parallele Implementierung**: Erstellen Sie neue API-Services mit dem Strapi Client parallel zu den bestehenden Axios-Services.
2. **Schrittweise Umstellung**: Migrieren Sie einen Service nach dem anderen und testen Sie gründlich.
3. **Komponententests**: Testen Sie jede Komponente nach der Umstellung auf den neuen Service.
4. **Vollständige Migration**: Wenn alle Services migriert sind, entfernen Sie die alten Axios-basierten Services.

## Beispiel für die Verwendung in Komponenten

```tsx
import { evsesApiStrapi } from '~/services/evses-api-strapi';

export default function EVSEsList() {
  const [evses, setEvses] = useState([]);
  
  useEffect(() => {
    const loadEvses = async () => {
      try {
        const response = await evsesApiStrapi.getAll();
        setEvses(response.data);
      } catch (error) {
        console.error("Fehler beim Laden der EVSEs:", error);
      }
    };
    
    loadEvses();
  }, []);
  
  return (
    <div>
      {/* Komponente rendern */}
    </div>
  );
}
```
