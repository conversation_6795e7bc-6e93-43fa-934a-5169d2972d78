version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
      - "9000:9000"
    environment:
      - DATABASE_URL=**************************************/ocpp_load_manager
      - OCPP_WS_PORT=9000
      - NEXT_PUBLIC_API_URL=http://localhost:3000/api
      - NATS_URL=nats://nats:4222
    depends_on:
      - db
      - nats
    restart: unless-stopped

  db:
    image: mysql:8.0
    ports:
      - "3306:3306"
    environment:
      - MYSQL_ROOT_PASSWORD=root
      - MYSQL_USER=ocpp
      - MYSQL_PASSWORD=ocpp
      - MYSQL_DATABASE=ocpp_load_manager
    volumes:
      - mysql_data:/var/lib/mysql
    command: --default-authentication-plugin=mysql_native_password
    restart: unless-stopped

  nats:
    image: nats:latest
    ports:
      - "4222:4222"
      - "8222:8222"
    restart: unless-stopped

volumes:
  postgres_data:
